import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import i18n from './i18n'
import './style.css'

// 导入页面组件
import Home from './views/Home.vue'
import Login from './views/Login.vue'
import Register from './views/Register.vue'
import Dashboard from './views/Dashboard.vue'
import Modules from './views/Modules.vue'
import Lesson from './views/Lesson.vue'
import AdminLogin from './views/AdminLogin.vue'
import AdminDashboard from './views/AdminDashboard.vue'
import AdminModules from './views/AdminModules.vue'
import AdminLessons from './views/AdminLessons.vue'
import AdminExercises from './views/AdminExercises.vue'
import AdminUsers from './views/AdminUsers.vue'
import AdminAnalytics from './views/AdminAnalytics.vue'
import ResearchAnalytics from './views/ResearchAnalytics.vue'

// 路由配置
const routes = [
  { path: '/', component: Home, name: 'home' },
  { path: '/login', component: Login, name: 'login' },
  { path: '/register', component: Register, name: 'register' },
  { path: '/dashboard', component: Dashboard, name: 'dashboard', meta: { requiresAuth: true } },
  { path: '/modules', component: Modules, name: 'modules', meta: { requiresAuth: true } },
  { path: '/lesson/:id', component: Lesson, name: 'lesson', meta: { requiresAuth: true } },
  // 管理员路由
  { path: '/admin/login', component: AdminLogin, name: 'admin-login' },
  { path: '/admin/dashboard', component: AdminDashboard, name: 'admin-dashboard', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin/modules', component: AdminModules, name: 'admin-modules', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin/lessons', component: AdminLessons, name: 'admin-lessons', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin/exercises', component: AdminExercises, name: 'admin-exercises', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin/users', component: AdminUsers, name: 'admin-users', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin/analytics', component: AdminAnalytics, name: 'admin-analytics', meta: { requiresAuth: true, requiresAdmin: true } },
  // 🔬 NEW: 研究分析页面 (管理员权限)
  { path: '/research', component: ResearchAnalytics, name: 'research-analytics', meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/admin', redirect: '/admin/dashboard' }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 检查认证状态
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('authToken')
  const user = JSON.parse(localStorage.getItem('user') || '{}')
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !token) {
    if (to.path.startsWith('/admin')) {
      next('/admin/login')
    } else {
      next('/login')
    }
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && user.role !== 'admin') {
    next('/admin/login')
    return
  }
  
  // 已登录用户访问登录页面，重定向到对应的仪表板
  if ((to.name === 'login' || to.name === 'register') && token) {
    next('/dashboard')
  } else if (to.name === 'admin-login' && token && user.role === 'admin') {
    next('/admin/dashboard')
  } else {
    next()
  }
})

// 导入标题管理工具
import { updateTitleByRoute, setupTitleWatcher } from './utils/titleManager.js'

// 设置路由后钩子，用于更新页面标题
router.afterEach((to) => {
  updateTitleByRoute(to.name)
})

const app = createApp(App)
app.use(router)
app.use(i18n)

// 设置标题监听器
setupTitleWatcher()

app.mount('#app')