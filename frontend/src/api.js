import axios from 'axios'
import config from './config'

// 创建axios实例
const api = axios.create({
  baseURL: config.API_BASE_URL,
  timeout: config.TIMEOUT
})

// 请求拦截器 - 添加认证令牌和语言偏好
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加语言偏好头
    const preferredLanguage = localStorage.getItem('preferred-language') || 'en'
    config.headers['Accept-Language'] = preferredLanguage
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理认证错误
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 用户注册
  register: async (userData) => {
    const response = await api.post('/auth/register', userData)
    return response.data
  },

  // 用户登录
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials)
    if (response.data.access_token) {
      localStorage.setItem('authToken', response.data.access_token)
      localStorage.setItem('user', JSON.stringify(response.data.user))
    }
    return response.data
  },

  // 获取用户资料
  getProfile: async () => {
    const response = await api.get('/auth/profile')
    return response.data
  },

  // 验证令牌
  verifyToken: async () => {
    const response = await api.get('/auth/verify')
    return response.data
  },

  // 用户注销
  logout: () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    localStorage.removeItem('userData')
    window.location.href = '/login'
  }
}

// 内容管理API
export const contentAPI = {
  // 获取所有模块
  getModules: async () => {
    const response = await api.get('/content/modules')
    return response.data
  },

  // 获取课程详情
  getLesson: async (lessonId) => {
    const response = await api.get(`/content/lessons/${lessonId}`)
    return response.data
  },

  // 获取练习提示
  getExerciseHints: async (exerciseId) => {
    const response = await api.get(`/content/exercises/${exerciseId}/hints`)
    return response.data
  }
}

// 代码提交API
export const submissionAPI = {
  // 提交代码
  submitCode: async (submissionData) => {
    const response = await api.post('/submissions/submit', submissionData)
    return response.data
  },

  // 请求提示
  requestHint: async (exerciseId, hintLevel = 1) => {
    const response = await api.post(`/submissions/exercises/${exerciseId}/hint`, {
      hint_level: hintLevel
    })
    return response.data
  },

  // 获取提交历史
  getSubmissionHistory: async (exerciseId) => {
    const response = await api.get(`/submissions/exercises/${exerciseId}/history`)
    return response.data
  },


  // 🔥 NEW: 为反馈质量评分
  rateFeedback: async (feedbackId, helpfulnessRating, clarityRating) => {
    const response = await api.post('/submissions/feedback-rating', {
      feedback_id: feedbackId,
      helpfulness_rating: helpfulnessRating,
      clarity_rating: clarityRating
    })
    return response.data
  },

  // 🔥 NEW: 按需请求额外反馈
  requestAdditionalFeedback: async (submissionId, model) => {
    const response = await api.post(`/submissions/${submissionId}/request_additional_feedback`, {
      model: model
    });
    return response.data;
  }
}

// 学习进度API
export const progressAPI = {
  // 获取仪表板数据
  getDashboard: async () => {
    const response = await api.get('/progress/dashboard')
    return response.data
  },

  // 获取学习统计
  getStats: async () => {
    const response = await api.get('/progress/stats')
    return response.data
  },

  // 重置课程进度
  resetLessonProgress: async (lessonId) => {
    const response = await api.post(`/progress/reset/${lessonId}`)
    return response.data
  }
}

// 学习分析API
export const analyticsAPI = {
  // 获取用户代码分析统计
  getUserCodeAnalysis: async () => {
    const response = await api.get('/analytics/user/code_analysis')
    return response.data
  },

  // 获取特定练习的分析数据
  getExerciseAnalysis: async (exerciseId) => {
    const response = await api.get(`/analytics/exercise/${exerciseId}/analysis`)
    return response.data
  },

  // 获取平台分析数据（管理员）
  getPlatformAnalysis: async () => {
    const response = await api.get('/analytics/admin/platform_analysis')
    return response.data
  },

  // 🔬 NEW: 获取反馈效果研究分析数据（论文核心数据）
  getFeedbackEffectivenessAnalysis: async () => {
    const response = await api.get('/analytics/research/feedback_effectiveness')
    return response.data
  }
}

export default api