<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <div class="header-content">
        <h1>📊 {{ $t('admin.dashboard.title') }}</h1>
        <p>{{ $t('admin.dashboard.subtitle') }}</p>
      </div>
      <div class="header-actions">
        <button @click="handleLogout" class="btn btn-logout">
          🚪 {{ $t('dashboard.logout') }}
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.users?.total || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalUsers') }}</p>
          <span class="stat-detail">{{ $t('admin.dashboard.monthlyNew') }}: {{ dashboardData?.users?.active_monthly || 0 }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.content?.lessons || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalLessons') }}</p>
          <span class="stat-detail">{{ dashboardData?.content?.modules || 0 }} {{ $t('admin.dashboard.modules') }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">💻</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.content?.exercises || 0 }}</h3>
          <p>{{ $t('admin.dashboard.stats.totalExercises') }}</p>
          <span class="stat-detail">{{ $t('admin.dashboard.codeExercises') }}</span>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.activity?.success_rate || 0 }}%</h3>
          <p>{{ $t('admin.dashboard.stats.successRate') }}</p>
          <span class="stat-detail">{{ dashboardData?.activity?.total_submissions || 0 }} {{ $t('admin.dashboard.submissions') }}</span>
        </div>
      </div>
    </div>

    <!-- 主要操作 -->
    <div class="main-actions">
      <div class="action-card">
        <h3>📝 {{ $t('admin.dashboard.contentManagement') }}</h3>
        <p>{{ $t('admin.dashboard.contentManagementDesc') }}</p>
        <div class="action-buttons">
          <router-link to="/admin/modules" class="btn btn-primary">
            {{ $t('admin.modules.title') }}
          </router-link>
          <router-link to="/admin/lessons" class="btn btn-secondary">
            {{ $t('admin.lessons.title') }}
          </router-link>
          <router-link to="/admin/exercises" class="btn btn-secondary">
            {{ $t('admin.exercises.title') }}
          </router-link>
        </div>
      </div>

      <div class="action-card">
        <h3>👥 {{ $t('admin.dashboard.userManagement') }}</h3>
        <p>{{ $t('admin.dashboard.userManagementDesc') }}</p>
        <div class="action-buttons">
          <router-link to="/admin/users" class="btn btn-primary">
            {{ $t('admin.users.title') }}
          </router-link>
          <router-link to="/admin/analytics" class="btn btn-secondary">
            {{ $t('admin.dashboard.learningAnalytics') }}
          </router-link>
          <!-- 🔬 NEW: 研究分析链接 -->
          <router-link to="/research" class="btn btn-secondary research-btn">
            🔬 {{ $t('navigation.research') }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2>🕒 {{ $t('admin.dashboard.recentActivity') }}</h2>
      
      <div class="activity-grid">
        <!-- 新注册用户 -->
        <div class="activity-section">
          <h3>{{ $t('admin.dashboard.recentUsers') }}</h3>
          <div class="activity-list">
            <div 
              v-for="user in dashboardData?.users?.recent || []" 
              :key="user.user_id"
              class="activity-item"
            >
              <div class="activity-icon">👤</div>
              <div class="activity-content">
                <div class="activity-title">{{ user.username }}</div>
                <div class="activity-time">{{ formatDate(user.created_at) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近提交 -->
        <div class="activity-section">
          <h3>{{ $t('admin.dashboard.recentSubmissions') }}</h3>
          <div class="activity-list">
            <div 
              v-for="submission in dashboardData?.activity?.recent_submissions || []" 
              :key="submission.submission_id"
              class="activity-item"
            >
              <div class="activity-icon" :class="submission.is_correct ? 'success' : 'error'">
                {{ submission.is_correct ? '✅' : '❌' }}
              </div>
              <div class="activity-content">
                <div class="activity-title">
                  {{ $t('admin.dashboard.userExercise', { userId: submission.user_id, exerciseId: submission.exercise_id }) }}
                </div>
                <div class="activity-time">{{ formatDate(submission.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'
import { authAPI } from '../api.js'

export default {
  name: 'AdminDashboard',
  setup() {
    const { t } = useI18n()
    const dashboardData = ref(null)
    const loading = ref(false)

    const loadDashboard = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const data = await adminAPI.getDashboard()
        dashboardData.value = data
      } catch (error) {
        console.error('加载仪表板失败:', error)
        window.showNotification(t('dashboard.loadDashboardError'), 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleString('zh-CN')
    }

    const handleLogout = () => {
      if (confirm(t('dashboard.confirmLogout'))) {
        authAPI.logout()
      }
    }

    onMounted(() => {
      loadDashboard()
    })

    return {
      dashboardData,
      loading,
      loadDashboard,
      formatDate,
      handleLogout,
      t
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  flex: 1;
}

.dashboard-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #7f8c8d;
  font-size: 18px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-logout {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.btn-logout:hover {
  background: #c0392b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.stat-content h3 {
  font-size: 2rem;
  margin: 0;
  color: #2c3e50;
}

.stat-content p {
  margin: 5px 0;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-detail {
  font-size: 12px;
  color: #95a5a6;
}

.main-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.action-card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.action-card p {
  color: #7f8c8d;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.recent-activity h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.activity-section h3 {
  color: #34495e;
  margin-bottom: 15px;
  font-size: 18px;
}

.activity-list {
  space-y: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  background: #e9ecef;
}

.activity-icon.success {
  background: #d4edda;
  color: #155724;
}

.activity-icon.error {
  background: #f8d7da;
  color: #721c24;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #7f8c8d;
}

/* 🔬 NEW: 研究分析按钮特殊样式 */
.research-btn {
  background: linear-gradient(45deg, #3498db, #9b59b6) !important;
  color: white !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.research-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.research-btn:hover::before {
  left: 100%;
}

.research-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}
</style>