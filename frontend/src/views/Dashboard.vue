<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <div class="header-content">
        <h1>📊 {{ $t('dashboard.title') }}</h1>
        <p>{{ $t('dashboard.welcome', { username: userData?.username }) }}</p>
      </div>
      <div class="header-actions">
        <button @click="handleLogout" class="btn btn-logout">
          🚪 {{ $t('dashboard.logout') }}
        </button>
      </div>
    </div>

    <!-- Learning Progress Overview -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.total_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.totalLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.completed_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.completedLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.in_progress_lessons || 0 }}</h3>
          <p>{{ $t('dashboard.stats.inProgressLessons') }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <h3>{{ dashboardData?.overview?.completion_rate || 0 }}%</h3>
          <p>{{ $t('dashboard.stats.completionRate') }}</p>
        </div>
      </div>
      
      <!-- 💡 NEW: Hint Usage Statistics -->
      <div class="stat-card hint-usage-card">
        <div class="stat-icon">💡</div>
        <div class="stat-content">
          <h3>{{ codeAnalysis?.overall_stats?.total_hints_used || 0 }}</h3>
          <p>{{ $t('dashboard.stats.hintsUsed') }}</p>
          <div v-if="codeAnalysis?.hint_analysis?.exercises_needing_hints" class="stat-subtitle">
            {{ $t('dashboard.stats.exercisesNeedingHints', { count: codeAnalysis.hint_analysis.exercises_needing_hints }) }}
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="card">
      <h2 class="card-title">{{ $t('dashboard.progress.title') }}</h2>
      <div class="progress-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${dashboardData?.overview?.completion_rate || 0}%` }"
          ></div>
        </div>
        <div class="progress-text">
          {{ $t('dashboard.progress.lessonsCompleted', { completed: dashboardData?.overview?.completed_lessons || 0, total: dashboardData?.overview?.total_lessons || 0 }) }}
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card" v-if="dashboardData?.recent_activity?.length">
      <h2 class="card-title">🕒 {{ $t('dashboard.recentActivity.title') }}</h2>
      <div class="activity-list">
        <div 
          v-for="activity in dashboardData.recent_activity" 
          :key="activity.submission_id"
          class="activity-item"
        >
          <div class="activity-icon" :class="activity.is_correct ? 'success' : 'error'">
            {{ activity.is_correct ? '✅' : '❌' }}
          </div>
          <div class="activity-content">
            <div class="activity-title">
              {{ activity.is_correct ? $t('dashboard.recentActivity.exerciseCompleted') : $t('dashboard.recentActivity.exerciseAttempt') }}
            </div>
            <div class="activity-time">
              {{ formatDate(activity.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 💡 NEW: Hint Usage Analysis -->
    <div class="card" v-if="codeAnalysis?.hint_analysis && codeAnalysis.hint_analysis.total_hints_used > 0">
      <h2 class="card-title">💡 {{ $t('dashboard.hintAnalysis.title') }}</h2>
      <div class="hint-analysis-grid">
        <div class="analysis-card">
          <div class="analysis-icon">🎯</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.hint_analysis.total_hints_used }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.totalHints') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon">🧩</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.hint_analysis.exercises_needing_hints }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.exercisesWithHints') }}</p>
          </div>
        </div>
        
        <div class="analysis-card" v-if="getMostUsedHintLevel()">
          <div class="analysis-icon">📊</div>
          <div class="analysis-content">
            <h3>{{ $t('dashboard.hintAnalysis.level') }} {{ getMostUsedHintLevel() }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.mostUsedLevel') }}</p>
          </div>
        </div>
        
        <div class="analysis-card" v-if="getRecentHintTrend() !== null">
          <div class="analysis-icon">📈</div>
          <div class="analysis-content">
            <h3>{{ getRecentHintTrend() }}</h3>
            <p>{{ $t('dashboard.hintAnalysis.recentTrend') }}</p>
            <div class="trend-indicator" :class="getRecentHintTrend() > 0 ? 'hint-increase' : 'hint-decrease'">
              {{ getRecentHintTrend() > 0 ? '↗' : '↘' }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Hint Level Distribution -->
      <div v-if="codeAnalysis.hint_analysis.hints_by_level && Object.keys(codeAnalysis.hint_analysis.hints_by_level).length > 0" class="hint-level-distribution">
        <h3>{{ $t('dashboard.hintAnalysis.levelDistribution') }}</h3>
        <div class="hint-level-chart">
          <div 
            v-for="(count, level) in codeAnalysis.hint_analysis.hints_by_level" 
            :key="level"
            class="hint-level-bar"
          >
            <span class="level-label">{{ $t('dashboard.hintAnalysis.level') }} {{ level }}</span>
            <div class="level-bar-container">
              <div class="level-bar-fill" :style="{ width: `${(count / maxHintCount) * 100}%` }"></div>
              <span class="level-count">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Code Quality Analysis -->
    <div class="card" v-if="codeAnalysis">
      <h2 class="card-title">📊 {{ $t('dashboard.codeQuality.title') }}</h2>
      <div class="code-analysis-grid">
        <div class="analysis-card">
          <div class="analysis-icon">📏</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_code_lines || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.averageLines') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon">🧠</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_complexity || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.complexity') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon">✨</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.overall_stats?.avg_syntax_score || 0 }}</h3>
            <p>{{ $t('dashboard.codeQuality.syntaxScore') }}</p>
          </div>
        </div>
        
        <div class="analysis-card">
          <div class="analysis-icon">📈</div>
          <div class="analysis-content">
            <h3>{{ codeAnalysis.recent_trend?.improvement || 0 }}%</h3>
            <p>{{ $t('dashboard.codeQuality.recentImprovement') }}</p>
            <div class="trend-indicator" :class="codeAnalysis.recent_trend?.improvement >= 0 ? 'positive' : 'negative'">
              {{ codeAnalysis.recent_trend?.improvement >= 0 ? '↗' : '↘' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Pattern Analysis -->
    <div class="card" v-if="dashboardData?.error_patterns?.length || codeAnalysis?.error_analysis">
      <h2 class="card-title">📈 {{ $t('dashboard.errorPatterns.title') }}</h2>
      
      <!-- Original error patterns -->
      <div v-if="dashboardData?.error_patterns?.length" class="error-patterns">
        <div 
          v-for="pattern in dashboardData.error_patterns" 
          :key="pattern.error_type"
          :class="['error-pattern', pattern.error_type === 'no_error' ? 'success-pattern' : '']"
        >
          <div class="pattern-type">{{ getErrorTypeName(pattern.error_type) }}</div>
          <div class="pattern-count">{{ pattern.count }} {{ $t('dashboard.errorPatterns.times') }}</div>
        </div>
      </div>
      
      <!-- Enhanced code analysis error statistics -->
      <div v-if="codeAnalysis?.error_analysis" class="enhanced-error-analysis">
        <h3>{{ $t('dashboard.errorPatterns.detailedErrorAnalysis') }}</h3>
        <div class="error-chart">
          <div 
            v-for="(count, errorType) in codeAnalysis.error_analysis" 
            :key="errorType"
            :class="['error-bar', errorType === 'no_error' ? 'success-bar' : '']"
          >
            <span :class="['error-label', errorType === 'no_error' ? 'success-label' : '']">{{ getErrorTypeName(errorType) }}</span>
            <div class="error-bar-container">
              <div class="error-bar-fill" :style="{ width: `${(count / maxErrorCount) * 100}%` }"></div>
              <span class="error-count">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <router-link to="/modules" class="btn btn-primary">
        📖 {{ $t('dashboard.quickActions.continueLastLesson') }}
      </router-link>
      <button @click="loadDashboard" class="btn btn-secondary">
        🔄 {{ $t('dashboard.quickActions.refreshData') }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { progressAPI, authAPI, analyticsAPI } from '../api.js'

export default {
  name: 'Dashboard',
  setup() {
    const { t, locale } = useI18n()
    const dashboardData = ref(null)
    const codeAnalysis = ref(null)
    const loading = ref(false)

    const userData = computed(() => {
      const data = localStorage.getItem('user')
      return data ? JSON.parse(data) : null
    })

    const maxErrorCount = computed(() => {
      if (!codeAnalysis.value?.error_analysis) return 0
      return Math.max(...Object.values(codeAnalysis.value.error_analysis))
    })

    // 💡 NEW: Hint analysis computed properties
    const maxHintCount = computed(() => {
      if (!codeAnalysis.value?.hint_analysis?.hints_by_level) return 0
      return Math.max(...Object.values(codeAnalysis.value.hint_analysis.hints_by_level))
    })

    const loadDashboard = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        // Load original dashboard data
        const data = await progressAPI.getDashboard()
        dashboardData.value = data
        
        // Load code analysis data
        try {
          const analysisData = await analyticsAPI.getUserCodeAnalysis()
          codeAnalysis.value = analysisData
        } catch (analysisError) {
          console.warn(t('dashboard.loadCodeAnalysisError'), analysisError)
          // Code analysis data loading failure does not affect main functionality
        }
        
      } catch (error) {
        console.error(t('dashboard.loadDashboardError'), error)
        if (window.showNotification) {
          window.showNotification(t('dashboard.loadDashboardError'), 'error')
        }
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const formatDate = (timestamp) => {
      const localeString = locale.value === 'zh' ? 'zh-CN' : 'en-US'
      return new Date(timestamp).toLocaleString(localeString)
    }

    const getErrorTypeName = (errorType) => {
      const errorTypeKeys = {
        'syntax_error': 'syntaxError',
        'name_error': 'nameError',
        'type_error': 'typeError',
        'runtime_error': 'runtimeError',
        'logic_error': 'logicError',
        'timeout_error': 'timeoutError',
        'no_error': 'noError'
      }
      const key = errorTypeKeys[errorType]
      return key ? t(`dashboard.errorPatterns.${key}`) : errorType
    }

    // 💡 NEW: Hint analysis helper methods
    const getMostUsedHintLevel = () => {
      if (!codeAnalysis.value?.hint_analysis?.hints_by_level) return null
      const hintsByLevel = codeAnalysis.value.hint_analysis.hints_by_level
      const maxCount = Math.max(...Object.values(hintsByLevel))
      const mostUsedLevel = Object.keys(hintsByLevel).find(level => hintsByLevel[level] === maxCount)
      return mostUsedLevel
    }

    const getRecentHintTrend = () => {
      if (!codeAnalysis.value?.hint_analysis?.recent_hints_by_day) return null
      const recentHints = codeAnalysis.value.hint_analysis.recent_hints_by_day
      const days = Object.keys(recentHints).sort()
      if (days.length < 2) return null
      
      const recent = recentHints[days[days.length - 1]] || 0
      const previous = recentHints[days[days.length - 2]] || 0
      return recent - previous
    }

    const handleLogout = () => {
      if (confirm(t('dashboard.confirmLogout'))) {
        authAPI.logout()
      }
    }

    onMounted(() => {
      loadDashboard()
    })

    return {
      dashboardData,
      codeAnalysis,
      userData,
      loading,
      maxErrorCount,
      maxHintCount,
      loadDashboard,
      formatDate,
      getErrorTypeName,
      getMostUsedHintLevel,
      getRecentHintTrend,
      handleLogout,
      t,
      locale
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  flex: 1;
}

.dashboard-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #7f8c8d;
  font-size: 18px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-logout {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.btn-logout:hover {
  background: #c0392b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.stat-content h3 {
  font-size: 2rem;
  margin: 0;
  color: #2c3e50;
}

.stat-content p {
  margin: 5px 0 0;
  color: #7f8c8d;
}

.progress-container {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #ecf0f1;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.5s ease;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #7f8c8d;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.activity-icon.success {
  background: #d4edda;
  color: #155724;
}

.activity-icon.error {
  background: #f8d7da;
  color: #721c24;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #2c3e50;
}

.activity-time {
  font-size: 14px;
  color: #7f8c8d;
}

.error-patterns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.error-pattern {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.pattern-type {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
}

.pattern-count {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
}

/* 成功模式（无错误）的绿色样式 */
.success-pattern {
  background: #d4edda !important;
  border: 1px solid #c3e6cb;
}

.success-pattern .pattern-count {
  color: #28a745 !important;
}

.success-pattern .pattern-type {
  color: #155724 !important;
}

.quick-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 30px;
}

/* 代码质量分析样式 */
.code-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.analysis-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.analysis-card:hover {
  transform: translateY(-2px);
}

.analysis-icon {
  font-size: 2rem;
  margin-right: 16px;
}

.analysis-content {
  flex: 1;
}

.analysis-content h3 {
  font-size: 1.5rem;
  margin: 0;
  color: #2c3e50;
}

.analysis-content p {
  margin: 5px 0 0;
  color: #7f8c8d;
  font-size: 14px;
}

.trend-indicator {
  display: inline-block;
  font-size: 1.2rem;
  margin-left: 8px;
}

.trend-indicator.positive {
  color: #27ae60;
}

.trend-indicator.negative {
  color: #e74c3c;
}

/* 增强的错误分析样式 */
.enhanced-error-analysis {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

.enhanced-error-analysis h3 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.error-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-label {
  min-width: 120px;
  font-size: 14px;
  color: #2c3e50;
}

.error-bar-container {
  flex: 1;
  height: 24px;
  background: #ecf0f1;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.error-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
  border-radius: 12px;
  transition: width 0.3s ease;
}

/* 成功条（无错误）的绿色样式 */
.success-bar .error-bar-fill {
  background: linear-gradient(90deg, #28a745, #20a53a) !important;
}

.success-label {
  color: #155724 !important;
  font-weight: 600;
}

.error-count {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #2c3e50;
  font-weight: 500;
}

/* 💡 NEW: Hint Usage Statistics Styles */
.hint-usage-card {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border: 1px solid #f39c12;
}

.hint-usage-card .stat-icon {
  color: #f39c12;
}

.stat-subtitle {
  font-size: 12px;
  color: #856404;
  margin-top: 4px;
  font-weight: normal;
}

.hint-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.hint-level-distribution {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ecf0f1;
}

.hint-level-distribution h3 {
  color: #2c3e50;
  margin-bottom: 16px;
}

.hint-level-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hint-level-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.level-label {
  min-width: 80px;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.level-bar-container {
  flex: 1;
  height: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.level-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #f39c12, #e67e22);
  border-radius: 12px;
  transition: width 0.3s ease;
}

.level-count {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #2c3e50;
  font-weight: 500;
}

.trend-indicator.hint-increase {
  color: #f39c12;
}

.trend-indicator.hint-decrease {
  color: #2ecc71;
}
</style>