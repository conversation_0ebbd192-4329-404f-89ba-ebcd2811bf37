<template>
  <div class="admin-users">
    <div class="page-header">
      <h1>👥 用户管理</h1>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-value">{{ pagination?.total || 0 }}</span>
          <span class="stat-label">总用户数</span>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>用户ID</th>
            <th>用户名</th>
            <th>邮箱</th>
            <th>注册时间</th>
            <th>学习进度</th>
            <th>完成率</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in users" :key="user.user_id" class="user-row">
            <td>{{ user.user_id }}</td>
            <td>
              <div class="user-info">
                <span class="username">{{ user.username }}</span>
              </div>
            </td>
            <td>{{ user.email }}</td>
            <td>{{ formatDate(user.created_at) }}</td>
            <td>
              <div class="progress-info">
                <span>{{ user.learning_stats?.completed_lessons || 0 }} / {{ user.learning_stats?.total_lessons || 0 }}</span>
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    :style="{ width: `${user.learning_stats?.completion_rate || 0}%` }"
                  ></div>
                </div>
              </div>
            </td>
            <td>
              <span class="completion-rate" :class="getCompletionClass(user.learning_stats?.completion_rate)">
                {{ user.learning_stats?.completion_rate || 0 }}%
              </span>
            </td>
            <td>
              <div class="action-buttons">
                <button @click="viewUserDetail(user)" class="btn btn-sm btn-primary">
                  👁️ 查看详情
                </button>
                <button @click="resetUserProgress(user)" class="btn btn-sm btn-warning">
                  🔄 重置进度
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination">
      <button 
        @click="loadUsers(pagination.page - 1)" 
        :disabled="pagination.page <= 1"
        class="btn btn-secondary"
      >
        上一页
      </button>
      <span class="page-info">
        第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页
      </span>
      <button 
        @click="loadUsers(pagination.page + 1)" 
        :disabled="pagination.page >= pagination.pages"
        class="btn btn-secondary"
      >
        下一页
      </button>
    </div>

    <!-- 用户详情模态框 -->
    <div v-if="selectedUser" class="modal-overlay" @click="selectedUser = null">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>👤 用户详情</h3>
          <button @click="selectedUser = null" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="user-details">
            <div class="detail-row">
              <label>用户ID:</label>
              <span>{{ selectedUser.user_id }}</span>
            </div>
            <div class="detail-row">
              <label>用户名:</label>
              <span>{{ selectedUser.username }}</span>
            </div>
            <div class="detail-row">
              <label>邮箱:</label>
              <span>{{ selectedUser.email }}</span>
            </div>
            <div class="detail-row">
              <label>注册时间:</label>
              <span>{{ formatDate(selectedUser.created_at) }}</span>
            </div>
            <div class="detail-row">
              <label>已完成课程:</label>
              <span>{{ selectedUser.learning_stats?.completed_lessons || 0 }} / {{ selectedUser.learning_stats?.total_lessons || 0 }}</span>
            </div>
            <div class="detail-row">
              <label>完成率:</label>
              <span>{{ selectedUser.learning_stats?.completion_rate || 0 }}%</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="selectedUser = null" class="btn btn-secondary">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminUsers',
  setup() {
    const users = ref([])
    const pagination = ref(null)
    const selectedUser = ref(null)
    const loading = ref(false)

    const loadUsers = async (page = 1) => {
      try {
        loading.value = true
        window.setLoading?.(true)
        
        const data = await adminAPI.getUsers({ page, per_page: 20 })
        users.value = data.users
        pagination.value = data.pagination
      } catch (error) {
        console.error('加载用户列表失败:', error)
        window.showNotification?.('加载用户列表失败', 'error')
      } finally {
        loading.value = false
        window.setLoading?.(false)
      }
    }

    const viewUserDetail = (user) => {
      selectedUser.value = user
    }

    const resetUserProgress = async (user) => {
      if (!confirm(`确定要重置用户 ${user.username} 的学习进度吗？此操作不可撤销，将删除该用户的所有学习进度和代码提交记录。`)) {
        return
      }

      try {
        loading.value = true
        window.setLoading?.(true)
        
        const result = await adminAPI.resetUserProgress(user.user_id)
        
        window.showNotification(
          `成功重置用户 ${user.username} 的学习进度，重置了 ${result.details?.reset_lessons || 0} 个课程进度，删除了 ${result.details?.deleted_submissions || 0} 条提交记录`, 
          'success'
        )
        
        // 刷新用户列表
        await loadUsers()
        
      } catch (error) {
        console.error('重置用户进度失败:', error)
        const message = error.response?.data?.error || '重置用户进度失败'
        window.showNotification?.(message, 'error')
      } finally {
        loading.value = false
        window.setLoading?.(false)
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    const getCompletionClass = (rate) => {
      if (rate >= 80) return 'high'
      if (rate >= 50) return 'medium'
      return 'low'
    }

    onMounted(() => {
      loadUsers()
    })

    return {
      users,
      pagination,
      selectedUser,
      loading,
      loadUsers,
      viewUserDetail,
      resetUserProgress,
      formatDate,
      getCompletionClass
    }
  }
}
</script>

<style scoped>
.admin-users {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.users-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: #f8f9fa;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e9ecef;
}

.users-table td {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
}

.user-row:hover {
  background: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  font-weight: 500;
  color: #2c3e50;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  width: 100px;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.completion-rate {
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.completion-rate.high {
  background: #d4edda;
  color: #155724;
}

.completion-rate.medium {
  background: #fff3cd;
  color: #856404;
}

.completion-rate.low {
  background: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.page-info {
  color: #7f8c8d;
  font-size: 14px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.modal-body {
  padding: 20px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.detail-row label {
  font-weight: 500;
  color: #7f8c8d;
}

.detail-row span {
  color: #2c3e50;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #7f8c8d;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e67e22;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>