<template>
  <div class="admin-modules">
    <div class="page-header">
      <h1>{{ $t('admin.interface.modules.title') }}</h1>
      <button @click="showCreateModal = true" class="btn btn-primary">
        {{ $t('admin.interface.modules.createNew') }}
      </button>
    </div>

    <!-- 模块列表 -->
    <div class="modules-grid">
      <div 
        v-for="module in modules" 
        :key="module.module_id"
        class="module-card"
      >
        <div class="module-header">
          <h3>{{ module.title }}</h3>
          <div class="module-actions">
            <button @click="editModule(module)" class="btn btn-sm btn-secondary">
              ✏️ {{ $t('admin.interface.modules.edit') }}
            </button>
            <button @click="deleteModule(module)" class="btn btn-sm btn-danger">
              🗑️ {{ $t('admin.interface.modules.delete') }}
            </button>
          </div>
        </div>
        
        <p class="module-description">{{ module.description }}</p>
        
        <div class="module-stats">
          <span class="stat-item">📖 {{ module.lessons?.length || 0 }} {{ $t('admin.interface.modules.lessonCount') }}</span>
          <span class="stat-item">🎯 {{ $t('admin.interface.modules.order') }}: {{ module.order_index }}</span>
        </div>
        
        <div class="module-footer">
          <router-link 
            :to="`/admin/lessons?module_id=${module.module_id}`" 
            class="btn btn-outline"
          >
            {{ $t('admin.interface.modules.manageLessons') }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- 创建/编辑模块对话框 -->
    <div v-if="showCreateModal || editingModule" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>{{ editingModule ? $t('admin.interface.modules.editModule') : $t('admin.interface.modules.createNewModule') }}</h2>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <form @submit.prevent="saveModule" class="modal-form">
          <div class="form-group">
            <label for="title">{{ $t('admin.interface.modules.moduleTitle') }}</label>
            <input
              id="title"
              v-model="moduleForm.title"
              type="text"
              :placeholder="$t('admin.interface.modules.enterModuleTitle')"
              required
              class="form-input"
            />
          </div>
          
          <div class="form-group">
            <label for="description">{{ $t('admin.interface.modules.moduleDescription') }}</label>
            <textarea
              id="description"
              v-model="moduleForm.description"
              :placeholder="$t('admin.interface.modules.enterModuleDescription')"
              rows="4"
              class="form-input"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="order_index">{{ $t('admin.interface.modules.displayOrder') }}</label>
            <input
              id="order_index"
              v-model.number="moduleForm.order_index"
              type="number"
              min="1"
              required
              class="form-input"
            />
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              {{ $t('admin.interface.modules.cancel') }}
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? $t('admin.interface.modules.saving') : $t('admin.interface.modules.save') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminModules',
  setup() {
    const { t } = useI18n()
    const modules = ref([])
    const showCreateModal = ref(false)
    const editingModule = ref(null)
    const saving = ref(false)
    const loading = ref(false)
    
    const moduleForm = ref({
      title: '',
      description: '',
      order_index: 1
    })

    const loadModules = async () => {
      try {
        loading.value = true
        const data = await adminAPI.getModules()
        modules.value = data.modules || []
      } catch (error) {
        console.error('加载模块失败:', error)
        window.showNotification(t('admin.interface.modules.loadModulesError'), 'error')
      } finally {
        loading.value = false
      }
    }

    const editModule = (module) => {
      editingModule.value = module
      moduleForm.value = {
        title: module.title,
        description: module.description,
        order_index: module.order_index
      }
    }

    const closeModal = () => {
      showCreateModal.value = false
      editingModule.value = null
      moduleForm.value = {
        title: '',
        description: '',
        order_index: modules.value.length + 1
      }
    }

    const saveModule = async () => {
      try {
        saving.value = true
        
        if (editingModule.value) {
          // 编辑现有模块
          await adminAPI.updateModule(editingModule.value.module_id, moduleForm.value)
          window.showNotification(t('admin.interface.modules.moduleUpdateSuccess'), 'success')
        } else {
          // 创建新模块
          await adminAPI.createModule(moduleForm.value)
          window.showNotification(t('admin.interface.modules.moduleCreateSuccess'), 'success')
        }
        
        closeModal()
        await loadModules()
      } catch (error) {
        console.error('保存模块失败:', error)
        const message = error.response?.data?.error || t('admin.interface.modules.saveFailed')
        window.showNotification(message, 'error')
      } finally {
        saving.value = false
      }
    }

    const deleteModule = async (module) => {
      if (!confirm(t('admin.interface.modules.moduleDeleteConfirm', { title: module.title }))) {
        return
      }
      
      try {
        await adminAPI.deleteModule(module.module_id)
        window.showNotification(t('admin.interface.modules.moduleDeleteSuccess'), 'success')
        await loadModules()
      } catch (error) {
        console.error('删除模块失败:', error)
        const message = error.response?.data?.error || t('admin.interface.modules.deleteFailed')
        window.showNotification(message, 'error')
      }
    }

    onMounted(() => {
      loadModules()
    })

    return {
      modules,
      showCreateModal,
      editingModule,
      saving,
      loading,
      moduleForm,
      loadModules,
      editModule,
      closeModal,
      saveModule,
      deleteModule
    }
  }
}
</script>

<style scoped>
.admin-modules {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.module-card:hover {
  transform: translateY(-5px);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.module-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2em;
}

.module-actions {
  display: flex;
  gap: 8px;
}

.module-description {
  color: #7f8c8d;
  margin-bottom: 15px;
  line-height: 1.5;
}

.module-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  font-size: 14px;
  color: #95a5a6;
}

.module-footer {
  border-top: 1px solid #ecf0f1;
  padding-top: 15px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #95a5a6;
}

.close-btn:hover {
  color: #2c3e50;
}

.modal-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #7f8c8d;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>