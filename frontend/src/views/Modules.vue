<template>
  <div class="modules-container">
    <div class="modules-header">
      <h1>📚 {{ $t('modules.title') }}</h1>
      <p>{{ $t('modules.subtitle') }}</p>
    </div>

    <div class="modules-grid" v-if="modules.length">
      <div 
        v-for="module in modules" 
        :key="module.module_id"
        class="module-card"
      >
        <div class="module-header">
          <h2>{{ module.title }}</h2>
          <div class="module-progress">
            <div class="progress-info">
              {{ getCompletedLessons(module) }} / {{ module.lessons.length }} {{ $t('modules.lessonsCompleted') }}
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${getModuleProgress(module)}%` }"
              ></div>
            </div>
          </div>
        </div>
        
        <p class="module-description">{{ module.description }}</p>
        
        <div class="lessons-list">
          <div 
            v-for="lesson in module.lessons" 
            :key="lesson.lesson_id"
            class="lesson-item"
            :class="getLessonStatusClass(lesson)"
            @click="handleLessonClick(lesson)"
          >
            <div class="lesson-status">
              {{ getLessonIcon(lesson) }}
            </div>
            <div class="lesson-content">
              <div class="lesson-title">{{ lesson.title }}</div>
              <div class="lesson-meta">
                <span v-if="lesson.progress">
                  {{ getProgressText(lesson.progress) }}
                </span>
                <span v-if="!lesson.is_unlocked" class="locked-text">
                  {{ $t('modules.prerequisiteRequired') }}
                </span>
              </div>
            </div>
            <div class="lesson-arrow" v-if="lesson.is_unlocked">→</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="loading" class="loading-message">
      <div class="spinner"></div>
      <p>{{ $t('modules.loading') }}</p>
    </div>

    <div v-else class="empty-state">
      <h3>🤔 {{ $t('modules.emptyTitle') }}</h3>
      <p>{{ $t('modules.emptyDescription') }}</p>
      <button @click="loadModules" class="btn btn-primary">{{ $t('modules.reload') }}</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { contentAPI } from '../api.js'

export default {
  name: 'Modules',
  setup() {
    const { t } = useI18n()
    const router = useRouter()
    const modules = ref([])
    const loading = ref(false)

    const loadModules = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const data = await contentAPI.getModules()
        modules.value = data.modules || []
      } catch (error) {
        console.error('Failed to load modules:', error)
        window.showNotification(t('modules.loadError'), 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const getCompletedLessons = (module) => {
      return module.lessons.filter(lesson => 
        lesson.progress?.status === 'completed'
      ).length
    }

    const getModuleProgress = (module) => {
      const completed = getCompletedLessons(module)
      const total = module.lessons.length
      return total > 0 ? Math.round((completed / total) * 100) : 0
    }

    const getLessonStatusClass = (lesson) => {
      if (!lesson.is_unlocked) return 'lesson-locked'
      if (lesson.progress?.status === 'completed') return 'lesson-completed'
      if (lesson.progress?.status === 'in_progress') return 'lesson-in-progress'
      return 'lesson-not-started'
    }

    const getLessonIcon = (lesson) => {
      if (!lesson.is_unlocked) return '🔒'
      if (lesson.progress?.status === 'completed') return '✅'
      if (lesson.progress?.status === 'in_progress') return '📝'
      return '📖'
    }

    const getProgressText = (progress) => {
      const statusMap = {
        'not_started': t('modules.progressStatus.notStarted'),
        'in_progress': t('modules.progressStatus.inProgress', { attempts: progress.attempts || 0 }),
        'completed': t('modules.progressStatus.completed')
      }
      return statusMap[progress.status] || t('modules.progressStatus.unknown')
    }

    const handleLessonClick = (lesson) => {
      if (!lesson.is_unlocked) {
        window.showNotification(t('modules.prerequisiteWarning'), 'warning')
        return
      }
      
      router.push(`/lesson/${lesson.lesson_id}`)
    }

    onMounted(() => {
      loadModules()
    })

    return {
      modules,
      loading,
      loadModules,
      getCompletedLessons,
      getModuleProgress,
      getLessonStatusClass,
      getLessonIcon,
      getProgressText,
      handleLessonClick,
      t
    }
  }
}
</script>

<style scoped>
.modules-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.modules-header {
  text-align: center;
  margin-bottom: 40px;
}

.modules-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.modules-header p {
  color: #7f8c8d;
  font-size: 18px;
}

.modules-grid {
  display: grid;
  gap: 30px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 6px 25px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.module-card:hover {
  transform: translateY(-5px);
}

.module-header {
  margin-bottom: 16px;
}

.module-header h2 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 1.5rem;
}

.module-progress {
  margin-bottom: 8px;
}

.progress-info {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.5s ease;
}

.module-description {
  color: #7f8c8d;
  margin-bottom: 20px;
  line-height: 1.6;
}

.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.lesson-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.lesson-item:hover {
  background: #f8f9fa;
}

.lesson-not-started {
  background: #ffffff;
  border-color: #dee2e6;
}

.lesson-not-started:hover {
  border-color: #3498db;
}

.lesson-in-progress {
  background: #fff3cd;
  border-color: #ffc107;
}

.lesson-completed {
  background: #d4edda;
  border-color: #28a745;
}

.lesson-locked {
  background: #f8f9fa;
  border-color: #dee2e6;
  cursor: not-allowed;
  opacity: 0.6;
}

.lesson-status {
  font-size: 24px;
  margin-right: 16px;
  width: 40px;
  text-align: center;
}

.lesson-content {
  flex: 1;
}

.lesson-title {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.lesson-meta {
  font-size: 14px;
  color: #7f8c8d;
}

.locked-text {
  color: #dc3545;
  font-style: italic;
}

.lesson-arrow {
  font-size: 18px;
  color: #3498db;
  margin-left: 16px;
}

.loading-message {
  text-align: center;
  padding: 60px 20px;
}

.loading-message .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-state h3 {
  margin-bottom: 16px;
  color: #2c3e50;
}

.empty-state p {
  margin-bottom: 24px;
}
</style>