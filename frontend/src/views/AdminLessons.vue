<template>
  <div class="admin-lessons">
    <div class="page-header">
      <h1>{{ $t('admin.interface.lessons.title') }}</h1>
      <div class="header-actions">
        <select v-model="selectedModuleId" @change="loadLessons" class="module-select">
          <option value="">{{ $t('admin.interface.lessons.allModules') }}</option>
          <option v-for="module in modules" :key="module.module_id" :value="module.module_id">
            {{ module.title }}
          </option>
        </select>
        <button @click="showCreateModal = true" class="btn btn-primary">
          {{ $t('admin.interface.lessons.createNewLesson') }}
        </button>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="lessons-grid">
      <div 
        v-for="lesson in lessons" 
        :key="lesson.lesson_id"
        class="lesson-card"
      >
        <div class="lesson-header">
          <h3>{{ lesson.title }}</h3>
          <div class="lesson-actions">
            <button @click="editLesson(lesson)" class="btn btn-sm btn-secondary">
              ✏️ {{ $t('admin.interface.lessons.edit') }}
            </button>
            <button @click="deleteLesson(lesson)" class="btn btn-sm btn-danger">
              🗑️ {{ $t('admin.interface.lessons.delete') }}
            </button>
          </div>
        </div>
        
        <div class="lesson-meta">
          <span class="meta-item">📚 {{ getModuleName(lesson.module_id) }}</span>
          <span class="meta-item">🎯 {{ $t('admin.interface.lessons.order') }}: {{ lesson.order_index }}</span>
        </div>
        
        <div class="lesson-content-preview">
          <p>{{ getContentPreview(lesson.content_md) }}</p>
        </div>
        
        <div class="lesson-footer">
          <router-link 
            :to="`/admin/exercises?lesson_id=${lesson.lesson_id}`" 
            class="btn btn-outline"
          >
            {{ $t('admin.interface.lessons.manageExercises') }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- 创建/编辑课程对话框 -->
    <div v-if="showCreateModal || editingLesson" class="modal-overlay" @click="closeModal">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h2>{{ editingLesson ? $t('admin.interface.lessons.editLesson') : $t('admin.interface.lessons.createNewLessonTitle') }}</h2>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <form @submit.prevent="saveLesson" class="modal-form">
          <div class="form-row">
            <div class="form-group">
              <label for="title">{{ $t('admin.interface.lessons.lessonTitle') }}</label>
              <input
                id="title"
                v-model="lessonForm.title"
                type="text"
                :placeholder="$t('admin.interface.lessons.enterLessonTitle')"
                required
                class="form-input"
              />
            </div>
            
            <div class="form-group">
              <label for="module_id">{{ $t('admin.interface.lessons.belongsToModule') }}</label>
              <select
                id="module_id"
                v-model="lessonForm.module_id"
                required
                class="form-input"
              >
                <option value="">{{ $t('admin.interface.lessons.pleaseSelectModule') }}</option>
                <option v-for="module in modules" :key="module.module_id" :value="module.module_id">
                  {{ module.title }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="order_index">{{ $t('admin.interface.lessons.displayOrder') }}</label>
              <input
                id="order_index"
                v-model.number="lessonForm.order_index"
                type="number"
                min="1"
                required
                class="form-input"
              />
            </div>
            
            <div class="form-group">
              <label for="prerequisite_lesson_id">{{ $t('admin.interface.lessons.prerequisiteLesson') }}</label>
              <select
                id="prerequisite_lesson_id"
                v-model="lessonForm.prerequisite_lesson_id"
                class="form-input"
              >
                <option value="">{{ $t('admin.interface.lessons.noPrerequisite') }}</option>
                <option 
                  v-for="lesson in availablePrerequisites" 
                  :key="lesson.lesson_id" 
                  :value="lesson.lesson_id"
                >
                  {{ lesson.title }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label for="content_md">{{ $t('admin.interface.lessons.lessonContent') }}</label>
            <div class="markdown-editor">
              <div class="editor-toolbar">
                <button type="button" @click="insertMarkdown('# ')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.h1') }}</button>
                <button type="button" @click="insertMarkdown('## ')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.h2') }}</button>
                <button type="button" @click="insertMarkdown('**', '**')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.bold') }}</button>
                <button type="button" @click="insertMarkdown('*', '*')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.italic') }}</button>
                <button type="button" @click="insertMarkdown('`', '`')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.code') }}</button>
                <button type="button" @click="insertMarkdown('```python\n', '\n```')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.codeBlock') }}</button>
                <button type="button" @click="insertMarkdown('- ')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.list') }}</button>
                <button type="button" @click="insertMarkdown('[', '](url)')" class="toolbar-btn">{{ $t('admin.interface.lessons.markdownToolbar.link') }}</button>
                <button type="button" @click="showPreview = !showPreview" class="toolbar-btn">
                  {{ showPreview ? $t('admin.interface.lessons.markdownToolbar.edit') : $t('admin.interface.lessons.markdownToolbar.preview') }}
                </button>
              </div>
              
              <div class="editor-container">
                <textarea
                  v-if="!showPreview"
                  ref="markdownEditor"
                  v-model="lessonForm.content_md"
                  :placeholder="$t('admin.interface.lessons.enterContentMarkdown')"
                  rows="15"
                  class="markdown-input"
                ></textarea>
                
                <div v-if="showPreview" class="markdown-preview" v-html="markdownPreview"></div>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="closeModal" class="btn btn-secondary">
              {{ $t('admin.interface.lessons.cancel') }}
            </button>
            <button type="submit" class="btn btn-primary" :disabled="saving">
              {{ saving ? $t('admin.interface.lessons.saving') : $t('admin.interface.lessons.save') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { adminAPI } from '../adminApi.js'

export default {
  name: 'AdminLessons',
  setup() {
    const { t } = useI18n()
    const lessons = ref([])
    const modules = ref([])
    const selectedModuleId = ref('')
    const showCreateModal = ref(false)
    const editingLesson = ref(null)
    const saving = ref(false)
    const loading = ref(false)
    const showPreview = ref(false)
    const markdownEditor = ref(null)
    
    const lessonForm = ref({
      title: '',
      module_id: '',
      content_md: '',
      prerequisite_lesson_id: '',
      order_index: 1
    })

    const availablePrerequisites = computed(() => {
      return lessons.value.filter(lesson => {
        if (editingLesson.value) {
          return lesson.lesson_id !== editingLesson.value.lesson_id
        }
        return true
      })
    })

    const markdownPreview = computed(() => {
      // 简单的Markdown转HTML预览
      let html = lessonForm.value.content_md
      
      // 标题
      html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>')
      html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>')
      html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>')
      
      // 粗体和斜体
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // 代码
      html = html.replace(/`(.*?)`/g, '<code>$1</code>')
      html = html.replace(/```python\n([\s\S]*?)\n```/g, '<pre><code class="language-python">$1</code></pre>')
      html = html.replace(/```\n([\s\S]*?)\n```/g, '<pre><code>$1</code></pre>')
      
      // 列表
      html = html.replace(/^- (.*$)/gm, '<li>$1</li>')
      html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      
      // 链接
      html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
      
      // 换行
      html = html.replace(/\n/g, '<br>')
      
      return html
    })

    const loadModules = async () => {
      try {
        const data = await adminAPI.getModules()
        modules.value = data.modules || []
      } catch (error) {
        console.error('加载模块失败:', error)
      }
    }

    const loadLessons = async () => {
      try {
        loading.value = true
        const data = await adminAPI.getLessons(selectedModuleId.value || null)
        lessons.value = data.lessons || []
      } catch (error) {
        console.error('加载课程失败:', error)
        window.showNotification(t('admin.interface.lessons.loadLessonsError'), 'error')
      } finally {
        loading.value = false
      }
    }

    const getModuleName = (moduleId) => {
      const module = modules.value.find(m => m.module_id === moduleId)
      return module ? module.title : t('admin.interface.lessons.unknownModule')
    }

    const getContentPreview = (content) => {
      if (!content) return t('admin.interface.lessons.noContent')
      return content.substring(0, 100) + (content.length > 100 ? '...' : '')
    }

    const editLesson = (lesson) => {
      editingLesson.value = lesson
      lessonForm.value = {
        title: lesson.title,
        module_id: lesson.module_id,
        content_md: lesson.content_md || '',
        prerequisite_lesson_id: lesson.prerequisite_lesson_id || '',
        order_index: lesson.order_index
      }
      showPreview.value = false
    }

    const closeModal = () => {
      showCreateModal.value = false
      editingLesson.value = null
      showPreview.value = false
      lessonForm.value = {
        title: '',
        module_id: selectedModuleId.value || '',
        content_md: '',
        prerequisite_lesson_id: '',
        order_index: lessons.value.length + 1
      }
    }

    const insertMarkdown = (before, after = '') => {
      const textarea = markdownEditor.value
      if (!textarea) return
      
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = textarea.value
      const selectedText = text.substring(start, end)
      
      const newText = before + selectedText + after
      lessonForm.value.content_md = text.substring(0, start) + newText + text.substring(end)
      
      // 设置光标位置
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length)
      }, 0)
    }

    const saveLesson = async () => {
      try {
        saving.value = true
        
        if (editingLesson.value) {
          await adminAPI.updateLesson(editingLesson.value.lesson_id, lessonForm.value)
          window.showNotification(t('admin.interface.lessons.lessonUpdateSuccess'), 'success')
        } else {
          await adminAPI.createLesson(lessonForm.value)
          window.showNotification(t('admin.interface.lessons.lessonCreateSuccess'), 'success')
        }
        
        closeModal()
        await loadLessons()
      } catch (error) {
        console.error('保存课程失败:', error)
        const message = error.response?.data?.error || t('admin.interface.lessons.saveFailed')
        window.showNotification(message, 'error')
      } finally {
        saving.value = false
      }
    }

    const deleteLesson = async (lesson) => {
      if (!confirm(t('admin.interface.lessons.lessonDeleteConfirm', { title: lesson.title }))) {
        return
      }
      
      try {
        await adminAPI.deleteLesson(lesson.lesson_id)
        window.showNotification(t('admin.interface.lessons.lessonDeleteSuccess'), 'success')
        await loadLessons()
      } catch (error) {
        console.error('删除课程失败:', error)
        const message = error.response?.data?.error || t('admin.interface.lessons.deleteFailed')
        window.showNotification(message, 'error')
      }
    }

    onMounted(() => {
      loadModules()
      loadLessons()
    })

    return {
      lessons,
      modules,
      selectedModuleId,
      showCreateModal,
      editingLesson,
      saving,
      loading,
      showPreview,
      markdownEditor,
      lessonForm,
      availablePrerequisites,
      markdownPreview,
      loadLessons,
      getModuleName,
      getContentPreview,
      editLesson,
      closeModal,
      insertMarkdown,
      saveLesson,
      deleteLesson
    }
  }
}
</script>

<style scoped>
.admin-lessons {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #2c3e50;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.module-select {
  padding: 8px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.lessons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.lesson-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.lesson-card:hover {
  transform: translateY(-5px);
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.lesson-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2em;
}

.lesson-actions {
  display: flex;
  gap: 8px;
}

.lesson-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.meta-item {
  font-size: 14px;
  color: #95a5a6;
}

.lesson-content-preview {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.lesson-content-preview p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.lesson-footer {
  border-top: 1px solid #ecf0f1;
  padding-top: 15px;
}

.modal-content.large {
  max-width: 900px;
  width: 95%;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.markdown-editor {
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
  padding: 10px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-btn {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: #e9ecef;
}

.editor-container {
  position: relative;
  min-height: 400px;
}

.markdown-input {
  width: 100%;
  min-height: 400px;
  padding: 15px;
  border: none;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  outline: none;
}

.markdown-preview {
  padding: 15px;
  min-height: 400px;
  background: white;
  overflow-y: auto;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3 {
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
}

.markdown-preview h1 {
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.markdown-preview code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.markdown-preview pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
}

.markdown-preview pre code {
  background: none;
  padding: 0;
}

.markdown-preview ul {
  padding-left: 20px;
}

.markdown-preview li {
  margin-bottom: 5px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #7f8c8d;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-outline {
  background: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #95a5a6;
}

.close-btn:hover {
  color: #2c3e50;
}

.modal-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>