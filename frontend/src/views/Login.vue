<template>
  <div class="auth-container">
    <div class="auth-card">
      <h1 class="auth-title">🐍 {{ $t('auth.loginTitle') }}</h1>
      
      <form @submit.prevent="handleLogin">
        <div class="form-group">
          <label class="form-label">{{ $t('auth.username') }}</label>
          <input
            v-model="loginForm.username"
            type="text"
            class="form-input"
            :placeholder="$t('auth.usernamePlaceholder')"
            required
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">{{ $t('auth.password') }}</label>
          <input
            v-model="loginForm.password"
            type="password"
            class="form-input"
            :placeholder="$t('auth.passwordPlaceholder')"
            required
          />
        </div>
        
        <button type="submit" class="btn btn-primary" :disabled="loading">
          {{ loading ? $t('auth.loggingIn') : $t('auth.loginButton') }}
        </button>
      </form>
      
      <div class="auth-link">
        {{ $t('auth.noAccount') }}
        <router-link to="/register">{{ $t('auth.register') }}</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { authAPI } from '../api.js'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const { t } = useI18n()
    const loading = ref(false)
    const loginForm = ref({
      username: '',
      password: ''
    })

    const handleLogin = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        await authAPI.login(loginForm.value)
        
        window.showNotification(t('auth.loginSuccess'), 'success')
        router.push('/dashboard')
      } catch (error) {
        console.error('Login failed:', error)
        const message = error.response?.data?.error || t('auth.loginError')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    return {
      loginForm,
      loading,
      handleLogin
    }
  }
}
</script>