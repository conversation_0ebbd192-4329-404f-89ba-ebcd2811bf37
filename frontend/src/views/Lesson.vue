<template>
  <div class="lesson-container" v-if="lesson">
    <!-- Lesson Navigation -->
    <div class="lesson-nav">
      <button @click="$router.go(-1)" class="btn btn-secondary">
        {{ $t('lesson.backToCourseList') }}
      </button>
      <div class="lesson-title">
        <h1>{{ lesson.title }}</h1>
        <div class="lesson-progress">
          <span class="progress-badge" :class="lesson.progress?.status">
            {{ getStatusText(lesson.progress?.status) }}
          </span>
          <span v-if="lesson.progress?.attempts" class="attempts-text">
            {{ $t('lesson.attemptCount', { count: lesson.progress.attempts }) }}
          </span>
          <span v-if="lesson.progress?.avg_time_sec && lesson.progress.avg_time_sec > 0" class="avg-time-text">
            📊 {{ $t('lesson.avgSolvingTime') }}: {{ formatTimerTime(Math.round(lesson.progress.avg_time_sec)) }}
          </span>
          <!-- DEBUG: 显示原始数据 -->
          <span v-if="lesson.progress?.avg_time_sec !== undefined" class="debug-text" style="font-size: 12px; color: #666;">
            [DEBUG: avg_time_sec = {{ lesson.progress.avg_time_sec }}]
          </span>
        </div>
      </div>
    </div>

    <!-- Lesson Content Area -->
    <div class="lesson-content">
      <!-- Theoretical Content -->
      <div class="content-section">
        <div class="markdown-content" v-html="renderedContent"></div>
      </div>

      <!-- Programming Exercises -->
      <div v-if="lesson.exercises?.length" class="exercises-section">
        <h2>{{ $t('lesson.programmingExercises') }}</h2>
        <div 
          v-for="(exercise, index) in lesson.exercises" 
          :key="exercise.exercise_id"
          class="exercise-card"
        >
          <div class="exercise-header">
            <h3>{{ $t('lesson.exerciseNumber', { number: index + 1 }) }}</h3>
            <div class="exercise-actions">
              <button 
                v-if="canShowHints(exercise)"
                @click="requestHint(exercise.exercise_id)"
                class="btn btn-warning btn-sm"
                :disabled="hintLoading"
              >
                {{ $t('lesson.getHint') }}
              </button>
              <button 
                @click="toggleHistory(exercise.exercise_id)"
                class="btn btn-info btn-sm"
                :disabled="historyLoading"
              >
                📊 {{ $t('lesson.viewHistory') }}
              </button>
            </div>
          </div>

          <div class="exercise-content">
            <div class="problem-statement">
              <p>{{ exercise.problem_statement }}</p>
            </div>

            <!-- Hints Display -->
            <div v-if="hints[exercise.exercise_id]" class="hints-section">
              <h4>{{ $t('lesson.hints') }}</h4>
              <div 
                v-for="(hint, hintIndex) in hints[exercise.exercise_id]" 
                :key="hintIndex"
                class="hint-item"
              >
                <strong>{{ $t('lesson.hintNumber', { number: hintIndex + 1 }) }}</strong> {{ hint }}
              </div>
            </div>

            <!-- Submission History Display -->
            <div v-if="histories[exercise.exercise_id]" class="history-section">
              <h4>{{ $t('lesson.submissionHistory') }}</h4>
              <div class="history-list">
                <div 
                  v-for="(submission, historyIndex) in histories[exercise.exercise_id]" 
                  :key="submission.submission_id"
                  class="history-item clickable"
                  :class="{ 'success': submission.is_correct }"
                  @click="showSubmissionCode(submission)"
                  :title="$t('lesson.clickToViewCode')"
                >
                  <span class="history-time">{{ formatTime(submission.timestamp) }}</span>
                  <span class="history-status">{{ submission.is_correct ? '✅' : '❌' }}</span>
                  <span class="history-attempts">{{ $t('lesson.attempt', { number: histories[exercise.exercise_id].length - historyIndex }) }}</span>
                  <span v-if="submission.error_type && !submission.is_correct" class="history-error">
                    ({{ $t(`lesson.errorTypes.${submission.error_type}`) || submission.error_type }})
                  </span>
                  <span class="history-hint">👁️</span>
                </div>
              </div>
            </div>

            <!-- Code Editor -->
            <div class="code-editor-section">
              <div class="editor-header">
                <div class="editor-title">
                  <span>{{ $t('lesson.pythonCodeEditor') }}</span>
                  <!-- 计时器显示 -->
                  <div v-if="hasStartedCoding[exercise.exercise_id]" class="coding-timer">
                    <i class="timer-icon">⏱️</i>
                    <span class="timer-text">{{ formatTimerTime(currentTimes[exercise.exercise_id] || 0) }}</span>
                  </div>
                </div>
                <div class="editor-controls">
                  <button 
                    @click="toggleTheme"
                    class="btn btn-secondary btn-sm"
                    :title="$t('lesson.toggleTheme')"
                  >
                    {{ isDarkTheme ? '🌞' : '🌙' }}
                  </button>
                  <button 
                    @click="runCode(exercise)"
                    class="btn btn-success"
                    :disabled="codeRunning"
                  >
                    {{ codeRunning ? $t('lesson.running') : '▶ ' + $t('lesson.runCode') }}
                  </button>
                </div>
              </div>
              <div class="code-editor">
                <CodeEditor
                  v-model="codes[exercise.exercise_id]"
                  :theme="isDarkTheme ? 'dark' : 'light'"
                  height="250px"
                  :placeholder="$t('lesson.placeholder')"
                  @update:modelValue="onCodeChange(exercise.exercise_id, $event)"
                />
              </div>
            </div>

            <!-- Execution Results -->
            <div v-if="results[exercise.exercise_id]" class="result-section">
              <div class="result-header" :class="results[exercise.exercise_id].is_correct ? 'success' : 'error'">
                <span class="result-icon">
                  {{ results[exercise.exercise_id].is_correct ? '✅' : '❌' }}
                </span>
                <span class="result-text">
                  {{ results[exercise.exercise_id].is_correct ? $t('lesson.codeCorrect') : $t('lesson.codeNeedsImprovement') }}
                </span>
                <!-- 🔥 NEW: Feedback Comparison Toggle Button -->
                <div class="feedback-controls" v-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated">
                  <button 
                    @click="toggleFeedbackMode(exercise.exercise_id)"
                    class="btn btn-sm btn-secondary"
                    :title="feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewDetailedComparison') : $t('lesson.simpleView')"
                  >
                    {{ feedbackModes[exercise.exercise_id] === 'simple' ? $t('lesson.viewComparison') : $t('lesson.simpleMode') }}
                  </button>
                </div>
              </div>
              
              <!-- Simple Feedback Mode -->
              <div v-if="feedbackModes[exercise.exercise_id] === 'simple'" class="feedback-message">
                {{ results[exercise.exercise_id].feedback }}
              </div>

              <!-- 🔥 NEW: Detailed Feedback Comparison Mode -->
              <div v-else-if="results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback?.ai_generated" class="comprehensive-feedback-section">
                <div class="feedback-tabs">
                  <button 
                    v-for="(feedbackData, feedbackType) in getAvailableFeedbacks(exercise.exercise_id)"
                    :key="feedbackType"
                    @click="!feedbackData.placeholder && (activeFeedbackTabs[exercise.exercise_id] = feedbackType)"
                    class="feedback-tab"
                    :class="{ 
                      active: activeFeedbackTabs[exercise.exercise_id] === feedbackType,
                      disabled: feedbackData.placeholder
                    }"
                    :disabled="feedbackData.placeholder"
                  >
                    <span v-if="feedbackData.loading" class="spinner-sm"></span>
                    {{ getFeedbackTypeLabel(feedbackType) }}
                    <span class="generation-time" v-if="feedbackData.generation_time">
                      ({{ Math.round(feedbackData.generation_time) }}ms)
                    </span>
                  </button>
                </div>
                
                <div class="feedback-content-area">
                  <div class="active-feedback-content">
                    <FeedbackComponents
                      :feedback-data="getActiveFeedbackData(exercise.exercise_id)"
                      :submission-id="results[exercise.exercise_id].submission?.submission_id"
                      :current-component="getFeedbackComponent(activeFeedbackTabs[exercise.exercise_id])"
                      @rate-feedback="handleFeedbackRating"
                    />
                  </div>
                  
                  <!-- Feedback Comparison Insights -->
                  <div class="feedback-insights" v-if="(results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.comparison">
                    <h5>{{ $t('lesson.feedbackComparison') }}</h5>
                    <div class="insights-content">
                      <p><strong>{{ $t('lesson.feedbackSources') }}</strong> {{ feedbackSourceInfo(exercise.exercise_id).count }} <span class="source-names">{{ feedbackSourceInfo(exercise.exercise_id).names }}</span></p>
                      <p><strong>{{ $t('lesson.recommendedPrimaryFeedback') }}</strong> {{ getFeedbackTypeLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.recommended_primary) }}</p>
                      <p><strong>{{ $t('lesson.learningStage') }}</strong> {{ getStageLabel((results[exercise.exercise_id].comprehensive_feedback?.comprehensive_feedback || results[exercise.exercise_id].comprehensive_feedback)?.learning_stage_adapted?.stage) }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Error Information -->
              <div v-if="!results[exercise.exercise_id].is_correct" class="error-details">
                <div v-if="results[exercise.exercise_id].error_message" class="error-message">
                  <strong>{{ $t('lesson.errorMessage') }}</strong>
                  <pre>{{ results[exercise.exercise_id].error_message }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].output" class="output-section">
                  <strong>{{ $t('lesson.yourOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].output }}</pre>
                </div>
                
                <div v-if="results[exercise.exercise_id].expected_output" class="expected-output">
                  <strong>{{ $t('lesson.expectedOutput') }}:</strong>
                  <pre>{{ results[exercise.exercise_id].expected_output }}</pre>
                </div>
              </div>

              <!-- Code Quality Analysis -->
              <div v-if="results[exercise.exercise_id].submission" class="code-quality-section">
                <h4>{{ $t('lesson.codeQualityAnalysis') }}</h4>
                <div class="quality-metrics">
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_lines">
                    <span class="metric-label">{{ $t('lesson.linesOfCode') }}</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_lines }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.code_complexity">
                    <span class="metric-label">{{ $t('lesson.complexity') }}:</span>
                    <span class="metric-value">{{ results[exercise.exercise_id].submission.code_complexity.toFixed(1) }}</span>
                  </div>
                  <div class="metric" v-if="results[exercise.exercise_id].submission.syntax_score">
                    <span class="metric-label">{{ $t('lesson.qualityScore') }}</span>
                    <span class="metric-value quality-score" :class="getScoreClass(results[exercise.exercise_id].submission.syntax_score)">
                      {{ results[exercise.exercise_id].submission.syntax_score.toFixed(1) }}/10
                    </span>
                  </div>
                </div>
              </div>

              <!-- 🔧 修复：独立的改进建议板块 -->
              <div v-if="results[exercise.exercise_id].submission?.api_analysis_result" class="improvement-suggestions-section">
                <h4>{{ $t('lesson.improvementSuggestions') }}</h4>
                <div class="suggestions-list">
                  <div v-for="suggestion in getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result)" 
                       :key="suggestion"
                       class="suggestion-item">
                    <span class="suggestion-text">{{ suggestion }}</span>
                  </div>
                </div>
                <div v-if="getAnalysisSuggestions(results[exercise.exercise_id].submission.api_analysis_result).length === 0" class="no-suggestions">
                  <span class="suggestion-icon">ℹ️</span>
                  <span class="suggestion-text">{{ $t('lesson.noSuggestionsAvailable') }}</span>
                </div>
              </div>

              <!-- Hint Availability Reminder -->
              <div v-if="!results[exercise.exercise_id].is_correct && results[exercise.exercise_id].hints_available" class="hint-reminder">
                {{ $t('lesson.hintReminder') }}
              </div>
            </div>

            <!-- Test Cases Display -->
            <div v-if="exercise.test_cases?.length" class="test-cases-section">
              <h4>{{ $t('lesson.testCases') }}</h4>
              <div 
                v-for="(testCase, testIndex) in exercise.test_cases" 
                :key="testIndex"
                class="test-case"
              >
                <div><strong>{{ $t('lesson.input') }}</strong> {{ testCase.input_data || $t('lesson.noInput') }}</div>
                <div><strong>{{ $t('lesson.expectedOutput') }}:</strong> {{ testCase.expected_output }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Course Completion Notification -->
    <div v-if="allExercisesCompleted" class="completion-banner">
      <div class="completion-content">
        <h3>{{ $t('lesson.congratulationsCompleted') }}</h3>
        <p>{{ $t('lesson.masteredAllConcepts') }}</p>
        <router-link to="/modules" class="btn btn-primary">
          {{ $t('lesson.continueToNextCourse') }}
        </router-link>
      </div>
    </div>
  </div>

  <div v-else-if="loading" class="loading-container">
    <div class="spinner"></div>
    <p>{{ $t('lesson.loadingContent') }}</p>
  </div>

  <div v-else class="error-container">
    <h3>{{ $t('lesson.loadFailed') }}</h3>
    <p>{{ $t('lesson.loadFailedMessage') }}</p>
    <button @click="loadLesson" class="btn btn-primary">{{ $t('lesson.reload') }}</button>
  </div>

  <!-- Submission Code Modal -->
  <div v-if="showCodeModal" class="code-modal-overlay" @click="closeCodeModal">
    <div class="code-modal" @click.stop>
      <div class="code-modal-header">
        <h3>{{ $t('lesson.submissionCodeTitle') }}</h3>
        <button class="close-btn" @click="closeCodeModal">×</button>
      </div>
      <div class="code-modal-info">
        <span class="modal-time">{{ formatTime(selectedSubmission?.timestamp) }}</span>
        <span class="modal-status">{{ selectedSubmission?.is_correct ? '✅ ' + $t('lesson.success') : '❌ ' + $t('lesson.failed') }}</span>
        <span v-if="selectedSubmission?.error_type && !selectedSubmission?.is_correct" class="modal-error">
          ({{ $t(`lesson.errorTypes.${selectedSubmission.error_type}`) || selectedSubmission.error_type }})
        </span>
      </div>
      <div class="code-modal-content">
        <pre><code>{{ selectedSubmission?.code || $t('lesson.noCodeAvailable') }}</code></pre>
      </div>
      <div class="code-modal-footer">
        <button class="btn btn-secondary" @click="closeCodeModal">{{ $t('common.close') }}</button>
        <button v-if="selectedSubmission?.code" class="btn btn-primary" @click="copyToCurrentEditor">
          {{ $t('lesson.copyToEditor') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { contentAPI, submissionAPI } from '../api.js'
import { marked } from 'marked'
import CodeEditor from '../components/CodeEditor.vue'
import FeedbackComponents from '../components/FeedbackComponents.vue'

export default {
  name: 'Lesson',
  components: {
    CodeEditor,
    FeedbackComponents
  },
  setup() {
    const route = useRoute()
    const { t } = useI18n()
    const lesson = ref(null)
    const loading = ref(false)
    const codeRunning = ref(false)
    const hintLoading = ref(false)
    const isDarkTheme = ref(false)
    
    // Store code, results and hints for each exercise
    const codes = ref({})
    const results = ref({})
    const hints = ref({})
    
    // Submission history for each exercise
    const histories = ref({})
    const historyLoading = ref(false)
    
    // Code modal for viewing submission code
    const showCodeModal = ref(false)
    const selectedSubmission = ref(null)
    
    // 🔥 NEW: Feedback mode and tab management
    const feedbackModes = ref({}) // 'simple' or 'detailed'
    const activeFeedbackTabs = ref({}) // Currently active feedback tab
    
    // 🔥 NEW: Time tracking for problem-solving duration
    const startTimes = ref({}) // Track when user starts working on each exercise
    const hasStartedCoding = ref({}) // Track if user has started typing code
    const currentTimes = ref({}) // Current elapsed time for each exercise (in seconds)
    const timers = ref({}) // Timer intervals for each exercise

    const renderedContent = computed(() => {
      if (!lesson.value?.content_md) return ''
      return marked(lesson.value.content_md)
    })

    const allExercisesCompleted = computed(() => {
      if (!lesson.value?.exercises?.length) return false
      return lesson.value.exercises.every(exercise => 
        results.value[exercise.exercise_id]?.is_correct
      )
    })

    const loadLesson = async () => {
      try {
        loading.value = true
        window.setLoading(true)
        
        const lessonId = route.params.id
        const data = await contentAPI.getLesson(lessonId)
        lesson.value = data.lesson
        console.log('DEBUG: 课程数据加载完成:', data.lesson.progress)
        
        // Initialize code storage for each exercise
        if (lesson.value.exercises) {
          lesson.value.exercises.forEach(exercise => {
            codes.value[exercise.exercise_id] = `# ${t('lesson.placeholder').replace('...', '')}\n`
            // Initialize feedback modes
            feedbackModes.value[exercise.exercise_id] = 'simple'
            activeFeedbackTabs.value[exercise.exercise_id] = 'structural'
            // Initialize time tracking
            startTimes.value[exercise.exercise_id] = null
            hasStartedCoding.value[exercise.exercise_id] = false
            currentTimes.value[exercise.exercise_id] = 0
            timers.value[exercise.exercise_id] = null
          })
        }
      } catch (error) {
        console.error('Failed to load lesson:', error)
        const message = error.response?.data?.error || t('lesson.notifications.loadingFailed')
        window.showNotification(message, 'error')
      } finally {
        loading.value = false
        window.setLoading(false)
      }
    }

    const onCodeChange = (exerciseId, newCode) => {
      // Update the code
      codes.value[exerciseId] = newCode
      
      // 如果用户修改了代码，并且当前没有在计时，则开始新的计时
      if (!hasStartedCoding.value[exerciseId]) {
        const placeholder = `# ${t('lesson.placeholder').replace('...', '')}\n`
        console.log(`DEBUG: 检查代码变化 - 新代码长度: ${newCode.trim().length}, 占位符: "${placeholder.trim()}", 新代码: "${newCode.trim()}"`)
        
        // 检查是否开始输入实际代码（不只是默认占位符）
        if (newCode.trim().length > 0 && newCode.trim() !== placeholder.trim()) {
          startTimes.value[exerciseId] = new Date()
          hasStartedCoding.value[exerciseId] = true
          startTimer(exerciseId)
          console.log(`✅ 开始做题时间: ${startTimes.value[exerciseId]}`)
        } else {
          console.log(`❌ 代码未变化或仍为占位符，不开始计时`)
        }
      } else {
        console.log(`⏰ 已在计时中，当前计时: ${currentTimes.value[exerciseId]}秒`)
      }
    }

    const startTimer = (exerciseId) => {
      // 清除可能存在的旧计时器
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
      }
      
      // 启动新计时器，每秒更新一次
      timers.value[exerciseId] = setInterval(() => {
        if (startTimes.value[exerciseId]) {
          const elapsed = Math.floor((Date.now() - startTimes.value[exerciseId].getTime()) / 1000)
          currentTimes.value[exerciseId] = elapsed
        }
      }, 1000)
    }

    const stopTimer = (exerciseId) => {
      if (timers.value[exerciseId]) {
        clearInterval(timers.value[exerciseId])
        timers.value[exerciseId] = null
      }
    }

    const formatTimerTime = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const runCode = async (exercise) => {
      const code = codes.value[exercise.exercise_id]
      if (!code.trim()) {
        window.showNotification(t('lesson.notifications.enterCodeFirst'), 'warning')
        return
      }

      // 点击提交时立即停止计时并计算耗时
      let solvingTimeSec = 0
      const startTime = startTimes.value[exercise.exercise_id]
      if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
        solvingTimeSec = Math.round((Date.now() - startTime.getTime()) / 1000)
        console.log(`本次做题耗时: ${solvingTimeSec}秒`)
        
        // 立即停止计时器
        stopTimer(exercise.exercise_id)
      }

      try {
        codeRunning.value = true
        
        // 准备提交数据
        const submissionData = {
          exercise_id: exercise.exercise_id,
          code: code
        }
        
        // 如果有计时数据，添加到提交中（包括0秒的情况）
        if (startTime && hasStartedCoding.value[exercise.exercise_id]) {
          submissionData.solving_time_sec = solvingTimeSec
          console.log(`传递给后端的做题耗时: ${solvingTimeSec}秒`)
        }
        
        const response = await submissionAPI.submitCode(submissionData)
        
        // Backend returns {submission: {...}, result: {...}}
        const result = response.result
        result.submission = response.submission // Add submission info for analysis display
        results.value[exercise.exercise_id] = result
        
        // 根据结果处理计时状态
        if (result.is_correct) {
          // 如果答案正确，彻底停止计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          window.showNotification(t('lesson.notifications.codeExecutedCorrectly'), 'success')
        } else {
          // 如果答案错误，重置计时状态，等待用户修改代码时重新开始计时
          hasStartedCoding.value[exercise.exercise_id] = false
          currentTimes.value[exercise.exercise_id] = 0
          startTimes.value[exercise.exercise_id] = null
          window.showNotification(t('lesson.notifications.codeNeedsModification'), 'warning')
        }
        
      } catch (error) {
        console.error('Code execution failed:', error)
        window.showNotification(t('lesson.notifications.executionFailed'), 'error')
        
        // 出错时也重置计时状态
        hasStartedCoding.value[exercise.exercise_id] = false
        currentTimes.value[exercise.exercise_id] = 0
        startTimes.value[exercise.exercise_id] = null
      } finally {
        codeRunning.value = false
      }
    }

    const canShowHints = (exercise) => {
      // Check if there are incorrect results or lesson progress indicates hints should be shown
      const result = results.value[exercise.exercise_id]
      return (result && !result.is_correct) || 
             lesson.value?.progress?.should_show_hint ||
             (result && result.hints_available)
    }

    const requestHint = async (exerciseId) => {
      try {
        hintLoading.value = true
        
        const currentHints = hints.value[exerciseId] || []
        const nextHintLevel = currentHints.length + 1
        
        const data = await submissionAPI.requestHint(exerciseId, nextHintLevel)
        
        if (!hints.value[exerciseId]) {
          hints.value[exerciseId] = []
        }
        hints.value[exerciseId].push(data.hint)
        
        window.showNotification(t('lesson.notifications.hintObtained', { level: data.hint_level, hint: data.hint }), 'info')
      } catch (error) {
        console.error('Failed to get hint:', error)
        const message = error.response?.data?.error || t('lesson.notifications.hintRequestFailed')
        window.showNotification(message, 'error')
      } finally {
        hintLoading.value = false
      }
    }

    const getStatusText = (status) => {
      return t(`lesson.statusLabels.${status}`) || t('lesson.statusLabels.unknown')
    }

    const toggleTheme = () => {
      isDarkTheme.value = !isDarkTheme.value
      // Save theme preference to localStorage
      localStorage.setItem('codeEditorTheme', isDarkTheme.value ? 'dark' : 'light')
    }

    const toggleHistory = async (exerciseId) => {
      if (histories.value[exerciseId]) {
        // Already loaded, toggle display
        histories.value[exerciseId] = null
      } else {
        // First time loading
        try {
          historyLoading.value = true
          const response = await submissionAPI.getSubmissionHistory(exerciseId)
          histories.value[exerciseId] = response.submissions
          window.showNotification(t('lesson.notifications.historyLoaded'), 'success')
        } catch (error) {
          console.error('Failed to get submission history:', error)
          const message = error.response?.data?.error || t('lesson.notifications.historyLoadFailed')
          window.showNotification(message, 'error')
        } finally {
          historyLoading.value = false
        }
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleString()
    }

    const showSubmissionCode = (submission) => {
      selectedSubmission.value = submission
      showCodeModal.value = true
    }

    const closeCodeModal = () => {
      showCodeModal.value = false
      selectedSubmission.value = null
    }

    const copyToCurrentEditor = () => {
      if (selectedSubmission.value?.code) {
        // Find the exercise that this submission belongs to
        const exerciseId = selectedSubmission.value.exercise_id
        if (exerciseId && codes.value[exerciseId] !== undefined) {
          codes.value[exerciseId] = selectedSubmission.value.code
          window.showNotification(t('lesson.notifications.codeCopiedToEditor'), 'success')
          closeCodeModal()
        }
      }
    }

    // Code quality analysis related methods
    const getScoreClass = (score) => {
      if (score >= 8) return 'excellent'
      if (score >= 6) return 'good'
      if (score >= 4) return 'fair'
      return 'poor'
    }

    const getAnalysisSuggestions = (analysisResult) => {
      if (!analysisResult) return []
      
      try {
        const analysis = typeof analysisResult === 'string' ? JSON.parse(analysisResult) : analysisResult
        const suggestions = []
        
        // 🔧 修复：优先使用新的suggestions_list字段
        if (analysis.summary?.suggestions_list) {
          suggestions.push(...analysis.summary.suggestions_list)
        } else {
          // 回退到原有逻辑
          // Extract suggestions from local analysis
          if (analysis.local_analysis?.quality_issues) {
            suggestions.push(...analysis.local_analysis.quality_issues)
          }
          
          // Extract suggestions from API analysis
          if (analysis.api_analysis) {
            Object.values(analysis.api_analysis).forEach(apiResult => {
              if (apiResult && typeof apiResult === 'object') {
                // 原有的suggestions字段
                if (apiResult.suggestions) {
                  suggestions.push(...apiResult.suggestions)
                }
                
                // 🆕 新增：从AI反馈中提取建议
                if (apiResult.feedback) {
                  const feedback = apiResult.feedback
                  if (typeof feedback === 'object') {
                    // OpenAI风格的反馈
                    if (feedback.learning_suggestions) {
                      suggestions.push(feedback.learning_suggestions)
                    }
                    if (feedback.guiding_hints) {
                      suggestions.push(feedback.guiding_hints)
                    }
                    
                    // DeepSeek风格的反馈
                    if (feedback.hints_not_answers) {
                      suggestions.push(feedback.hints_not_answers)
                    }
                    if (feedback.next_steps) {
                      suggestions.push(feedback.next_steps)
                    }
                  }
                }
              }
            })
          }
        }
        
        // 过滤空值和重复项
        return [...new Set(suggestions.filter(s => s && s.trim()))]
          .slice(0, 5) // 增加显示数量到5个
      } catch (error) {
        console.warn('Failed to parse analysis result:', error)
        return ['分析结果解析失败，请查看详细反馈信息']
      }
    }

    // 🔥 NEW: Feedback comparison related methods
    const deepseekLoading = ref({});

    const toggleFeedbackMode = async (exerciseId) => {
      const currentMode = feedbackModes.value[exerciseId];
      const newMode = currentMode === 'simple' ? 'detailed' : 'simple';
      feedbackModes.value[exerciseId] = newMode;

      // If switching to detailed mode and DeepSeek data hasn't loaded yet, trigger loading
      if (newMode === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
        deepseekLoading.value[exerciseId] = true;
        try {
          const submissionId = results.value[exerciseId].submission.submission_id;
          const response = await submissionAPI.requestAdditionalFeedback(submissionId, 'deepseek');
          
          // Merge new feedback data into existing results
          const newFeedbackData = response.feedback_content;
          const comprehensiveFeedback = results.value[exerciseId].comprehensive_feedback.comprehensive_feedback || results.value[exerciseId].comprehensive_feedback;
          
          comprehensiveFeedback.ai_generated.deepseek = {
              feedback: JSON.parse(newFeedbackData.feedback_content),
              generation_time: newFeedbackData.generation_time_ms,
              cost: newFeedbackData.api_cost,
              source: 'deepseek_educational',
              feedback_id: newFeedbackData.feedback_id
          };

          // Update UI
          window.showNotification(t('lesson.notifications.deepseekFeedbackLoaded'), 'success');
        } catch (error) {
          console.error('Failed to load DeepSeek feedback:', error);
          window.showNotification(t('lesson.notifications.deepseekFeedbackFailed'), 'error');
        } finally {
          deepseekLoading.value[exerciseId] = false;
        }
      }

      // Set default active tab
      if (newMode === 'detailed') {
        const result = results.value[exerciseId];
        if (result?.comprehensive_feedback?.recommended_primary) {
          activeFeedbackTabs.value[exerciseId] = result.comprehensive_feedback.recommended_primary;
        }
      }
    };

    const isDeepSeekDataAvailable = (exerciseId) => {
        const result = results.value[exerciseId];
        const feedbacks = result?.comprehensive_feedback?.comprehensive_feedback?.ai_generated || {};
        return feedbacks.deepseek && !feedbacks.deepseek.error;
    };

    const getAvailableFeedbacks = (exerciseId) => {
      const result = results.value[exerciseId];
      if (!result?.comprehensive_feedback) return {};
      
      const feedbacks = {};
      const comprehensiveFeedback = result.comprehensive_feedback.comprehensive_feedback || result.comprehensive_feedback;
      
      // Add structural feedback
      feedbacks['structural'] = {
        feedback: comprehensiveFeedback.structural,
        generation_time: 0,
        cost: 0
      };
      
      // Add existing AI feedback
      const aiFeedbacks = comprehensiveFeedback.ai_generated || {};
      Object.keys(aiFeedbacks).forEach(apiName => {
        feedbacks[apiName] = aiFeedbacks[apiName];
      });

      // If DeepSeek should be shown but data is not yet available, add a placeholder
      if (feedbackModes.value[exerciseId] === 'detailed' && !isDeepSeekDataAvailable(exerciseId)) {
          feedbacks['deepseek'] = { placeholder: true, loading: deepseekLoading.value[exerciseId] };
      }
      
      return feedbacks;
    };

    const getFeedbackTypeLabel = (feedbackType) => {
      return t(`lesson.feedbackTypeLabels.${feedbackType}`) || feedbackType
    }

    const getFeedbackComponent = (feedbackType) => {
      // Return different component names based on feedback type
      if (feedbackType === 'structural') {
        return 'StructuralFeedback'
      } else if (feedbackType.includes('openai')) {
        return 'AIFeedback'
      } else if (feedbackType.includes('deepseek')) {
        return 'AIFeedback'
      }
      return 'BasicFeedback'
    }

    const getActiveFeedbackData = (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId)
      const activeType = activeFeedbackTabs.value[exerciseId]
      return feedbacks[activeType] || {}
    }

    const getStageLabel = (stage) => {
      return t(`lesson.stageLabels.${stage}`) || t('lesson.stageLabels.unknown')
    }

    const feedbackSourceInfo = computed(() => (exerciseId) => {
      const feedbacks = getAvailableFeedbacks(exerciseId);
      const sources = Object.keys(feedbacks).filter(key => !feedbacks[key].placeholder);
      const sourceNames = sources.map(s => {
          if (s === 'structural') return 'Judge0';
          if (s.includes('openai')) return 'OpenAI';
          if (s.includes('deepseek')) return 'DeepSeek';
          return 'Other';
      });
      return {
          count: sources.length,
          names: `(${sourceNames.join(', ')})`
      };
    });

    const handleFeedbackRating = async (data) => {
      try {
        // Submit feedback rating
        await submissionAPI.rateFeedback(data.feedback_id, data.helpfulness_rating, data.clarity_rating)
        window.showNotification(t('lesson.notifications.feedbackRatingSuccess'), 'success')
      } catch (error) {
        console.error('Failed to submit rating:', error)
        window.showNotification(t('lesson.notifications.feedbackRatingFailed'), 'error')
      }
    }

    // Restore theme preference from localStorage
    const savedTheme = localStorage.getItem('codeEditorTheme')
    if (savedTheme) {
      isDarkTheme.value = savedTheme === 'dark'
    }

    onMounted(() => {
      loadLesson()
    })

    // 组件卸载时清理所有计时器
    onUnmounted(() => {
      Object.keys(timers.value).forEach(exerciseId => {
        if (timers.value[exerciseId]) {
          clearInterval(timers.value[exerciseId])
        }
      })
    })

    return {
      t,
      lesson,
      loading,
      codeRunning,
      hintLoading,
      isDarkTheme,
      codes,
      results,
      hints,
      // NEW: Submission history functionality
      histories,
      historyLoading,
      toggleHistory,
      formatTime,
      // NEW: Code modal functionality
      showCodeModal,
      selectedSubmission,
      showSubmissionCode,
      closeCodeModal,
      copyToCurrentEditor,
      renderedContent,
      allExercisesCompleted,
      loadLesson,
      onCodeChange,
      runCode,
      canShowHints,
      requestHint,
      getStatusText,
      toggleTheme,
      getScoreClass,
      getAnalysisSuggestions,
      // 🔥 NEW: Feedback comparison functionality
      feedbackModes,
      activeFeedbackTabs,
      toggleFeedbackMode,
      getAvailableFeedbacks,
      getFeedbackTypeLabel,
      getFeedbackComponent,
      getActiveFeedbackData,
      getStageLabel,
      handleFeedbackRating,
      deepseekLoading,
      feedbackSourceInfo,
      // 🔥 NEW: Time tracking functionality
      startTimes,
      hasStartedCoding,
      currentTimes,
      formatTimerTime
    }
  }
}
</script>

<style scoped>
.lesson-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.lesson-nav {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
}

.lesson-title h1 {
  color: #2c3e50;
  margin: 0;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
}

.progress-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.progress-badge.not_started {
  background: #e9ecef;
  color: #6c757d;
}

.progress-badge.in_progress {
  background: #fff3cd;
  color: #856404;
}

.progress-badge.completed {
  background: #d4edda;
  color: #155724;
}

.attempts-text {
  font-size: 14px;
  color: #6c757d;
}

.avg-time-text {
  font-size: 14px;
  color: #007bff;
  font-weight: 500;
  background: rgba(0, 123, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(0, 123, 255, 0.2);
}

.lesson-content {
  display: grid;
  gap: 30px;
}

.content-section {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.markdown-content {
  line-height: 1.8;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
  color: #2c3e50;
  margin-top: 30px;
  margin-bottom: 16px;
}

.markdown-content h1:first-child {
  margin-top: 0;
}

.markdown-content pre {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #3498db;
  overflow-x: auto;
}

.markdown-content code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
}

.exercises-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.exercise-card {
  background: white;
  border-radius: 10px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.exercise-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 20px;
}

.exercise-header h3 {
  color: #2c3e50;
  margin: 0;
}

.problem-statement {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  margin-bottom: 20px;
}

.hints-section {
  background: #fff3cd;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
  margin-bottom: 20px;
}

.hints-section h4 {
  color: #856404;
  margin-bottom: 12px;
}

.hint-item {
  margin-bottom: 8px;
  color: #856404;
}

/* Submission History Styles */
.history-section {
  background: #e7f3ff;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  margin-bottom: 20px;
}

.history-section h4 {
  color: #004085;
  margin-bottom: 12px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  background: white;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid #6c757d;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.history-item.clickable {
  cursor: pointer;
}

.history-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.history-item.clickable:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
  background: linear-gradient(to right, #f8f9fa, white);
}

.history-item.success {
  border-left-color: #28a745;
  background: linear-gradient(to right, #d4edda, white);
}

.history-time {
  color: #6c757d;
  font-size: 12px;
  min-width: 120px;
}

.history-status {
  font-size: 16px;
  min-width: 24px;
}

.history-attempts {
  color: #495057;
  font-weight: 500;
  min-width: 80px;
}

.history-error {
  color: #dc3545;
  font-size: 12px;
  font-style: italic;
  opacity: 0.8;
}

.exercise-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.history-hint {
  margin-left: auto;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.history-item.clickable:hover .history-hint {
  opacity: 1;
}

/* Code Modal Styles */
.code-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.code-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.code-modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.code-modal-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.modal-time {
  color: #6c757d;
  font-size: 14px;
}

.modal-status {
  font-weight: 500;
}

.modal-error {
  color: #dc3545;
  font-size: 14px;
  font-style: italic;
}

.code-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  min-height: 200px;
  max-height: 400px;
}

.code-modal-content pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
}

.code-modal-content code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  color: inherit;
}

.code-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: #f8f9fa;
}

/* Responsive design for modal */
@media (max-width: 768px) {
  .code-modal {
    width: 95vw;
    max-height: 85vh;
  }
  
  .code-modal-header,
  .code-modal-info,
  .code-modal-content,
  .code-modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .code-modal-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.code-editor-section {
  margin-bottom: 20px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2c3e50;
  color: white;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.coding-timer {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #ecf0f1;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: pulse 2s infinite;
  backdrop-filter: blur(4px);
}

.timer-icon {
  font-size: 16px;
}

.timer-text {
  font-weight: 600;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.02);
  }
}

.editor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.code-editor {
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.result-section {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.result-header {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.result-header.success {
  background: #d4edda;
  color: #155724;
}

.result-header.error {
  background: #f8d7da;
  color: #721c24;
}

.result-icon {
  font-size: 20px;
}

.feedback-message {
  padding: 16px;
  background: white;
  border-left: 4px solid #dee2e6;
}

.error-details {
  background: #f8f9fa;
  padding: 16px;
}

.error-message, .output-section, .expected-output {
  margin-bottom: 16px;
}

.error-details pre {
  background: white;
  padding: 12px;
  border-radius: 4px;
  margin-top: 8px;
  white-space: pre-wrap;
  font-size: 14px;
}

.hint-reminder {
  background: #e7f3ff;
  color: #0056b3;
  padding: 12px 16px;
  border-radius: 6px;
  margin-top: 12px;
}

/* Code quality analysis styles */
.code-quality-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.code-quality-section h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
}

.quality-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-weight: 500;
  color: #495057;
}

.metric-value {
  font-weight: 600;
  color: #2c3e50;
}

.quality-score.excellent {
  color: #27ae60;
}

.quality-score.good {
  color: #2ecc71;
}

.quality-score.fair {
  color: #f39c12;
}

.quality-score.poor {
  color: #e74c3c;
}

/* 🔧 修复：独立的改进建议板块样式 */
.improvement-suggestions-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.improvement-suggestions-section h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  display: flex;
  align-items: center;
  font-size: 18px;
}

/* 移除标题前的💡，因为翻译文本中已包含 */

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.suggestion-text {
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  display: block;
  width: 100%;
}

.suggestion-icon {
  font-size: 16px;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.no-suggestions {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #6c757d;
  font-style: italic;
}

.test-cases-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.test-cases-section h4 {
  color: #2c3e50;
  margin-bottom: 12px;
}

.test-case {
  background: white;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid #3498db;
}

.completion-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  margin-top: 30px;
}

.completion-content h3 {
  margin-bottom: 16px;
}

.completion-content p {
  margin-bottom: 24px;
  opacity: 0.9;
}

.completion-content .btn {
  background: white;
  color: #667eea;
}

.loading-container, .error-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-container .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🔥 NEW: Feedback comparison functionality styles */
.feedback-controls {
  margin-left: auto;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comprehensive-feedback-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.feedback-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.feedback-tab {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.feedback-tab:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.feedback-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.feedback-tab.disabled {
  cursor: not-allowed;
  background: #f8f9fa;
  color: #6c757d;
}

.spinner-sm {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.6s linear infinite;
  margin-right: 5px;
}

.generation-time {
  font-size: 12px;
  opacity: 0.8;
  font-weight: normal;
}

.feedback-content-area {
  display: grid;
  gap: 16px;
}

.active-feedback-content {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #007bff;
}

.feedback-insights {
  background: #e7f3ff;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #17a2b8;
}

.feedback-insights h5 {
  margin: 0 0 12px 0;
  color: #0c5460;
}

.insights-content p {
  margin: 8px 0;
  font-size: 14px;
  color: #0c5460;
}

.insights-content strong {
  color: #495057;
}

.source-names {
    font-size: 13px;
    color: #6c757d;
    margin-left: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .feedback-tabs {
    flex-direction: column;
  }
  
  .feedback-tab {
    width: 100%;
    justify-content: center;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .feedback-controls {
    margin-left: 0;
    width: 100%;
  }
}
</style>