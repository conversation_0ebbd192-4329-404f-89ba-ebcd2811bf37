<template>
  <div class="home-container">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="logo">
          <h1>🐍 {{ $t('home.nav.brand') }}</h1>
        </div>
        <div class="nav-links">
          <router-link to="/login" class="nav-link">{{ $t('home.nav.studentLogin') }}</router-link>
          <router-link to="/register" class="nav-link">{{ $t('home.nav.register') }}</router-link>
          <router-link to="/admin/login" class="nav-link admin-link">{{ $t('home.nav.admin') }}</router-link>
          <LanguageSwitcher />
        </div>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">
          {{ $t('home.hero.title') }} <span class="highlight">{{ $t('home.hero.titleHighlight') }}</span> {{ $t('home.hero.titleEnd') }}
        </h1>
        <p class="hero-subtitle">
          {{ $t('home.hero.subtitle') }}<br>
          {{ $t('home.hero.subtitleLine2') }}
        </p>
        <div class="hero-buttons">
          <router-link to="/register" class="btn btn-primary">
            {{ $t('home.hero.startLearning') }}
          </router-link>
          <router-link to="/login" class="btn btn-secondary">
            {{ $t('home.hero.hasAccount') }}
          </router-link>
        </div>
      </div>
      <div class="hero-image">
        <div class="code-preview">
          <div class="code-header">
            <span class="dot red"></span>
            <span class="dot yellow"></span>
            <span class="dot green"></span>
          </div>
          <div class="code-content">
            <pre><code>{{ $t('home.codePreview.comment1') }}
print("Hello, World!")

{{ $t('home.codePreview.comment2') }}
name = "{{ $t('home.codePreview.learnerName') }}"
age = 25
print(f"{{ $t('home.codePreview.ageOutput', { name: '{name}', age: '{age}' }) }}")

{{ $t('home.codePreview.comment3') }}
if age >= 18:
    print("{{ $t('home.codePreview.adultMessage') }}")
else:
    print("{{ $t('home.codePreview.childMessage') }}")
</code></pre>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特色 -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">{{ $t('home.features.title') }}</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">💻</div>
            <h3>{{ $t('home.features.zeroSetup.title') }}</h3>
            <p>{{ $t('home.features.zeroSetup.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>{{ $t('home.features.instantFeedback.title') }}</h3>
            <p>{{ $t('home.features.instantFeedback.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>{{ $t('home.features.personalizedLearning.title') }}</h3>
            <p>{{ $t('home.features.personalizedLearning.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🏗️</div>
            <h3>{{ $t('home.features.progressiveTeaching.title') }}</h3>
            <p>{{ $t('home.features.progressiveTeaching.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💡</div>
            <h3>{{ $t('home.features.smartHints.title') }}</h3>
            <p>{{ $t('home.features.smartHints.description') }}</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>{{ $t('home.features.learningTracking.title') }}</h3>
            <p>{{ $t('home.features.learningTracking.description') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 学习路径 -->
    <section class="learning-path">
      <div class="container">
        <h2 class="section-title">{{ $t('home.learningPath.title') }}</h2>
        <div class="path-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step1.title') }}</h3>
              <p>{{ $t('home.learningPath.step1.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step2.title') }}</h3>
              <p>{{ $t('home.learningPath.step2.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step3.title') }}</h3>
              <p>{{ $t('home.learningPath.step3.description') }}</p>
            </div>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h3>{{ $t('home.learningPath.step4.title') }}</h3>
              <p>{{ $t('home.learningPath.step4.description') }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行动号召 -->
    <section class="cta">
      <div class="container">
        <h2>{{ $t('home.cta.title') }}</h2>
        <p>{{ $t('home.cta.subtitle') }}</p>
        <router-link to="/register" class="btn btn-primary btn-large">
          {{ $t('home.cta.registerNow') }}
        </router-link>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>🐍 {{ $t('home.footer.brand') }}</h3>
            <p>{{ $t('home.footer.brandSubtitle') }}</p>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.quickLinks') }}</h4>
            <ul>
              <li><router-link to="/login">{{ $t('home.footer.studentLogin') }}</router-link></li>
              <li><router-link to="/register">{{ $t('home.footer.registerAccount') }}</router-link></li>
              <li><router-link to="/admin/login">{{ $t('home.footer.adminLogin') }}</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ $t('home.footer.learningSupport') }}</h4>
            <ul>
              <li><a href="#">{{ $t('home.footer.userGuide') }}</a></li>
              <li><a href="#">{{ $t('home.footer.faq') }}</a></li>
              <li><a href="#">{{ $t('home.footer.contactUs') }}</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>{{ $t('home.footer.copyright') }}</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'

const { t } = useI18n()
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 导航栏 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #2c3e50;
}

.nav-links {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: #2c3e50;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.admin-link {
  background: #667eea;
  color: white;
}

.admin-link:hover {
  background: #5a67d8;
  color: white;
}

/* 英雄区域 */
.hero {
  padding: 80px 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-content {
  color: white;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 20px;
  line-height: 1.2;
}

.highlight {
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn {
  padding: 12px 30px;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: #ffd700;
  color: #2c3e50;
}

.btn-primary:hover {
  background: #ffed4a;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
}

.btn-large {
  padding: 16px 40px;
  font-size: 1.1rem;
}

/* 代码预览 */
.code-preview {
  background: #1e1e1e;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.code-header {
  background: #2d2d2d;
  padding: 12px 16px;
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.dot.red { background: #ff5f57; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #28ca42; }

.code-content {
  padding: 20px;
}

.code-content pre {
  margin: 0;
  color: #e6e6e6;
  font-family: 'Fira Code', monospace;
  line-height: 1.6;
}

/* 功能特色 */
.features {
  padding: 80px 20px;
  background: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.feature-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* 学习路径 */
.learning-path {
  padding: 80px 20px;
  background: #f8f9fa;
}

.path-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.step {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  position: relative;
}

.step-number {
  background: #667eea;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 20px;
}

.step-content h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

/* 行动号召 */
.cta {
  padding: 80px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  text-align: center;
  color: white;
}

.cta h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

/* 页脚 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 40px 20px 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #34495e;
  color: #bdc3c7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-buttons {
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .path-steps {
    grid-template-columns: 1fr;
  }
}
</style>