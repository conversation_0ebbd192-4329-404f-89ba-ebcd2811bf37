import { createI18n } from 'vue-i18n'
import en from './locales/en.js'
import zh from './locales/zh.js'

const messages = { en, zh }

// Detect browser language or use stored preference
function getDefaultLocale() {
  const stored = localStorage.getItem('preferred-language')
  if (stored && ['en', 'zh'].includes(stored)) {
    return stored
  }
  
  // For UK MSc project, default to English
  const browserLang = navigator.language.split('-')[0]
  return ['en', 'zh'].includes(browserLang) ? browserLang : 'en'
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'en',
  messages,
  globalInjection: true
})

export default i18n