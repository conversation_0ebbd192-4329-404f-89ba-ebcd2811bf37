export default {
  // Page titles
  title: {
    main: '交互式Python学习平台',
    home: '首页 - Python学习平台',
    login: '登录 - Python学习平台',
    register: '注册 - Python学习平台',
    dashboard: '仪表板 - Python学习平台',
    modules: '课程模块 - Python学习平台',
    lesson: '课程学习 - Python学习平台',
    admin: '管理后台 - Python学习平台',
    analytics: '数据分析 - Python学习平台'
  },

  // Navigation & Common
  navigation: {
    brand: 'Python学习平台',
    dashboard: '仪表板',
    courses: '课程',
    admin: '管理后台',
    research: '研究分析',
    logout: '退出登录',
    home: '首页'
  },

  // Common UI elements
  common: {
    loading: '加载中...',
    submit: '提交',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    update: '更新',
    confirm: '确认',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '关闭',
    search: '搜索',
    filter: '筛选',
    clear: '清除',
    refresh: '刷新',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息'
  },

  // Home page
  home: {
    title: '交互式Python学习平台',
    subtitle: '通过AI指导和实时反馈掌握Python编程',
    nav: {
      brand: 'Python学习平台',
      studentLogin: '学生登录',
      register: '注册',
      admin: '管理员'
    },
    hero: {
      title: '开始你的',
      titleHighlight: 'Python编程',
      titleEnd: '之旅',
      subtitle: '专为编程初学者设计的在线Python学习平台',
      subtitleLine2: '无需配置环境，即写即运行，获得即时反馈',
      startLearning: '免费开始学习',
      hasAccount: '已有账户？登录'
    },
    codePreview: {
      comment1: '# 你的第一个Python程序',
      comment2: '# 变量和数据类型',
      learnerName: 'Python学习者',
      ageOutput: '我是{name}，今年{age}岁',
      comment3: '# 条件语句',
      adultMessage: '我已经成年了！',
      childMessage: '我还是个孩子'
    },
    features: {
      title: '为什么选择我们的平台？',
      zeroSetup: {
        title: '零环境配置',
        description: '无需安装Python或任何开发工具，打开浏览器即可开始编程学习。'
      },
      instantFeedback: {
        title: '即时反馈',
        description: '代码运行结果和错误提示立即显示，帮助你快速找到问题并改正。'
      },
      personalizedLearning: {
        title: '个性化学习',
        description: '根据你的学习进度和能力，系统会推荐适合的课程内容和练习。'
      },
      progressiveTeaching: {
        title: '渐进式教学',
        description: '从基础语法到高级概念，循序渐进的课程设计让学习更加轻松。'
      },
      smartHints: {
        title: '智能提示',
        description: '当你遇到困难时，系统会提供分层次的提示，引导你找到解决方案。'
      },
      learningTracking: {
        title: '学习追踪',
        description: '详细的学习进度统计，让你清楚了解自己的学习成果。'
      }
    },
    learningPath: {
      title: '学习路径',
      step1: {
        title: '编程基础',
        description: '了解什么是编程，学习Python基本语法和数据类型'
      },
      step2: {
        title: '数据处理',
        description: '学习字符串、数字运算和用户输入处理'
      },
      step3: {
        title: '逻辑控制',
        description: '掌握条件语句、循环结构和列表操作'
      },
      step4: {
        title: '代码组织',
        description: '学习函数定义、参数传递和返回值'
      }
    },
    cta: {
      title: '准备开始你的编程之旅吗？',
      subtitle: '加入我们，开始免费的Python学习体验',
      registerNow: '立即注册'
    },
    footer: {
      brand: 'Python学习平台',
      brandSubtitle: '专为编程初学者设计的在线学习平台',
      quickLinks: '快速链接',
      studentLogin: '学生登录',
      registerAccount: '注册账户',
      adminLogin: '管理员登录',
      learningSupport: '学习支持',
      userGuide: '使用指南',
      faq: '常见问题',
      contactUs: '联系我们',
      copyright: '© 2024 Python学习平台. 保留所有权利.'
    }
  },

  // Authentication
  auth: {
    login: '登录',
    register: '注册',
    username: '用户名',
    password: '密码',
    email: '邮箱',
    confirmPassword: '确认密码',
    loginTitle: '欢迎回来',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    loggingIn: '登录中...',
    loginError: '登录失败，请检查用户名和密码',
    emailPlaceholder: '请输入邮箱地址',
    confirmPasswordPlaceholder: '请再次输入密码',
    registering: '注册中...',
    registerError: '注册失败，请稍后重试',
    loginSubtitle: '登录以继续你的Python学习之旅',
    registerTitle: '加入我们的社区',
    registerSubtitle: '创建账户开始学习Python',
    loginButton: '登录',
    registerButton: '创建账户',
    forgotPassword: '忘记密码？',
    noAccount: '还没有账户？',
    haveAccount: '已有账户？',
    loginSuccess: '登录成功！',
    registerSuccess: '注册成功！欢迎加入Python学习之旅！',
    logoutSuccess: '已成功退出登录',
    invalidCredentials: '用户名或密码无效',
    passwordMismatch: '两次输入的密码不一致',
    usernameTaken: '用户名已被占用',
    emailTaken: '邮箱已被注册'
  },

  // Dashboard
  dashboard: {
    title: '学习仪表板',
    welcome: '欢迎回来，{username}！继续你的Python学习之旅吧！',
    logout: '注销',
    stats: {
      totalLessons: '总课程数',
      completedLessons: '已完成',
      inProgressLessons: '进行中',
      completionRate: '完成率',
      averageScore: '平均分数',
      hintsUsed: '使用提示',
      exercisesNeedingHints: '{count} 个练习需要提示',
      timeSpent: '学习时间',
      totalSubmissions: '提交次数',
      successRate: '成功率'
    },
    progress: {
      title: '整体学习进度',
      lessonsCompleted: '{completed} / {total} 课程已完成'
    },
    recentActivity: {
      title: '最近活动',
      noActivity: '暂无最近活动',
      completed: '完成了',
      started: '开始了',
      submitted: '提交了代码',
      exerciseCompleted: '成功完成练习',
      exerciseAttempt: '练习尝试'
    },
    codeQuality: {
      title: '代码质量分析',
      averageLines: '平均代码行数',
      complexity: '平均复杂度',
      syntaxScore: '代码质量评分',
      recentImprovement: '最近改进'
    },
    hintAnalysis: {
      title: '提示使用分析',
      totalHints: '总提示次数',
      exercisesWithHints: '需要提示的练习',
      level: '级别',
      mostUsedLevel: '最常用级别',
      recentTrend: '近期趋势',
      levelDistribution: '提示级别分布'
    },
    errorPatterns: {
      title: '学习分析',
      detailedErrorAnalysis: '详细错误分析',
      syntaxError: '语法错误',
      nameError: '名称错误',
      typeError: '类型错误',
      runtimeError: '运行时错误',
      logicError: '逻辑错误',
      timeoutError: '超时错误',
      noError: '无错误',
      times: '次'
    },
    quickActions: {
      title: '快速操作',
      continueLastLesson: '继续学习',
      browseModules: '浏览课程',
      viewProgress: '查看进度',
      refreshData: '刷新数据'
    },
    confirmLogout: '确定要注销吗？',
    loadDashboardError: '加载仪表板数据失败',
    loadCodeAnalysisError: '加载代码分析数据失败'
  },

  // 🔬 NEW: Research Analytics 研究分析页面
  research: {
    title: '教育技术研究分析',
    subtitle: '基于智能辅导系统的AI反馈效果对比研究',
    academicBadge: 'MSc Computer Science 毕业设计研究',
    loading: '正在加载研究数据...',
    error: '加载研究数据失败',
    loadError: '无法连接到研究数据API',
    retry: '重试',
    
    overview: {
      title: '研究数据概览',
      totalFeedback: '反馈实例总数',
      totalRecords: '效果追踪记录',
      successfulCases: '成功案例',
      successRate: '整体成功率'
    },
    
    question: {
      title: '核心研究问题',
      main: '研究问题:',
      methodology: '研究方法:',
      period: '数据收集期:'
    },
    
    matrix: {
      title: 'AI反馈效果对比矩阵',
      description: '以下矩阵展示了不同AI反馈类型在处理各种编程错误时的平均成功时间、尝试次数和用户评分。这是本研究的核心数据可视化。',
      attempts: '次尝试',
      noData: '暂无数据',
      legend: '图例 (按平均成功时间分类)',
      excellent: '优秀',
      good: '良好', 
      moderate: '中等',
      poor: '较差'
    },
    
    feedbackTypes: {
      structural: '技术分析反馈',
      openai: 'OpenAI教育反馈',
      deepseek: 'DeepSeek教育反馈',
      localRules: '本地规则反馈'
    },
    
    errorTypes: {
      syntax: '语法错误',
      name: '名称错误',
      type: '类型错误',
      logic: '逻辑错误',
      runtime: '运行时错误',
      noError: '无错误'
    },
    
    insights: {
      title: '研究发现与学术价值',
      dataIntegrity: '数据完整性',
      dataIntegrityDesc: '系统成功实现了从反馈生成到效果追踪的完整数据闭环，为教育技术研究提供了可靠的数据基础。',
      feedbackComparison: '反馈对比分析',
      feedbackComparisonDesc: '通过定量分析不同AI模型的反馈效果，本研究为智能辅导系统的反馈策略优化提供了实证依据。',
      userExperience: '用户体验评估',
      userExperienceDesc: '结合定量数据和用户主观评分，形成了多维度的反馈效果评估框架，提升了研究的学术严谨性。'
    },
    
    export: {
      title: '数据导出与学术引用',
      csv: '导出CSV格式',
      json: '导出JSON格式',
      citation: '生成引用格式',
      citationFormat: '建议引用格式:'
    }
  },

  // Modules (Courses)
  modules: {
    title: 'Python课程模块',
    subtitle: '选择一个模块开始学习，按照顺序完成课程可以获得最佳学习效果',
    progress: '进度',
    lessons: '课程',
    exercises: '练习',
    startModule: '开始模块',
    continueModule: '继续模块',
    moduleCompleted: '模块已完成！',
    lessonsCompleted: '课程完成',
    prerequisiteRequired: '需要完成前置课程',
    loading: '加载课程中...',
    emptyTitle: '暂无课程内容',
    emptyDescription: '课程内容正在准备中，请稍后再试',
    reload: '重新加载',
    loadError: '加载课程失败，请稍后重试',
    prerequisiteWarning: '请先完成前置课程',
    progressStatus: {
      notStarted: '未开始',
      inProgress: '进行中 ({attempts} 次尝试)',
      completed: '已完成',
      unknown: '未知状态'
    },
    lessonStatus: {
      locked: '已锁定',
      available: '可学习',
      inProgress: '进行中',
      completed: '已完成'
    },
    noModules: '暂无可用模块'
  },

  // Lesson
  lesson: {
    title: '课程',
    exercise: '练习',
    content: '内容',
    practice: '实践',
    runCode: '运行代码',
    submitCode: '提交代码',
    resetCode: '重置代码',
    getHint: '获取提示',
    showHints: '显示提示',
    hideHints: '隐藏提示',
    testCases: '测试用例',
    expectedOutput: '期望输出',
    yourOutput: '你的输出',
    feedback: '反馈',
    hints: '提示',
    problemStatement: '问题描述',
    instructions: '说明',
    codeEditor: '代码编辑器',
    placeholder: '在这里输入你的Python代码...',
    toggleTheme: '切换主题',
    lightTheme: '浅色主题',
    darkTheme: '深色主题',
    simpleView: '简单视图',
    detailedView: '详细视图',
    runSuccess: '代码执行成功！',
    submitSuccess: '代码提交成功！',
    lessonCompleted: '课程已完成！做得很好！',
    nextLesson: '下一课程',
    previousLesson: '上一课程',
    backToModules: '返回模块',
    codeAnalysis: '代码分析',
    executionTime: '执行时间',
    codeLines: '代码行数',
    complexity: '复杂度',
    // New keys for Lesson.vue
    backToCourseList: '← 返回课程列表',
    attemptCount: '第 {count} 次尝试',
    avgSolvingTime: '平均做题时间',
    programmingExercises: '💻 编程练习',
    exerciseNumber: '练习 {number}',
    hintNumber: '提示 {number}：',
    pythonCodeEditor: 'Python 代码编辑器',
    running: '运行中...',
    codeCorrect: '太棒了！代码运行正确！',
    codeNeedsImprovement: '代码需要修改',
    viewDetailedComparison: '查看详细反馈对比',
    viewComparison: '详细辅导',
    simpleMode: '简单解释',
    errorMessage: '错误信息：',
    codeQualityAnalysis: '📊 代码质量分析',
    linesOfCode: '代码行数：',
    qualityScore: '质量评分：',
    improvementSuggestions: '💡 改进建议',
    noSuggestionsAvailable: '暂无改进建议',
    input: '输入：',
    noInput: '(无输入)',
    congratulationsCompleted: '🎉 恭喜完成本课程！',
    masteredAllConcepts: '你已经成功掌握了本课程的所有知识点，可以继续下一个课程了。',
    continueToNextCourse: '继续学习下一课程',
    loadingContent: '加载课程内容中...',
    loadFailed: '😞 课程加载失败',
    loadFailedMessage: '无法加载课程内容，请检查网络连接或稍后重试。',
    reload: '重新加载',
    hintReminder: '💡 多次尝试后可以获取提示来帮助解决问题',
    viewHistory: '查看历史',
    submissionHistory: '📝 提交历史',
    attempt: '第{number}次尝试',
    clickToViewCode: '点击查看提交的代码',
    submissionCodeTitle: '📝 提交代码详情',
    success: '成功',
    failed: '失败',
    noCodeAvailable: '暂无代码可用',
    copyToEditor: '📋 复制到编辑器',
    feedbackComparison: '💡 反馈对比分析',
    feedbackSources: '反馈来源数量：',
    recommendedPrimaryFeedback: '推荐优先查看：',
    learningStage: '学习阶段：',
    statusLabels: {
      not_started: '未开始',
      in_progress: '进行中',
      completed: '已完成',
      unknown: '未知'
    },
    stageLabels: {
      advanced_beginner: '🚀 快速学习者',
      typical_learner: '📚 正常学习者',
      needs_support: '🤝 需要支持',
      unknown: '❓ 未知阶段'
    },
    feedbackTypeLabels: {
      structural: '🔧 技术分析反馈',
      openai: '🤖 OpenAI导师',
      deepseek: '🚀 DeepSeek导师',
      local_rules: '📋 本地规则',
      openai_educational: '🤖 OpenAI导师',
      deepseek_educational: '🚀 DeepSeek导师'
    },
    errorTypes: {
      syntax_error: '语法错误',
      name_error: '名称错误',
      type_error: '类型错误',
      runtime_error: '运行时错误',
      logic_error: '逻辑错误',
      timeout_error: '超时错误',
      no_error: '无错误'
    },
    notifications: {
      enterCodeFirst: '请先输入代码',
      executionFailed: '代码执行失败，请稍后重试',
      codeExecutedCorrectly: '🎉 代码运行正确！',
      codeNeedsModification: '代码需要修改，请查看反馈',
      hintObtained: '获得提示 {level}：{hint}',
      hintRequestFailed: '获取提示失败',
      deepseekFeedbackLoaded: 'DeepSeek导师反馈已加载',
      deepseekFeedbackFailed: '加载DeepSeek反馈失败',
      feedbackRatingSuccess: '感谢您的评分！这将帮助我们改进反馈质量。',
      feedbackRatingFailed: '评分提交失败，请稍后重试',
      loadingFailed: '加载课程失败',
      historyLoaded: '📝 提交历史已加载',
      historyLoadFailed: '获取提交历史失败',
      codeCopiedToEditor: '📋 代码已复制到编辑器'
    }
  },

  // Feedback
  feedback: {
    title: '反馈',
    structural: {
      title: '技术分析反馈',
      subtitle: '关于你代码的详细技术反馈'
    },
    ai: {
      title: 'AI导师反馈',
      subtitle: '来自AI导师的教育性指导'
    },
    encouragement: '鼓励',
    problemAnalysis: '问题分析',
    conceptExplanation: '概念解释',
    learningHints: '学习提示',
    suggestions: '学习建议',
    technicalDetails: '技术详情',
    noContent: '暂无反馈内容',
    rating: {
      title: '这个反馈对您有帮助吗？',
      helpfulness: '有用性：',
      clarity: '清晰度：',
      submitRating: '提交评分',
      thankYou: '感谢您的反馈！'
    },
    comparison: {
      title: '反馈对比',
      openai: 'OpenAI GPT-4',
      deepseek: 'DeepSeek',
      structural: '技术分析'
    },
    requestMore: '请求额外反馈',
    generationTime: '生成时间',
    cost: '成本'
  },

  // Admin section
  admin: {
    title: '管理面板',
    dashboard: '管理仪表板',
    modules: '管理模块',
    lessons: '管理课程',
    exercises: '管理练习',
    users: '管理用户',
    analytics: '分析',
    login: {
      title: '管理员登录',
      subtitle: '访问管理面板',
      platformSubtitle: 'Python学习平台 - 后台管理系统',
      usernamePlaceholder: '请输入管理员用户名',
      studentLogin: '学生端登录',
      adminOnlyError: '只有管理员可以访问后台系统'
    },
    dashboard: {
      title: '管理员仪表板',
      subtitle: 'Python学习平台管理中心',
      contentManagement: '内容管理',
      contentManagementDesc: '管理课程模块、课程内容和练习题',
      userManagement: '用户管理',
      userManagementDesc: '查看学生信息和学习进度',
      learningAnalytics: '学习分析',
      recentActivity: '最近活动',
      monthlyNew: '本月新增',
      modules: '个模块',
      codeExercises: '代码练习',
      submissions: '次提交',
      userExercise: '用户 {userId} - 练习 {exerciseId}',
      stats: {
        totalUsers: '注册学生',
        totalLessons: '课程数量',
        totalExercises: '练习题',
        totalSubmissions: '代码提交',
        activeUsers: '活跃用户',
        successRate: '成功率'
      },
      recentUsers: '新注册用户',
      recentSubmissions: '最近代码提交'
    },
    modules: {
      title: '模块管理',
      create: '创建模块',
      edit: '编辑模块',
      name: '模块名称',
      description: '描述',
      order: '顺序',
      lessonsCount: '课程数'
    },
    lessons: {
      title: '课程管理',
      create: '创建课程',
      edit: '编辑课程',
      title_field: '课程标题',
      content: '内容',
      module: '模块',
      prerequisite: '前置课程',
      order: '顺序',
      preview: '预览',
      exercisesCount: '练习数'
    },
    exercises: {
      title: '练习管理',
      create: '创建练习',
      edit: '编辑练习',
      problemStatement: '问题描述',
      lesson: '课程',
      hints: '提示',
      testCases: '测试用例',
      input: '输入',
      expectedOutput: '期望输出',
      hidden: '隐藏测试用例'
    },
    // Extended admin interface translations
    interface: {
      modules: {
        title: '📚 模块管理',
        createNew: '➕ 创建新模块',
        edit: '编辑',
        delete: '删除',
        moduleTitle: '模块标题',
        moduleDescription: '模块描述',
        displayOrder: '显示顺序',
        editModule: '编辑模块',
        createNewModule: '创建新模块',
        moduleManagement: '管理课程',
        manageLessons: '管理课程',
        lessonCount: '课程',
        order: '顺序',
        cancel: '取消',
        save: '保存',
        saving: '保存中...',
        enterModuleTitle: '请输入模块标题',
        enterModuleDescription: '请输入模块描述',
        moduleUpdateSuccess: '模块更新成功',
        moduleCreateSuccess: '模块创建成功',
        moduleDeleteSuccess: '模块删除成功',
        moduleDeleteConfirm: '确定要删除模块 "{title}" 吗？这将同时删除该模块下的所有课程。',
        saveFailed: '保存失败',
        deleteFailed: '删除失败',
        loadModulesError: '加载模块列表失败'
      },
      lessons: {
        title: '📖 课程管理',
        allModules: '所有模块',
        createNewLesson: '➕ 创建新课程',
        edit: '编辑',
        delete: '删除',
        lessonTitle: '课程标题',
        belongsToModule: '所属模块',
        displayOrder: '显示顺序',
        prerequisiteLesson: '前置课程',
        lessonContent: '课程内容 (Markdown)',
        editLesson: '编辑课程',
        createNewLessonTitle: '创建新课程',
        pleaseSelectModule: '请选择模块',
        enterLessonTitle: '请输入课程标题',
        noPrerequisite: '无前置课程',
        enterContentMarkdown: '请输入课程内容，支持Markdown语法',
        markdownToolbar: {
          h1: 'H1',
          h2: 'H2',
          bold: 'B',
          italic: 'I',
          code: 'Code',
          codeBlock: 'Block',
          list: 'List',
          link: 'Link',
          edit: '编辑',
          preview: '预览'
        },
        cancel: '取消',
        save: '保存',
        saving: '保存中...',
        lessonUpdateSuccess: '课程更新成功',
        lessonCreateSuccess: '课程创建成功',
        lessonDeleteSuccess: '课程删除成功',
        lessonDeleteConfirm: '确定要删除课程 "{title}" 吗？这将同时删除该课程的所有练习。',
        saveFailed: '保存失败',
        deleteFailed: '删除失败',
        loadLessonsError: '加载课程列表失败',
        manageExercises: '管理练习',
        noContent: '暂无内容',
        unknownModule: '未知模块'
      },
      exercises: {
        title: '💻 练习管理',
        allLessons: '所有课程',
        createNewExercise: '➕ 创建新练习',
        loadingExercises: '🔄 正在加载练习数据...',
        noExercisesTitle: '📝 暂无练习数据',
        noExercisesDesc: '请先创建一些练习，或检查网络连接',
        exerciseNumber: '练习 {id}',
        edit: '✏️ 编辑',
        testCases: '🧪 测试用例',
        delete: '🗑️ 删除',
        testCaseCount: '{count} 个测试用例',
        problemStatement: '题目描述',
        hints: '提示',
        editExercise: '编辑练习',
        createNewExerciseTitle: '创建新练习',
        belongsToLesson: '所属课程',
        pleaseSelectLesson: '请选择课程',
        enterProblemStatement: '请输入题目描述和要求',
        hintsPerLine: '提示 (每行一个)',
        enterHints: '请输入提示内容，每行一个提示',
        cancel: '取消',
        save: '保存',
        saving: '保存中...',
        exerciseUpdateSuccess: '练习更新成功',
        exerciseCreateSuccess: '练习创建成功',
        exerciseDeleteSuccess: '练习删除成功',
        exerciseDeleteConfirm: '确定要删除练习 {id} 吗？这将同时删除所有测试用例。',
        saveFailed: '保存失败',
        deleteFailed: '删除失败',
        loadExercisesError: '加载练习列表失败',
        loadLessonsError: '加载课程失败',
        unknownLesson: '未知课程',
        testCaseManagement: {
          title: '测试用例管理',
          exerciseTestCases: '练习 {id} 的测试用例',
          addTestCase: '➕ 添加测试用例',
          testCaseNumber: '测试用例 {id}',
          edit: '✏️ 编辑',
          delete: '🗑️ 删除',
          inputData: '输入数据:',
          expectedOutput: '期望输出:',
          noInput: '(无输入)',
          editTestCase: '编辑测试用例',
          addTestCaseTitle: '添加测试用例',
          inputDataLabel: '输入数据',
          inputDataPlaceholder: '输入数据 (如果练习不需要输入，请留空)',
          expectedOutputLabel: '期望输出',
          expectedOutputPlaceholder: '期望的输出结果',
          cancel: '取消',
          save: '保存',
          saving: '保存中...',
          testCaseUpdateSuccess: '测试用例更新成功',
          testCaseCreateSuccess: '测试用例创建成功',
          testCaseDeleteSuccess: '测试用例删除成功',
          testCaseDeleteConfirm: '确定要删除测试用例 {id} 吗？',
          saveFailed: '保存失败',
          deleteFailed: '删除失败',
          loadTestCasesError: '加载测试用例失败'
        }
      }
    },
    users: {
      title: '用户管理',
      username: '用户名',
      email: '邮箱',
      role: '角色',
      createdAt: '创建时间',
      progress: '进度',
      resetProgress: '重置进度',
      viewDetails: '查看详情',
      totalUsers: '用户总数'
    },
    analytics: {
      title: '学习分析',
      overview: '概览',
      userActivity: '用户活动',
      contentStats: '内容统计',
      errorAnalysis: '错误分析',
      performanceMetrics: '性能指标',
      platformOverview: '平台概览',
      lessonCompletionRate: '课程完成率',
      exerciseSuccessRate: '练习成功率',
      errorTypeAnalysis: '错误类型分析',
      noErrorData: '暂无错误数据',
      platformLearningAnalysis: '平台学习分析',
      totalLearners: '学习者总数',
      avgSuccessRate: '平均成功率',
      avgCodeQuality: '平均代码质量',
      activeLearners: '活跃学习者',
      commonErrors: '常见错误类型',
      exerciseDifficulty: '练习难度分析',
      attempts: '次尝试',
      mostActiveUsers: '最活跃学习者',
      submissions: '次提交',
      noActiveUserData: '暂无活跃用户数据',
      recentActivityTrend: '近期活动趋势',
      last7DaysSubmissions: '最近7天提交量',
      noActivityTrendData: '暂无活动趋势数据',
      loadAnalyticsError: '加载分析数据失败',
      noError: '无错误'
    }
  },

  // Error messages
  errors: {
    networkError: '网络错误，请检查您的连接。',
    serverError: '服务器错误，请稍后再试。',
    validationError: '请检查您的输入。',
    unauthorized: '您无权执行此操作。',
    notFound: '未找到请求的资源。',
    generalError: '发生了意外错误。',
    codeExecutionError: '代码执行失败。',
    submissionError: '代码提交失败，请重试。'
  },

  // Success messages
  success: {
    codeSaved: '代码保存成功',
    progressUpdated: '进度已更新',
    lessonCompleted: '课程完成成功',
    moduleCompleted: '模块完成成功',
    profileUpdated: '个人资料更新成功'
  }
}