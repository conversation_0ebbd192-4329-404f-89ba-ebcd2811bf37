<template>
  <div class="feedback-components">
    <!-- 结构化反馈组件 -->
    <div v-if="currentComponent === 'StructuralFeedback'" class="structural-feedback">
      <div class="feedback-header">
        <span class="feedback-icon">🔧</span>
        <h4>{{ $t('feedback.structural.title') }}</h4>
      </div>
      <div class="feedback-body">
        <div class="main-message">{{ feedbackData.message || feedbackData.feedback?.message }}</div>
        <div v-if="feedbackData.details || feedbackData.feedback?.details" class="details">
          {{ feedbackData.details || feedbackData.feedback?.details }}
        </div>
        <div v-if="feedbackData.technical_details || feedbackData.feedback?.technical_details" class="technical-details">
          <strong>{{ $t('feedback.technicalDetails') }}：</strong>{{ feedbackData.technical_details || feedbackData.feedback?.technical_details }}
        </div>
      </div>
    </div>

    <!-- AI反馈组件 -->
    <div v-else-if="currentComponent === 'AIFeedback'" class="ai-feedback">
      <div class="feedback-header">
        <span class="feedback-icon">🤖</span>
        <h4>{{ $t('feedback.ai.title') }}</h4>
        <div class="feedback-meta">
          <span v-if="feedbackData.generation_time" class="meta-item">
            ⏱️ {{ Math.round(feedbackData.generation_time) }}ms
          </span>
          <span v-if="feedbackData.cost" class="meta-item">
            💰 ${{ feedbackData.cost.toFixed(4) }}
          </span>
        </div>
      </div>
      
      <div class="feedback-body">        
        <div v-if="feedbackData.feedback?.encouragement" class="encouragement-section">
          <div class="section-title">💪 {{ $t('feedback.encouragement') }}</div>
          <div class="section-content">{{ feedbackData.feedback.encouragement }}</div>
        </div>
        
        <div v-if="feedbackData.feedback?.problem_identification || feedbackData.feedback?.problem_explanation" class="problem-section">
          <div class="section-title">🔍 {{ $t('feedback.problemAnalysis') }}</div>
          <div class="section-content">
            {{ feedbackData.feedback.problem_identification || feedbackData.feedback.problem_explanation }}
          </div>
        </div>
        
        <div v-if="feedbackData.feedback?.concept_explanation || feedbackData.feedback?.concept_clarification" class="concept-section">
          <div class="section-title">📚 {{ $t('feedback.conceptExplanation') }}</div>
          <div class="section-content">
            {{ feedbackData.feedback.concept_explanation || feedbackData.feedback.concept_clarification }}
          </div>
        </div>
        
        <div v-if="feedbackData.feedback?.guiding_hints || feedbackData.feedback?.hints_not_answers" class="hints-section">
          <div class="section-title">💡 {{ $t('feedback.learningHints') }}</div>
          <div class="section-content">
            {{ feedbackData.feedback.guiding_hints || feedbackData.feedback.hints_not_answers }}
          </div>
        </div>
        
        <div v-if="feedbackData.feedback?.learning_suggestions || feedbackData.feedback?.next_steps" class="suggestions-section">
          <div class="section-title">📈 {{ $t('feedback.suggestions') }}</div>
          <div class="section-content">
            {{ feedbackData.feedback.learning_suggestions || feedbackData.feedback.next_steps }}
          </div>
        </div>

        <!-- 反馈质量评分 -->
        <div class="feedback-rating" v-if="submissionId">
          <div class="rating-title">{{ $t('feedback.rating.title') }}</div>
          <div class="rating-controls">
            <div class="rating-group">
              <label>{{ $t('feedback.rating.helpfulness') }}</label>
              <div class="star-rating">
                <span 
                  v-for="star in 5" 
                  :key="`helpful-${star}`"
                  class="star"
                  :class="{ active: star <= helpfulnessRating }"
                  @click="helpfulnessRating = star"
                >
                  ⭐
                </span>
              </div>
            </div>
            <div class="rating-group">
              <label>{{ $t('feedback.rating.clarity') }}</label>
              <div class="star-rating">
                <span 
                  v-for="star in 5" 
                  :key="`clear-${star}`"
                  class="star"
                  :class="{ active: star <= clarityRating }"
                  @click="clarityRating = star"
                >
                  ⭐
                </span>
              </div>
            </div>
            <button 
              @click="submitRating" 
              class="btn btn-sm btn-primary"
              :disabled="!helpfulnessRating"
            >
              {{ $t('feedback.rating.submitRating') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 基础反馈组件 -->
    <div v-else class="basic-feedback">
      <div class="feedback-content">
        {{ feedbackData.feedback || $t('feedback.noContent') }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'FeedbackComponents',
  props: {
    feedbackData: {
      type: Object,
      default: () => ({})
    },
    submissionId: {
      type: Number,
      default: null
    },
    currentComponent: {
      type: String,
      default: 'BasicFeedback'
    }
  },
  emits: ['rate-feedback'],
  setup(props, { emit }) {
    const helpfulnessRating = ref(0)
    const clarityRating = ref(0)
    
    const submitRating = () => {
      if (helpfulnessRating.value > 0) {
        // 修复字段名匹配问题
        emit('rate-feedback', {
          feedback_id: props.feedbackData.feedback_id,
          helpfulness_rating: helpfulnessRating.value,
          clarity_rating: clarityRating.value || helpfulnessRating.value
        })
        
        // 重置评分
        helpfulnessRating.value = 0
        clarityRating.value = 0
      }
    }

    return {
      helpfulnessRating,
      clarityRating,
      submitRating
    }
  }
}
</script>

<style scoped>
.feedback-components {
  width: 100%;
}

.feedback-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.feedback-icon {
  font-size: 24px;
}

.feedback-header h4 {
  margin: 0;
  color: #2c3e50;
  flex: 1;
}

.feedback-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.meta-item {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.feedback-body {
  space-y: 16px;
}

.structural-feedback .main-message {
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 12px;
}

.structural-feedback .details {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  margin-bottom: 12px;
}

.structural-feedback .technical-details {
  font-size: 14px;
  color: #6c757d;
  background: #fff3cd;
  padding: 8px;
  border-radius: 4px;
}

.ai-feedback .section-title {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-feedback .section-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.encouragement-section .section-content {
  background: #d4edda;
  border-left: 4px solid #28a745;
}

.problem-section .section-content {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
}

.concept-section .section-content {
  background: #e7f3ff;
  border-left: 4px solid #007bff;
}

.hints-section .section-content {
  background: #f0e5ff;
  border-left: 4px solid #6f42c1;
}

.suggestions-section .section-content {
  background: #e8f5e8;
  border-left: 4px solid #20c997;
}

.feedback-rating {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.rating-title {
  font-weight: 500;
  margin-bottom: 12px;
  color: #495057;
}

.rating-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rating-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-group label {
  min-width: 60px;
  font-size: 14px;
  color: #6c757d;
}

.star-rating {
  display: flex;
  gap: 4px;
}

.star {
  cursor: pointer;
  font-size: 18px;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.active {
  opacity: 1;
}

.star:hover {
  opacity: 0.8;
}

.basic-feedback {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #6c757d;
}

.feedback-content {
  color: #495057;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .feedback-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .feedback-meta {
    width: 100%;
    justify-content: space-between;
  }
  
  .rating-controls {
    gap: 8px;
  }
  
  .rating-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>