<template>
  <div class="code-editor-wrapper">
    <div ref="editorRef" class="code-editor"></div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'
import { basicSetup } from 'codemirror'
import { python } from '@codemirror/lang-python'
import { oneDark } from '@codemirror/theme-one-dark'
import { useI18n } from 'vue-i18n'

export default {
  name: 'CodeEditor',
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    theme: {
      type: String,
      default: 'light', // 'light' or 'dark'
    },
    height: {
      type: String,
      default: '200px'
    },
    placeholder: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const editorRef = ref(null)
    const { t } = useI18n()
    let editorView = null

    // Computed placeholder with i18n
    const placeholderText = computed(() => {
      return props.placeholder || t('lesson.placeholder')
    })

    const createEditor = () => {
      if (!editorRef.value) return

      const extensions = [
        basicSetup,
        python(),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const newValue = update.state.doc.toString()
            emit('update:modelValue', newValue)
            emit('change', newValue)
          }
        }),
        EditorView.theme({
          '&': {
            height: props.height
          },
          '.cm-editor': {
            fontSize: '14px',
            fontFamily: 'Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-content': {
            padding: '16px',
            minHeight: props.height,
            caretColor: props.theme === 'dark' ? '#fff' : '#000'
          },
          '.cm-placeholder': {
            color: props.theme === 'dark' ? '#888' : '#999'
          }
        })
      ]

      // 添加主题
      if (props.theme === 'dark') {
        extensions.push(oneDark)
      }

      // 添加只读模式
      if (props.readonly) {
        extensions.push(EditorState.readOnly.of(true))
      }

      // 添加占位符
      if (placeholderText.value) {
        extensions.push(EditorView.theme({
          '.cm-placeholder': {
            color: props.theme === 'dark' ? '#888' : '#999'
          }
        }))
      }

      const state = EditorState.create({
        doc: props.modelValue,
        extensions
      })

      editorView = new EditorView({
        state,
        parent: editorRef.value
      })
    }

    const destroyEditor = () => {
      if (editorView) {
        editorView.destroy()
        editorView = null
      }
    }

    // 监听modelValue变化，同步到编辑器
    watch(() => props.modelValue, (newValue) => {
      if (editorView && newValue !== editorView.state.doc.toString()) {
        editorView.dispatch({
          changes: {
            from: 0,
            to: editorView.state.doc.length,
            insert: newValue
          }
        })
      }
    })

    // 监听主题变化
    watch(() => props.theme, () => {
      destroyEditor()
      createEditor()
    })

    // 监听只读状态变化
    watch(() => props.readonly, () => {
      destroyEditor()
      createEditor()
    })

    onMounted(() => {
      createEditor()
    })

    onUnmounted(() => {
      destroyEditor()
    })

    // 公开方法
    const focus = () => {
      if (editorView) {
        editorView.focus()
      }
    }

    const setValue = (value) => {
      if (editorView) {
        editorView.dispatch({
          changes: {
            from: 0,
            to: editorView.state.doc.length,
            insert: value
          }
        })
      }
    }

    const getValue = () => {
      return editorView ? editorView.state.doc.toString() : ''
    }

    return {
      editorRef,
      focus,
      setValue,
      getValue
    }
  }
}
</script>

<style scoped>
.code-editor-wrapper {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.code-editor {
  min-height: 200px;
}

/* CodeMirror样式覆盖 */
:deep(.cm-editor) {
  border-radius: 8px;
}

:deep(.cm-editor.cm-focused) {
  outline: 2px solid #3498db;
  outline-offset: -2px;
}

:deep(.cm-scroller) {
  font-family: 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
}

:deep(.cm-line) {
  line-height: 1.5;
}

/* 自定义滚动条 */
:deep(.cm-scroller::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.cm-scroller::-webkit-scrollbar-track) {
  background: #f1f1f1;
}

:deep(.cm-scroller::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.cm-scroller::-webkit-scrollbar-thumb:hover) {
  background: #a1a1a1;
}

/* 语法高亮自定义 */
:deep(.cm-keyword) {
  color: #0066cc;
  font-weight: bold;
}

:deep(.cm-string) {
  color: #008000;
}

:deep(.cm-comment) {
  color: #808080;
  font-style: italic;
}

:deep(.cm-number) {
  color: #ff6600;
}

:deep(.cm-operator) {
  color: #666666;
}

:deep(.cm-builtin) {
  color: #0066cc;
}

/* 暗色主题下的边框 */
:deep(.cm-theme-dark .cm-editor) {
  border-color: #444;
}
</style>