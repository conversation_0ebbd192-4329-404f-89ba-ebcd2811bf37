<template>
  <div class="language-switcher">
    <select 
      v-model="currentLanguage" 
      @change="changeLanguage"
      class="language-select"
    >
      <option value="en">🇬🇧 English</option>
      <option value="zh">🇨🇳 中文</option>
    </select>
  </div>
</template>

<script>
import { useI18n } from 'vue-i18n'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { updateTitleByRoute } from '../utils/titleManager.js'

export default {
  name: 'LanguageSwitcher',
  setup() {
    const { locale } = useI18n()
    const route = useRoute()
    const currentLanguage = ref(locale.value)
    
    // Watch for locale changes and sync with select
    watch(locale, (newLocale) => {
      currentLanguage.value = newLocale
    })
    
    const changeLanguage = (event) => {
      const newLang = event.target.value
      locale.value = newLang
      localStorage.setItem('preferred-language', newLang)
      
      // 更新页面标题
      updateTitleByRoute(route.name)
      
      // Optional: Send preference to backend
      // await api.updateUserPreference({ language: newLang })
    }
    
    return {
      currentLanguage,
      changeLanguage
    }
  }
}
</script>

<style scoped>
.language-switcher {
  display: inline-flex;
  align-items: center;
}

.language-select {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.language-select:hover {
  border-color: #3498db;
}

.language-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .language-select {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
  }
  
  .language-select:hover {
    border-color: #3498db;
  }
}
</style>