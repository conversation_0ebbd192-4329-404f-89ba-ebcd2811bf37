/**
 * 页面标题管理工具
 * 用于根据当前语言和页面动态更新页面标题
 */

import i18n from '../i18n'

/**
 * 更新页面标题
 * @param {string} titleKey - 多语言配置中的标题key，如 'title.main'
 * @param {string} fallback - 后备标题（可选）
 */
export function updatePageTitle(titleKey = 'title.main', fallback = null) {
  try {
    const title = i18n.global.t(titleKey)
    document.title = title || fallback || 'Python Learning Platform'
    
    // 同时更新HTML标签的lang属性
    const htmlElement = document.querySelector('html')
    if (htmlElement) {
      const currentLocale = i18n.global.locale.value
      htmlElement.setAttribute('lang', currentLocale === 'zh' ? 'zh-CN' : 'en-US')
    }
  } catch (error) {
    console.warn('Failed to update page title:', error)
    if (fallback) {
      document.title = fallback
    }
  }
}

/**
 * 根据路由名称更新页面标题
 * @param {string} routeName - 路由名称
 */
export function updateTitleByRoute(routeName) {
  const titleMap = {
    'home': 'title.home',
    'login': 'title.login',
    'register': 'title.register',
    'dashboard': 'title.dashboard',
    'modules': 'title.modules',
    'lesson': 'title.lesson',
    'admin': 'title.admin',
    'admin-dashboard': 'title.admin',
    'admin-modules': 'title.admin',
    'admin-lessons': 'title.admin',
    'admin-exercises': 'title.admin',
    'admin-users': 'title.admin',
    'admin-analytics': 'title.analytics',
    'research-analytics': 'title.analytics'
  }
  
  const titleKey = titleMap[routeName] || 'title.main'
  updatePageTitle(titleKey)
}

/**
 * 监听语言变化并更新标题
 * 注意：在Vue 3 + vue-i18n 9.x中，语言变化监听应该在组件内部处理
 * 这个函数主要用于初始化设置
 */
export function setupTitleWatcher() {
  // 设置初始标题
  updatePageTitle('title.main')
  
  // 监听popstate事件，处理浏览器前进后退
  window.addEventListener('popstate', () => {
    setTimeout(() => {
      const currentRoute = window.location.pathname
      let routeName = 'home'
      
      if (currentRoute.includes('/login')) routeName = 'login'
      else if (currentRoute.includes('/register')) routeName = 'register'
      else if (currentRoute.includes('/dashboard')) routeName = 'dashboard'
      else if (currentRoute.includes('/modules')) routeName = 'modules'
      else if (currentRoute.includes('/lesson')) routeName = 'lesson'
      else if (currentRoute.includes('/admin')) routeName = 'admin'
      
      updateTitleByRoute(routeName)
    }, 100)
  })
}
