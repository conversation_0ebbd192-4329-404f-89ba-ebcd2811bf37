/**
 * 前端配置管理
 * 统一管理API地址、端口等配置信息
 */

// 开发环境配置
const development = {
  API_BASE_URL: 'http://localhost:5000/api',
  FRONTEND_PORT: 3000,
  BACKEND_PORT: 5000,
  TIMEOUT: 30000
}

// 生产环境配置
const production = {
  API_BASE_URL: '/api', // 生产环境使用相对路径
  FRONTEND_PORT: 80,
  BACKEND_PORT: 80,
  TIMEOUT: 30000
}

// 测试环境配置
const test = {
  API_BASE_URL: 'http://localhost:5001/api',
  FRONTEND_PORT: 3001,
  BACKEND_PORT: 5001,
  TIMEOUT: 10000
}

// 根据环境变量选择配置
const env = (typeof import.meta !== 'undefined' && import.meta.env?.MODE) || 'development'

const config = {
  development,
  production,
  test
}[env] || development

// 允许环境变量覆盖配置 (仅在浏览器环境中)
if (typeof import.meta !== 'undefined' && import.meta.env) {
  if (import.meta.env.VITE_API_BASE_URL) {
    config.API_BASE_URL = import.meta.env.VITE_API_BASE_URL
  }

  if (import.meta.env.VITE_FRONTEND_PORT) {
    config.FRONTEND_PORT = parseInt(import.meta.env.VITE_FRONTEND_PORT)
  }

  if (import.meta.env.VITE_BACKEND_PORT) {
    config.BACKEND_PORT = parseInt(import.meta.env.VITE_BACKEND_PORT)
  }
}

console.log(`🔧 前端配置 [${env}]:`, config)

export default config 