import axios from 'axios'
import config from './config'

// 创建axios实例
const apiClient = axios.create({
  baseURL: config.API_BASE_URL,
  timeout: config.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证令牌
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
apiClient.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // 令牌过期，清除本地存储并跳转到登录页
      localStorage.removeItem('authToken')
      localStorage.removeItem('user')
      if (window.location.pathname !== '/admin/login') {
        window.location.href = '/admin/login'
      }
    }
    return Promise.reject(error)
  }
)

export const adminAPI = {
  // 仪表板数据
  getDashboard: () => apiClient.get('/admin/dashboard'),
  
  // 模块管理
  getModules: () => apiClient.get('/admin/modules'),
  createModule: (data) => apiClient.post('/admin/modules', data),
  updateModule: (id, data) => apiClient.put(`/admin/modules/${id}`, data),
  deleteModule: (id) => apiClient.delete(`/admin/modules/${id}`),
  
  // 课程管理
  getLessons: (moduleId = null) => {
    const params = moduleId ? { module_id: moduleId } : {}
    return apiClient.get('/admin/lessons', { params })
  },
  getLesson: (id) => apiClient.get(`/admin/lessons/${id}`),
  createLesson: (data) => apiClient.post('/admin/lessons', data),
  updateLesson: (id, data) => apiClient.put(`/admin/lessons/${id}`, data),
  deleteLesson: (id) => apiClient.delete(`/admin/lessons/${id}`),
  
  // 练习管理
  getExercises: (lessonId = null) => {
    const params = lessonId ? { lesson_id: lessonId } : {}
    return apiClient.get('/admin/exercises', { params })
  },
  getExercise: (id) => apiClient.get(`/admin/exercises/${id}`),
  createExercise: (data) => apiClient.post('/admin/exercises', data),
  updateExercise: (id, data) => apiClient.put(`/admin/exercises/${id}`, data),
  deleteExercise: (id) => apiClient.delete(`/admin/exercises/${id}`),
  
  // 测试用例管理
  getTestCases: (exerciseId) => apiClient.get(`/admin/exercises/${exerciseId}/test-cases`),
  createTestCase: (exerciseId, data) => apiClient.post(`/admin/exercises/${exerciseId}/test-cases`, data),
  updateTestCase: (exerciseId, testCaseId, data) => apiClient.put(`/admin/exercises/${exerciseId}/test-cases/${testCaseId}`, data),
  deleteTestCase: (exerciseId, testCaseId) => apiClient.delete(`/admin/exercises/${exerciseId}/test-cases/${testCaseId}`),
  
  // 用户管理
  getUsers: (params = {}) => {
    const queryParams = new URLSearchParams()
    if (params.page) queryParams.append('page', params.page)
    if (params.per_page) queryParams.append('per_page', params.per_page)
    const queryString = queryParams.toString()
    return apiClient.get(`/admin/users${queryString ? '?' + queryString : ''}`)
  },
  getUserDetails: (id) => apiClient.get(`/admin/users/${id}`),
  updateUser: (id, data) => apiClient.put(`/admin/users/${id}`, data),
  deleteUser: (id) => apiClient.delete(`/admin/users/${id}`),
  resetUserProgress: (id) => apiClient.post(`/admin/users/${id}/reset-progress`),
  
  // 学习分析
  getAnalytics: () => apiClient.get('/admin/analytics'),
  
  // 获取平台代码分析数据
  getPlatformAnalytics: () => apiClient.get('/analytics/admin/platform_analysis'),
  getUserProgress: (userId) => apiClient.get(`/admin/users/${userId}/progress`),
  getSubmissionStats: () => apiClient.get('/admin/submissions/stats')
}

export default adminAPI