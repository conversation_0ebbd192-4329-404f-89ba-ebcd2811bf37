import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// 从环境变量读取配置，提供默认值
const FRONTEND_PORT = parseInt(process.env.VITE_FRONTEND_PORT) || 3000
const BACKEND_PORT = parseInt(process.env.VITE_BACKEND_PORT) || 5000

export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: FRONTEND_PORT,
    proxy: {
      '/api': {
        target: `http://localhost:${BACKEND_PORT}`,
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist'
  }
})