#!/usr/bin/env node

/**
 * Development startup script for i18n testing
 * Tests both English and Chinese interfaces
 */

console.log('\n🌍 Starting Python Learning Platform with i18n support...\n');

console.log('📋 Features implemented:');
console.log('✅ Vue I18n integration');
console.log('✅ English/Chinese language packages');
console.log('✅ Language switcher component');
console.log('✅ All core pages internationalized:');
console.log('   • Home page');
console.log('   • Login/Register');
console.log('   • Dashboard');
console.log('   • Modules/Lessons');
console.log('   • Admin interfaces');
console.log('   • Feedback systems');

console.log('\n🎯 For your MSc Computer Science project:');
console.log('• Default language: English (perfect for UK academics)');
console.log('• Language persistence via localStorage');
console.log('• Professional English translations');
console.log('• Seamless language switching');

console.log('\n🚀 Starting development server...\n');

// Start the development server
const { spawn } = require('child_process');
const dev = spawn('npm', ['run', 'dev'], { 
  stdio: 'inherit',
  shell: true 
});

dev.on('error', (err) => {
  console.error('Failed to start development server:', err);
});

dev.on('close', (code) => {
  console.log(`\nDevelopment server exited with code ${code}`);
});