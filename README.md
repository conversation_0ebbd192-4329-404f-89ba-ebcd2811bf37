# 🐍 交互式Python学习平台

基于智能辅导系统(ITS)理论的编程学习平台，为初学者提供个性化的Python学习体验。

## 📖 项目简介

这是一个毕业设计项目，实现了完整的智能编程教学系统，基于经典的三模型ITS架构：

- **学习者模型(Learner Model)**：追踪学习进度和个人化数据
- **内容模型(Content Model)**：结构化的课程内容和练习
- **交互模型(Interaction Model)**：智能反馈和自适应支持

### 🎯 核心功能

- ✅ **浏览器内代码编辑**：无需安装本地环境，支持语法高亮
- ✅ **实时代码执行**：安全的代码运行环境，3秒内响应
- ✅ **智能反馈系统**：多种AI模型(OpenAI + DeepSeek)提供教育性反馈对比
- ✅ **个性化学习路径**：基于前置条件的课程解锁机制
- ✅ **分级提示系统**：基于尝试次数和时间的渐进式脚手架支持
- ✅ **学习分析**：代码质量分析、复杂度评估和学习效果追踪
- ✅ **反馈效果研究**：内置A/B测试框架，对比不同反馈方法的教学效果

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.12** + **Flask 2.3.3** - Web框架
- **SQLAlchemy** - ORM数据库操作
- **Flask-JWT-Extended** - JWT身份认证
- **SQLite** - 开发数据库（支持升级到PostgreSQL）
- **Judge0/Piston API** - 安全代码执行服务
- **OpenRouter API** - 多AI模型接入（OpenAI + DeepSeek）

### 前端技术栈
- **Vue.js 3** + **Composition API** - 响应式用户界面
- **Vue Router** - 单页应用路由
- **CodeMirror 6** - 专业代码编辑器
- **Axios** - HTTP客户端
- **Marked** - Markdown渲染

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Learner       │    │   Content       │    │   Interaction   │
│   Model         │    │   Model         │    │   Model         │
│                 │    │                 │    │                 │
│ • User Progress │    │ • Modules       │    │ • Submissions   │
│ • Attempts      │◄──►│ • Lessons       │◄──►│ • Feedback      │
│ • Time Tracking │    │ • Exercises     │    │ • Hints         │
│ • Error Patterns│    │ • Test Cases    │    │ • Scaffolding   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
python-learning-platform/
├── 📂 backend/                 # Flask后端应用
│   ├── 🚀 app.py              # Flask应用入口
│   ├── 🔧 config.py           # 配置管理系统
│   ├── 🗃️ setup_database.py   # 数据库初始化脚本
│   ├── 📋 requirements.txt    # Python依赖
│   ├── 📂 models/             # 数据模型
│   │   ├── content_models.py  # Content Model实现
│   │   └── learning_models.py # Learner Model实现
│   ├── 📂 routes/             # API路由 (Interaction Model)
│   │   ├── auth.py           # 用户认证
│   │   ├── content.py        # 内容管理
│   │   ├── submissions.py    # 代码提交和评估
│   │   ├── progress.py       # 学习进度跟踪
│   │   ├── admin.py          # 管理员功能
│   │   └── analytics.py      # 学习分析
│   ├── 📂 utils/             # 工具类
│   │   ├── code_executor.py  # 代码执行器
│   │   └── code_analyzer.py  # AI代码分析器
│   └── 📂 instance/          # 数据库文件
├── 📂 frontend/               # Vue.js前端应用
│   ├── 📋 package.json        # 前端依赖配置
│   ├── ⚙️ vite.config.js     # Vite构建配置
│   └── 📂 src/
│       ├── 🎨 App.vue         # 主应用组件
│       ├── 🔧 config/         # 前端配置管理
│       ├── 🧩 components/     # Vue组件
│       │   ├── CodeEditor.vue       # 代码编辑器
│       │   └── FeedbackComponents.vue # 反馈组件
│       └── 📄 views/          # 页面组件
│           ├── Login.vue      # 登录页面
│           ├── Dashboard.vue  # 学习仪表板
│           ├── Modules.vue    # 课程模块
│           ├── Lesson.vue     # 课程详情页
│           └── Admin*.vue     # 管理员页面
└── 📄 setup.py               # 一键安装脚本
```

## 🚀 快速开始

### 📋 环境要求

- **Python 3.8+**
- **Node.js 16+** 
- **现代浏览器** (支持ES6+)

### ⚡ 一键安装（推荐）

```bash
# 克隆项目
git clone https://github.com/你的用户名/python-learning-platform.git
cd python-learning-platform

# 运行一键安装脚本
python setup.py
```

安装脚本将自动：
1. ✅ 检查环境依赖
2. ✅ 安装后端和前端依赖
3. ✅ 初始化数据库和示例内容
4. ✅ 启动前后端服务

### 🔧 手动安装

<details>
<summary>点击展开手动安装步骤</summary>

#### 1. 后端安装

```bash
# 进入后端目录
cd backend

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python setup_database.py

# 启动后端服务
python app.py
```

#### 2. 前端安装

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

</details>

### 🌐 访问应用

安装完成后，打开浏览器访问：

- **学生端**：http://localhost:3000
- **管理员后台**：http://localhost:3000/admin
- **API文档**：http://localhost:5000

**默认管理员账户**：
- 用户名：`admin`
- 密码：`admin123`

## 🎓 教学设计特色

### 基于教育理论的设计

1. **维果茨基的最近发展区(ZPD)理论**
   - 动态评估学习者能力
   - 提供恰到好处的脚手架支持

2. **即时反馈原则**
   - 3秒内的代码执行响应
   - 多层次的错误诊断和建议

3. **微学习方法**
   - 每课程10-15分钟完成
   - 渐进式难度设计

### 智能化特性

- **自适应提示系统**：基于尝试次数(>3次)和时间(>90秒)自动触发提示
- **多源反馈对比**：OpenAI vs DeepSeek教育性反馈效果对比研究
- **代码质量分析**：复杂度、行数、语法质量自动评估
- **学习模式识别**：高级初学者、普通学习者、需要支持三种类型自动识别

## 🔧 配置管理

项目支持多环境配置，通过环境变量轻松部署：

### 前端配置

创建 `frontend/.env.local`：
```bash
VITE_API_BASE_URL=http://localhost:5000/api
VITE_FRONTEND_PORT=3000
VITE_BACKEND_PORT=5000
```

### 后端配置

创建 `backend/.env`：
```bash
FLASK_ENV=development
HOST=0.0.0.0
PORT=5000
DEBUG=true
FRONTEND_URL=http://localhost:3000

# 安全密钥（生产环境必须修改）
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here

# 外部API配置（可选）
JUDGE0_API_KEY=your-judge0-api-key
OPENROUTER_API_KEY=your-openrouter-api-key
```

### 🔑 外部API说明

- **Judge0 API**：用于安全代码执行（可选，有免费额度）
- **OpenRouter API**：用于AI代码分析（可选，支持多模型对比）

## 📊 数据库设计

### 三模型映射

**Content Model (内容模型)**
```sql
modules      -- 课程模块
lessons      -- 具体课程（知识组件 KCs）
exercises    -- 编程练习
test_cases   -- 自动评估测试用例
```

**Learner Model (学习者模型) - 覆盖模型实现**
```sql
users           -- 用户基本信息
user_progress   -- 学习进度跟踪（Overlay Model）
```

**Interaction Model (交互模型)**
```sql
submissions           -- 代码提交历史
hint_usage           -- 提示使用记录
feedback_instances   -- 反馈实例（支持A/B测试）
feedback_effectiveness -- 反馈效果追踪
```

## 🔬 研究功能

本平台内置了教育技术研究功能：

### A/B测试框架
- 对比不同AI模型的教育效果
- 追踪反馈方法的学习效果
- 支持用户体验评分收集

### 学习分析指标
- 代码提交成功率
- 平均完成时间
- 错误模式识别
- 提示使用频率

## 🛠️ 开发与部署

### 开发模式
```bash
# 后端开发服务器（Flask）
cd backend && python app.py

# 前端开发服务器（Vite热重载）
cd frontend && npm run dev
```

### 生产部署建议
- 使用Gunicorn/uWSGI部署Flask应用
- 配置Nginx反向代理
- 升级到PostgreSQL数据库
- 使用Docker容器化部署
- 配置SSL证书

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 修改端口
   export PORT=8080  # 后端
   export VITE_FRONTEND_PORT=4000  # 前端
   ```

2. **API连接失败**
   - 检查后端服务：`curl http://localhost:5000/api/health`
   - 确认CORS配置正确

3. **代码执行失败**
   - 平台会自动回退到模拟执行
   - 不影响学习体验

4. **AI分析不可用**
   - 系统会自动回退到本地规则分析
   - 核心功能不受影响

## 📝 API文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户资料

### 内容接口
- `GET /api/content/modules` - 获取课程模块
- `GET /api/content/lessons/:id` - 获取课程详情

### 提交接口
- `POST /api/submissions/submit` - 提交代码评估
- `POST /api/submissions/exercises/:id/hint` - 请求提示

### 进度接口
- `GET /api/progress/dashboard` - 学习仪表板
- `GET /api/progress/stats` - 学习统计

### 分析接口
- `GET /api/analytics/user/code_analysis` - 用户代码分析
- `GET /api/analytics/admin/platform_analysis` - 平台分析（管理员）

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交变更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- **理论基础**：智能辅导系统(ITS)研究社区
- **技术支持**：Flask和Vue.js开源社区
- **API服务**：Judge0代码执行服务、OpenRouter AI服务
- **教育理论**：维果茨基的最近发展区理论、脚手架教学法

---

**开发者**: [你的名字]  
**项目类型**: 毕业设计 - 智能编程教育系统  
**开发时间**: 2024年

如果这个项目对你有帮助，请给个⭐星支持！