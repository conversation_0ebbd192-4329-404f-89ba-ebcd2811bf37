#!/bin/bash

echo "🎓 Starting Python Learning Platform - English Edition"
echo "🇬🇧 Perfect for UK MSc Computer Science Project"
echo "=================================================="

# Kill any existing processes on ports 5000 and 3000
echo "🔧 Cleaning up existing processes..."
pkill -f "python.*app" 2>/dev/null || true
pkill -f "npm.*dev" 2>/dev/null || true
sleep 2

# Start backend with English database
echo "🚀 Starting backend with English content..."
cd /home/<USER>/xiang/backend
source venv/bin/activate
python app_english.py &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to initialize..."
sleep 5

# Start frontend
echo "🎨 Starting frontend with i18n support..."
cd /home/<USER>/xiang/frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 Platform started successfully!"
echo "=================================================="
echo "🌐 Frontend: http://localhost:3000 (Default: English)"
echo "🔧 Backend API: http://localhost:5000"
echo "👨‍💼 Admin Login: admin / admin123"
echo "📊 Students: alice_johnson, bob_smith, charlie_brown, diana_wilson, evan_davis"
echo "🔑 Student Password: password123"
echo ""
echo "🎯 Features for your MSc project:"
echo "   • Complete English interface"
echo "   • Professional course content"
echo "   • AI-powered feedback system"
echo "   • Real-time language switching"
echo "   • Sample learning analytics"
echo ""
echo "⚠️  Press Ctrl+C to stop both servers"
echo "=================================================="

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Cleanup completed!"
    exit 0
}

# Trap Ctrl+C
trap cleanup INT

# Wait for user to stop
wait