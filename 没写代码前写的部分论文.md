Project Progress Summary

Abstract:
This project presents the analysis, design, and implementation of an interactive Python learning website tailored for programming beginners, especially students from non-Computer Science backgrounds. By providing an online coding environment with an integrated instant feedback mechanism, this system aims to address core barriers faced by novices, such as complex environment setup and a lack of immediate support. This proposal details the project's motivation, theoretical foundation, system requirements, and the planned evaluation methodology.

Project Objectives:
To design and develop a web-based Python learning platform that requires no local environment configuration.
To implement an automated code evaluation system capable of providing instant and targeted feedback.
To build personalised learning pathways based on established educational technology models.
To evaluate and improve the platform's usability and educational effectiveness through user testing.

1.Introduction
1.1 Context and Rationale
In the current technology-driven era, programming is evolving from a specialist skill into a general core competency essential for enhancing personal and professional capabilities. However, for beginners without a technical background, the entry barrier to programming remains significant. A primary hurdle is the complexity of setting up a local development environment, which can be an intimidating and frustrating experience for novices.
Furthermore, effective learning depends on timely feedback. Research in the field of computer science education highlights that providing immediate and constructive feedback is crucial for maintaining learner motivation and persistence. Traditional learning resources like textbooks and video tutorials have limitations in offering this level of interactive support.
This project, therefore, aims to fill this gap. By developing a highly interactive and responsive online learning platform, it seeks to lower the initial barrier to learning programming. The platform will feature a browser-based code editor, removing the need for environment setup, and will provide real-time code execution and feedback. We believe such a system can significantly improve the learning efficiency and success rate of beginners by creating a more engaging and less frustrating educational experience.
2.Application Domain
2.1Background: E-learning Technologies
This project is grounded in the fields of E-learning and Learning Technologies. To effectively address the common challenges faced by programming novices, modern Intelligent Tutoring Systems often rely on three core theoretical models: the Learner Model, the Content Model, and the Interaction Model. Together, they form the theoretical foundation for an adaptive and personalised learning environment.

Learner Model: This model is a dynamic profile of the individual learner, responsible for storing and updating their personal information, knowledge state, learning history, and common error patterns. An effective Learner Model is the prerequisite for personalisation, enabling the system to recommend suitable learning paths and exercises.

Content Model: This model is a structured representation of the knowledge domain being taught. It defines how learning materials are organised and how knowledge topics relate to one another, particularly their dependencies. For example, the Content Model ensures that a user must understand 'variables' before they can proceed to learn 'loops'.

Interaction Model: This model defines the ways in which the learner engages with the system. It embodies educational principles such as 'learning by doing' and 'instant feedback'. Through carefully designed interaction mechanisms, such as interactive coding exercises and context-aware hints, the system can guide the learner to actively explore concepts and correct mistakes promptly.

In summary, these three models work in a closed loop: the system understands the student via the Learner Model, provides appropriate materials from the Content Model, and facilitates the learning process through the Interaction Model. The outcomes of these interactions are then used to update the Learner Model, thus creating a truly personalised learning journey for each user.
2.2Workflow Example
To illustrate how these models work together, consider the following scenario: A user named 'Alex' (whose Learner Model profile indicates they have mastered 'variables') intends to learn about 'loops'.
1.Interaction Trigger: Alex clicks on the 'Loops' chapter in the course list.
2.Prerequisite Check: The system, guided by the Interaction Model, queries the Content Model and identifies that 'variables' is a prerequisite for 'loops'.
3.Learner Validation: The system accesses Alex's Learner Model and confirms that this prerequisite has been met.
4.Content Delivery: The system retrieves the 'loops' tutorial and exercises from the Content Model and presents them to Alex via the user interface (the Interaction Model).
5.User Practice & Feedback: 
1.Alex writes code in the browser-based editor and clicks 'Run'. The Interaction Model captures this event and sends the code to the backend.
2.The code is evaluated, and it fails to pass a predefined test case.
3.The Interaction Model immediately displays a feedback message, such as "Incorrect output. Check your loop's termination condition."
6.Success & State Update: 
1.Alex revises the code based on the feedback and runs it again, this time passing all tests.
2.The Interaction Model displays a congratulatory message.
3.The system then updates Alex's Learner Model, marking the 'loops' topic as 'completed'.



2.3Evaluation of Existing Systems

Platforms such as freeCodeCamp will be reviewed using the above three-model framework. While they offer well-structured content, their feedback is often too technical for true beginners.

2.4Desirable Features in a Generic System

A strong learner model to track progress and common errors
A well-structured content model that respects topic dependencies
An interaction model with highly targeted, supportive feedback that encourages problem-solving without giving direct answers
3.Requirements Analysis
3.1Requirements Overview
The goal of this project is to build an interactive web-based platform to help Python programming beginners—especially non-CS students—learn basic programming skills effectively. The system must be easy to use, provide timely feedback, and support a progressive learning path.
3.2Evaluation of Current Systems
Existing platforms excel in content structure but lack personalization and beginner-friendly guidance. Many offer technical error messages that confuse novices. This project will analyze those limitations through the three-model framework to guide the new system's design.
3.3User Requirements
UR1: As a new user, I want to easily register for an account and log in, so that my learning progress can be saved.
UR2: As a learner, I want to see a clear list of courses and my completion status for each.
UR3: As a learner, I want to read the lesson material and practice coding on the same page to avoid switching between windows.
UR4: As a learner, I want to run my code directly in the browser without any local setup and see the output or error messages immediately.
UR5: As a learner who is stuck on a problem, I want to receive a useful hint to help me find a solution.
3.4System Requirements
Functional Requirements (FR):
FR1: The system must provide user authentication features, including registration, login, and session management.
FR2: The system must store each user's learning progress, including a list of completed lessons.
FR3: The system must provide a browser-based code editor with Python syntax highlighting.
FR4: The system must provide simple and easy-to-understand lesson content in a structured format.
FR5: The system must be able to automatically evaluate user-submitted code against predefined test cases and return the result.
Non-functional Requirements (NFR):
Performance: The system's response time, from receiving a code submission to returning a result, should not exceed 3 seconds for simple exercises.
Security: User passwords must be hashed with a salt before being stored in the database.
Reliability: The system must handle common user code errors (e.g., infinite loops) gracefully without crashing the service.
Usability: The user interface must be clean and intuitive, allowing first-time users to understand its core functions without a tutorial.
8. Critical Evaluation (Preliminary Plan)
8.1Evaluation of Process
The project will adopt an incremental development model, starting with a Minimum Viable Product (MVP). The proposed technology stack is Python with the Flask framework for the backend, SQLite for the database (for ease of development), and Vue.js for a dynamic and responsive frontend. A Gantt chart will be used for project scheduling and progress tracking.

8.2Evaluation of Product
Conformance to specification (Vertical): The final product will be evaluated against all requirements listed in Section 3 to verify that it meets the predefined specifications.
Comparison with other systems (Horizontal): A comparative analysis will be conducted against 1-2 mainstream competitors (e.g., freeCodeCamp) on aspects such as user experience, feedback quality, and learning path design.
Client/user evaluation: A user study is planned, involving 4 students from the University of Birmingham with no prior programming experience. Participants will be asked to complete the first three course units. Data will be collected via questionnaires and semi-structured interviews to evaluate the system's usability and effectiveness.
Pedagogical Foundation   (Intelligent Tutoring System, ITS)
1.Scaffolding
The first idea is Scaffolding, which comes from the theory of ZPD (Zone of Proximal Development).
ZPD is the gap between what a student can do alone, and what they can do with help.
Scaffolding means giving the right help to cross this gap. For example, giving hints.
The system gives help when the student needs it, and removes the help when the student gets better.
2. Instant Feedback
The second idea is Instant Feedback.
Studies show that beginners need feedback fast to fix mistakes and stay motivated.
My system gives feedback right away. This is a big advantage.
My three models (Learner, Content, Interaction) use these two main ideas.

Learner Model
1. Overview
Definition  
The learner model is a live profile that the system keeps for every user. It stores what the learner knows, how the learner studies, and past results.

Main goals
Personalised teaching:give real-time data to the content model and the interaction model, so the system can show the right lesson and the right feedback.
Measured progress:turn actions into numbers (finished / tries / average time) so we can analyse learning and judge the system.

For the Learner Model, I use the Overlay Model.
The idea is simple: The system has a map of all topics. The Overlay Model tracks which topics the student has "completed" on that map.
In my system: The User_Progress table is the Overlay Model. It stores the status (completed, in_progress) for each user and each lesson.

2.Design principles
Principle	Meaning
Focus on core	First build progress tracking, status control, and performance record.
Keep it small	Use simple tables and rule-based updates; finishable in a three-month project.
Data-driven	Only user actions change the model; all advice comes from stored data.






3.Database schema
Table	Key columns	Purpose
Users	user_id PK, username, password_hash, created_at	Basic user info.
User_Progress	progress_id PK, user_id FK, lesson_id FK, status (not_started / in_progress / completed), attempts, avg_time_sec	Track state for each lesson.
Submissions	submission_id PK, user_id FK, exercise_id FK, is_correct, error_type, exec_time_sec, timestamp	Full history of code runs.

Personalisation signals
Attempts:how many tries the user made on this lesson.
avg_time_sec:average solving time; shows how “stuck” the learner is.
Example: if the average time is 1.5 × site average or tries > 3, the UI shows a small hint button.

4.Update flow (rule summary)
Situation	Trigger	Main database actions
First visit	open lesson or first submit	insert row in User_Progress, set status='in_progress', attempts=0, avg_time_sec=NULL
Wrong answer	is_correct = False	insert row in Submissions; attempts += 1; recalculate avg_time_sec
Correct answer	is_correct = True	insert row in Submissions; set status='completed'; update attempts, avg_time_sec


5.Links to other models
Content model uses to decide if the next lesson is unlocked.status
Interaction model uses and to decide when to show hints or examples.attempts avg_time_sec

6.Example run
User 101 studies Lesson 2.
First open → status = in_progress attempts = 0
First wrong submit (15.5 s) → attempts = 1 avg_time_sec = 15.5
Second submit correct (20.2 s) → status = completed attempts = 2 avg_time_sec = 17.85

7. Summary
With only three tables the model captures progress and two easy signals for personal help. This is enough for the MVP. More fine-grained error analysis or a probability mastery model(Bayesian Knowledge Tracking) can be added later.


Content Model
1.Overview
Definition :The content model is a structured map that shows what we teach in an entry-level Python course and in which order.

Main goals
Structured content :break the topic into Modules → Lessons(KCs) → Exercises.
Guided path :each lesson can point to a prerequisite lesson. A learner must finish the prerequisite before opening the next lesson.
Easy auto-grading :every exercise stores standard test cases, so the system can check the code at once.

For the Content Model, I use Knowledge Components, or KCs.
A KC is one small piece of knowledge. For example, a "variable" is one KC, and a "for loop" is another KC.
In my system: Each row in the Lessons table is one KC.
The model also defines the order to learn the KCs (the prerequisites).

2.Design principles
Principle	Short note
Structured & modular	clear hierarchy: Module → Lesson(KCs) → Exercise → Test Case
Dependency navigation	field prerequisite_lesson_id locks or unlocks a lesson
Practice first	every lesson has at least one coding task; pass the task to unlock the next lesson

3.Database schema
Table	Key columns	Purpose
Modules	module_id PK, title, description, order_index	top-level group (e.g. “Control Flow”)
Lessons	lesson_id PK, module_id FK, title, content_md, prerequisite_lesson_id, order_index	one theory page plus tasks
Exercises	exercise_id PK, lesson_id FK, problem_statement, hints (JSON)	coding task and optional hints
Test_Cases	test_case_id PK, exercise_id FK, input_data, expected_output	unit tests for auto-grading

4.Links to other models
Unlock rule (called by the interaction model):
User asks for Lesson B.
System reads .prerequisite_lesson_id
If not completed in , the system says “Please finish Lesson A first”.User_Progress
Otherwise it sends Lesson B, the exercise and the test cases.

5.Summary
With four small tables and one prerequisite field, the content is structured, navigable and auto-gradable. Together with the learner model and the interaction model it gives each learner the right topic at the right time and instant feedback on code.

Interaction Model
1.Overview
Definition:The interaction model is a rule-based layer that tells the system how to react to every user action. It answers the question “How should the system teach?”

Main goals
Data-driven decisions :use data from the learner model (status, tries, time) and the content model (prerequisites) to choose each next step.
Scaffolding support :give graded hints when a learner is in trouble; never show the full answer at once.
Automatic learning loop :keep repeating: show content → let user code → auto-check → give feedback / unlock next lesson.

2.Design principles
Principle	Meaning
Rule-driven	Clear “if-then” rules, no heavy AI; easy to build and explain.
Constructive feedback	Feedback is specific and helpful; success is praised quickly.
Minimal intervention	Let users explore; step in only after clear trouble signs (many errors or long delay).

3.Core rules 
Scenario	Trigger	System action
Open a lesson	User asks for Lesson B; Lesson B has prerequisite Lesson A	If User_Progress(A).status ≠ completed → show message “Please finish Lesson A first.” Otherwise send Lesson B page and its exercise.
Submit code – correct	is_correct = True	Set status = completed; send positive message “Great, lesson completed!”
Submit code – wrong output	is_correct = False (no exception)	Add failure record; attempts += 1; update avg_time_sec; show diff: “Expected …, your result …”
Submit code – runtime error	Code raises SyntaxError, NameError, …	Add failure record; update counts; show hint matched to error_type, e.g. “SyntaxError: missing colon on line 3?”
Hint by tries	status = in_progress and attempts ≥ 3	Show a non-intrusive button “Need a hint?”
Hint by time	status = in_progress and avg_time_sec > 90 s	Auto pop-up: “It seems you are stuck. Need help?”
Give graded hint	User clicks “Need a hint?”	Return next item from hints[] list (first click → first hint, second → second hint, …)

4.How the three models work together

sequenceDiagram
  participant U as Learner
  participant I as Interaction Layer
  participant C as Content Model
  participant L as Learner Model

  %% ---------- lesson request ----------
  U->>I: request Lesson B
  I->>C: query B.prerequisite
  C-->>I: prerequisite = Lesson A
  I->>L: read Progress(A).status
  alt not completed
    I-->>U: "Please complete Lesson A first."
  else completed
    I-->>U: deliver Lesson B page + exercise
  end

  %% ---------- code submission ----------
  U->>I: submit code
  I->>Server: POST /api/submit
  Server-->>I: is_correct, error_type

  alt correct
    I->>L: set status = completed
    I-->>U: "Great, lesson finished."
  else incorrect
    I->>L: attempts ++, update avg_time
    I-->>U: detailed feedback
    alt hint threshold reached
      I-->>U: show scaffold hint
    end
  end

5. Summary
With a small set of transparent rules, this model links what to learn (content) and who is learning (learner data). The rules map directly to backend APIs and front-end messages, so the system is simple, explainable, and efficient for personalisation.

Teaching Material Design

1.Pedagogical Approach
The success of an educational system depends equally on its technical implementation and the quality of its teaching content. The design of our teaching materials is guided by the following principles:

Microlearning: We break down complex programming topics into small, "bite-sized" lessons. Each lesson focuses on one core concept and is designed to be completed in 10-15 minutes. This approach helps to reduce the cognitive load on beginners.

Practice-Oriented: We believe in "Learning by Doing" as the most effective way to learn programming. Every theoretical explanation is immediately followed by a hands-on coding exercise to reinforce the new knowledge.

Scaffolded Support: When a learner gets stuck, the system provides graded hints. These hints guide the learner step-by-step, starting with a general direction and leading to more specific advice, without giving away the final answer directly.

2.Curriculum Outline (with Prerequisites)
Module	Lesson (ID & Title)	Prerequisite Lesson ID
M1 First Steps	1.1 What is Programming?	–
	1.2 First program print("Hello, World!")	1.1
	1.3 Comments in code	1.2
	1.4 Variables	1.3
	1.5 Data types: numbers & text	1.4
M2 Working with Data	2.1 Strings	1.5
	2.2 Numbers & maths	1.5
	2.3 User input input()	2.1 or 2.2
M3 Logic & Control	3.1 Booleans & comparisons	1.5
	3.2 Conditionals if elif else	3.1
	3.3 Lists	1.5
	3.4 for loop	3.3
	3.5 while loop	3.2
M4 Structuring Code	4.1 Functions	3.2
	4.2 Function parameters & returns	4.1

3.Exercise and Feedback Design

Each exercise is designed based on the following principles to maximize learning effectiveness:
Clear Instructions: The goal of each task is described in simple, plain language, avoiding technical jargon wherever possible.
Single-Objective: Each exercise is designed to test one core concept at a time. This prevents confusion by not mixing multiple new ideas.
Comprehensive Test Cases: Every exercise is supported by multiple test cases to ensure the user's code is robust. This includes:
Standard Cases: To check if the basic functionality is correct.
Edge Cases: To check special or tricky situations (e.g., using zero, an empty list, or negative numbers).

High-Quality, Graded Hints: Each exercise has 2-3 pre-written hints. For an exercise that asks to sum all numbers in a list, the hints would be:
Hint 1: "You might need a loop to go through each number in the list."
Hint 2: "Don't forget to create a variable (like total_sum = 0) before the loop to store the sum."
Hint 3: "Take a look at the for element in my_list: syntax. It might be helpful."

Technical Implementation Plan
1.Overview
This plan explains how the three conceptual models (Learner, Content, Interaction) will become a working web site. The goal is to use modern but lightweight tools that a student team can finish within one semester.

2.System Architecture
Layer	Role	Main tools
Frontend (SPA)	Shows the pages, code editor, and feedback	Vue 3, Fetch API, CodeMirror
Backend API	Handles login, progress, content lookup, hint logic	Flask 2 (Python 3)
Code-Execution Service	Runs learner code safely in a container	External API (Judge0 or Piston)

3.Technology Stack
Component	Choice	Why this choice works for beginners
Backend	Python + Flask	Same language as the course, tiny learning curve, huge community.
Frontend	Vue.js	Easiest modern framework; reactive UI for live results.
Database	SQLite (dev) → PostgreSQL (deploy)	Start with zero config; switch to a stronger DB when needed.
Sandbox	Judge0 / Piston API	Removes the risk of building your own secure container.



Pedagogical Foundation   (Intelligent Tutoring System, ITS)
1.Scaffolding
The first idea is Scaffolding, which comes from the theory of ZPD (Zone of Proximal Development).
ZPD is the gap between what a student can do alone, and what they can do with help.
Scaffolding means giving the right help to cross this gap. For example, giving hints.
The system gives help when the student needs it, and removes the help when the student gets better.
2. Instant Feedback
The second idea is Instant Feedback.
Studies show that beginners need feedback fast to fix mistakes and stay motivated.
My system gives feedback right away. This is a big advantage.
My three models (Learner, Content, Interaction) use these two main ideas.

Learner Model
1. Overview
Definition  
The learner model is a live profile that the system keeps for every user. It stores what the learner knows, how the learner studies, and past results.

Main goals
Personalised teaching:give real-time data to the content model and the interaction model, so the system can show the right lesson and the right feedback.
Measured progress:turn actions into numbers (finished / tries / average time) so we can analyse learning and judge the system.

For the Learner Model, I use the Overlay Model.
The idea is simple: The system has a map of all topics. The Overlay Model tracks which topics the student has "completed" on that map.
In my system: The User_Progress table is the Overlay Model. It stores the status (completed, in_progress) for each user and each lesson.

2.Design principles
Principle	Meaning
Focus on core	First build progress tracking, status control, and performance record.
Keep it small	Use simple tables and rule-based updates; finishable in a three-month project.
Data-driven	Only user actions change the model; all advice comes from stored data.






3.Database schema
Table	Key columns	Purpose
Users	user_id PK, username, password_hash, created_at	Basic user info.
User_Progress	progress_id PK, user_id FK, lesson_id FK, status (not_started / in_progress / completed), attempts, avg_time_sec	Track state for each lesson.
Submissions	submission_id PK, user_id FK, exercise_id FK, is_correct, error_type, exec_time_sec, timestamp	Full history of code runs.

Personalisation signals
Attempts:how many tries the user made on this lesson.
avg_time_sec:average solving time; shows how “stuck” the learner is.
Example: if the average time is 1.5 × site average or tries > 3, the UI shows a small hint button.

4.Update flow (rule summary)
Situation	Trigger	Main database actions
First visit	open lesson or first submit	insert row in User_Progress, set status='in_progress', attempts=0, avg_time_sec=NULL
Wrong answer	is_correct = False	insert row in Submissions; attempts += 1; recalculate avg_time_sec
Correct answer	is_correct = True	insert row in Submissions; set status='completed'; update attempts, avg_time_sec


5.Links to other models
Content model uses to decide if the next lesson is unlocked.status
Interaction model uses and to decide when to show hints or examples.attempts avg_time_sec

6.Example run
User 101 studies Lesson 2.
First open → status = in_progress attempts = 0
First wrong submit (15.5 s) → attempts = 1 avg_time_sec = 15.5
Second submit correct (20.2 s) → status = completed attempts = 2 avg_time_sec = 17.85

7. Summary
With only three tables the model captures progress and two easy signals for personal help. This is enough for the MVP. More fine-grained error analysis or a probability mastery model(Bayesian Knowledge Tracking) can be added later.


Content Model
1.Overview
Definition :The content model is a structured map that shows what we teach in an entry-level Python course and in which order.

Main goals
Structured content :break the topic into Modules → Lessons(KCs) → Exercises.
Guided path :each lesson can point to a prerequisite lesson. A learner must finish the prerequisite before opening the next lesson.
Easy auto-grading :every exercise stores standard test cases, so the system can check the code at once.

For the Content Model, I use Knowledge Components, or KCs.
A KC is one small piece of knowledge. For example, a "variable" is one KC, and a "for loop" is another KC.
In my system: Each row in the Lessons table is one KC.
The model also defines the order to learn the KCs (the prerequisites).

2.Design principles
Principle	Short note
Structured & modular	clear hierarchy: Module → Lesson(KCs) → Exercise → Test Case
Dependency navigation	field prerequisite_lesson_id locks or unlocks a lesson
Practice first	every lesson has at least one coding task; pass the task to unlock the next lesson

3.Database schema
Table	Key columns	Purpose
Modules	module_id PK, title, description, order_index	top-level group (e.g. “Control Flow”)
Lessons	lesson_id PK, module_id FK, title, content_md, prerequisite_lesson_id, order_index	one theory page plus tasks
Exercises	exercise_id PK, lesson_id FK, problem_statement, hints (JSON)	coding task and optional hints
Test_Cases	test_case_id PK, exercise_id FK, input_data, expected_output	unit tests for auto-grading

4.Links to other models
Unlock rule (called by the interaction model):
User asks for Lesson B.
System reads .prerequisite_lesson_id
If not completed in , the system says “Please finish Lesson A first”.User_Progress
Otherwise it sends Lesson B, the exercise and the test cases.

5.Summary
With four small tables and one prerequisite field, the content is structured, navigable and auto-gradable. Together with the learner model and the interaction model it gives each learner the right topic at the right time and instant feedback on code.

Interaction Model
1.Overview
Definition:The interaction model is a rule-based layer that tells the system how to react to every user action. It answers the question “How should the system teach?”

Main goals
Data-driven decisions :use data from the learner model (status, tries, time) and the content model (prerequisites) to choose each next step.
Scaffolding support :give graded hints when a learner is in trouble; never show the full answer at once.
Automatic learning loop :keep repeating: show content → let user code → auto-check → give feedback / unlock next lesson.

2.Design principles
Principle	Meaning
Rule-driven	Clear “if-then” rules, no heavy AI; easy to build and explain.
Constructive feedback	Feedback is specific and helpful; success is praised quickly.
Minimal intervention	Let users explore; step in only after clear trouble signs (many errors or long delay).

3.Core rules 
Scenario	Trigger	System action
Open a lesson	User asks for Lesson B; Lesson B has prerequisite Lesson A	If User_Progress(A).status ≠ completed → show message “Please finish Lesson A first.” Otherwise send Lesson B page and its exercise.
Submit code – correct	is_correct = True	Set status = completed; send positive message “Great, lesson completed!”
Submit code – wrong output	is_correct = False (no exception)	Add failure record; attempts += 1; update avg_time_sec; show diff: “Expected …, your result …”
Submit code – runtime error	Code raises SyntaxError, NameError, …	Add failure record; update counts; show hint matched to error_type, e.g. “SyntaxError: missing colon on line 3?”
Hint by tries	status = in_progress and attempts ≥ 3	Show a non-intrusive button “Need a hint?”
Hint by time	status = in_progress and avg_time_sec > 90 s	Auto pop-up: “It seems you are stuck. Need help?”
Give graded hint	User clicks “Need a hint?”	Return next item from hints[] list (first click → first hint, second → second hint, …)

4.How the three models work together

sequenceDiagram
  participant U as Learner
  participant I as Interaction Layer
  participant C as Content Model
  participant L as Learner Model

  %% ---------- lesson request ----------
  U->>I: request Lesson B
  I->>C: query B.prerequisite
  C-->>I: prerequisite = Lesson A
  I->>L: read Progress(A).status
  alt not completed
    I-->>U: "Please complete Lesson A first."
  else completed
    I-->>U: deliver Lesson B page + exercise
  end

  %% ---------- code submission ----------
  U->>I: submit code
  I->>Server: POST /api/submit
  Server-->>I: is_correct, error_type

  alt correct
    I->>L: set status = completed
    I-->>U: "Great, lesson finished."
  else incorrect
    I->>L: attempts ++, update avg_time
    I-->>U: detailed feedback
    alt hint threshold reached
      I-->>U: show scaffold hint
    end
  end

5. Summary
With a small set of transparent rules, this model links what to learn (content) and who is learning (learner data). The rules map directly to backend APIs and front-end messages, so the system is simple, explainable, and efficient for personalisation.

Teaching Material Design

1.Pedagogical Approach
The success of an educational system depends equally on its technical implementation and the quality of its teaching content. The design of our teaching materials is guided by the following principles:

Microlearning: We break down complex programming topics into small, "bite-sized" lessons. Each lesson focuses on one core concept and is designed to be completed in 10-15 minutes. This approach helps to reduce the cognitive load on beginners.

Practice-Oriented: We believe in "Learning by Doing" as the most effective way to learn programming. Every theoretical explanation is immediately followed by a hands-on coding exercise to reinforce the new knowledge.

Scaffolded Support: When a learner gets stuck, the system provides graded hints. These hints guide the learner step-by-step, starting with a general direction and leading to more specific advice, without giving away the final answer directly.

2.Curriculum Outline (with Prerequisites)
Module	Lesson (ID & Title)	Prerequisite Lesson ID
M1 First Steps	1.1 What is Programming?	–
	1.2 First program print("Hello, World!")	1.1
	1.3 Comments in code	1.2
	1.4 Variables	1.3
	1.5 Data types: numbers & text	1.4
M2 Working with Data	2.1 Strings	1.5
	2.2 Numbers & maths	1.5
	2.3 User input input()	2.1 or 2.2
M3 Logic & Control	3.1 Booleans & comparisons	1.5
	3.2 Conditionals if elif else	3.1
	3.3 Lists	1.5
	3.4 for loop	3.3
	3.5 while loop	3.2
M4 Structuring Code	4.1 Functions	3.2
	4.2 Function parameters & returns	4.1

3.Exercise and Feedback Design

Each exercise is designed based on the following principles to maximize learning effectiveness:
Clear Instructions: The goal of each task is described in simple, plain language, avoiding technical jargon wherever possible.
Single-Objective: Each exercise is designed to test one core concept at a time. This prevents confusion by not mixing multiple new ideas.
Comprehensive Test Cases: Every exercise is supported by multiple test cases to ensure the user's code is robust. This includes:
Standard Cases: To check if the basic functionality is correct.
Edge Cases: To check special or tricky situations (e.g., using zero, an empty list, or negative numbers).

High-Quality, Graded Hints: Each exercise has 2-3 pre-written hints. For an exercise that asks to sum all numbers in a list, the hints would be:
Hint 1: "You might need a loop to go through each number in the list."
Hint 2: "Don't forget to create a variable (like total_sum = 0) before the loop to store the sum."
Hint 3: "Take a look at the for element in my_list: syntax. It might be helpful."

Technical Implementation Plan
1.Overview
This plan explains how the three conceptual models (Learner, Content, Interaction) will become a working web site. The goal is to use modern but lightweight tools that a student team can finish within one semester.

2.System Architecture
Layer	Role	Main tools
Frontend (SPA)	Shows the pages, code editor, and feedback	Vue 3, Fetch API, CodeMirror
Backend API	Handles login, progress, content lookup, hint logic	Flask 2 (Python 3)
Code-Execution Service	Runs learner code safely in a container	External API (Judge0 or Piston)

3.Technology Stack
Component	Choice	Why this choice works for beginners
Backend	Python + Flask	Same language as the course, tiny learning curve, huge community.
Frontend	Vue.js	Easiest modern framework; reactive UI for live results.
Database	SQLite (dev) → PostgreSQL (deploy)	Start with zero config; switch to a stronger DB when needed.
Sandbox	Judge0 / Piston API	Removes the risk of building your own secure container.

















2.Literature Review
2.1Introduction
This chapter provides a comprehensive review of existing literature on online programming education and intelligent tutoring systems, in order to establish the theoretical and practical context for this project. The review follows a “funnel” approach: beginning with the broad landscape of e-learning for programming, then narrowing to specific challenges faced by novice learners and relevant educational theories, and finally examining current platforms and identifying a gap that motivates the present work. The chapter first explores the rise of online programming learning platforms and the importance of interactive practice. It then discusses foundational pedagogical theories – notably Vygotsky’s Zone of Proximal Development and the concept of scaffolding – as well as the critical role of timely feedback for novices. Next, it introduces a framework for analyzing intelligent learning environments, focusing on the learner model, content (domain) model, and interaction (tutoring) model. Using this framework, a critical evaluation of several existing learning platforms (e.g. Codecademy, freeCodeCamp) is presented to highlight their strengths and limitations. Finally, the chapter identifies a specific gap in the current landscape – namely, the lack of adaptive, personalized support for novice programmers – and justifies how our project will address this gap.
2.2The Landscape of Online Programming Education
In recent years, programming education has increasingly moved from traditional classrooms to online environments. Massive Open Online Courses (MOOCs) and interactive coding websites have surged in popularity, making programming instruction widely accessible. For example, platforms like Codecademy and freeCodeCamp have attracted millions of learners by offering free, self-paced coding lessons with hands-on exercises. Codecademy, in particular, is known for its interactive coding tutorials designed to make learning to code engaging and beginner-friendly. These platforms typically combine short instructional texts or videos with an in-browser code editor, allowing learners to write and run code immediately. This immediate practice approach aligns with the well-established principle that learning-by-doing is crucial for mastering programming skills[1]. Studies have shown that active engagement through solving programming exercises leads to better knowledge retention and skill development than passive reading or watching lectures[2]. Interactive e-learning tools have also been linked to improved motivation and performance. For instance, a study found that students who used an online coding resource scored significantly higher on programming tests than those who learned via traditional methods, with a 20-point gain vs. 8-point gain in the control group[3]. Such findings underscore that providing plentiful hands-on practice and instant feedback in online platforms can substantially enhance learners’ success.

Despite their benefits and popularity, online coding platforms also highlight specific challenges in programming education. Novice programmers often struggle with abstract concepts, complex syntax, and debugging – difficulties that can be exacerbated when learning independently online. A common obstacle in MOOCs and websites is the lack of immediate, personalized support when a learner gets stuck. Traditional classroom settings allow students to ask instructors or peers for help in real time, whereas a solo online learner facing a confusing error might become frustrated or demotivated. Research indicates that novices can quickly lose confidence without timely guidance, contributing to high dropout rates in introductory programming courses[4]. Consequently, there is a growing recognition that simply providing content and exercises is not enough; the quality of interaction and feedback in these platforms is critical. Modern online coding education is thus increasingly informed by findings from educational psychology and learning science to keep beginners engaged[5]. Many platforms incorporate features like progress visualizations, badges, or basic hint systems to encourage persistence. Gamification elements (points, challenges) and community forums for peer support (as used in freeCodeCamp) are also employed to mitigate feelings of isolation. However, as will be discussed, the depth of pedagogical support in these platforms often remains limited[6]. This sets the stage for examining theoretical frameworks that can inform better designs.

2.3Foundational Pedagogical Theories for Novice Programmers
Effective learning environments for novice programmers draw on several foundational pedagogical theories. Three concepts are particularly relevant to this project’s design: (1) Vygotsky’s Zone of Proximal Development and the related notion of scaffolding, and (2) the importance of immediate, formative feedback.
2.3.1Zone of Proximal Development (ZPD) and Scaffolding
Vygotsky's theory identifies the ZPD as the crucial gap between what a learner can do independently and what they can achieve with guidance[7]. This concept is operationalized through scaffolding, a term coined to describe temporary, adaptive support that is gradually withdrawn as the learner gains competence[8]. For novice programmers, this support can manifest as hints, problem breakdowns, or partially completed examples. The key is that assistance must be minimal—just enough to enable the learner's next step without giving away the solution, thereby encouraging active problem-solving. The principle of scaffolding is a core pedagogical foundation for this project; the system will monitor learner performance and provide graduated, "just-in-time" and "just-enough" prompts when it detects struggle. As the learner improves, this support fades to promote independence.

2.3.2 Immediate and Formative Feedback

Another pillar of effective instruction is timely, specific feedback. Research has consistently shown that feedback is one of the most powerful influences on achievement, especially when it is formative—aimed at guiding improvement—and delivered immediately[9].A comprehensive review of the topic emphasizes that immediate feedback helps learners correct misunderstandings before they become ingrained, which is vital in programming where flawed mental models can persist[10]. Standard compiler errors, which are often cryptic and not pedagogically oriented, act as a barrier to persistence. A system that can intercept mistakes and provide clear, helpful cues can therefore dramatically improve the learning experience. The benefits of such adaptive feedback were demonstrated in a controlled study where an environment providing real-time, tailored hints significantly increased student engagement and their intent to persist in computer science [4]. The study also confirmed that well-designed feedback can reduce novice anxiety and foster a growth mindset, with students reporting the experience as more enjoyable and focused[4].

Therefore, the pedagogical groundwork for this project is clear: to create an optimal learning platform that operates within the learner’s ZPD by using scaffolded support and providing instant, informative feedback. The system is designed to continuously assess performance metrics (e.g., attempts, errors, time spent) and use these as cues to replicate the timely interventions of a human tutor, preventing frustration while fostering independence.

2.4Core Models of Intelligent Tutoring Systems (ITS)
To situate our proposed system within established educational-technology research, its design is grounded in the widely accepted architecture of Intelligent Tutoring Systems (ITS). An effective ITS is typically conceptualized as comprising three core components: a Learner Model, a Domain (Content) Model, and a Tutoring (Interaction) Model [11-13]. This separation of concerns is vital for designing intelligent learning environments, as it clarifies how the system uses different data: the domain model provides what to teach, the learner model tracks to whom it is being taught, and the tutoring model decides how to teach next[14].

Learner Model. A dynamic profile that represents the student’s current knowledge, skills, and learning history. Its primary role is to enable personalization by tracking what the student knows and does not know, often as an “overlay” of the expert domain knowledge [15]. Our project implements this as a simple yet effective overlay model, recording each user’s progress (e.g., which lessons are completed), performance metrics (e.g., number of attempts), and engagement patterns (e.g., time taken per exercise). This data is crucial for the tutoring model to make informed, adaptive decisions.

Domain (Content) Model. Encodes the subject matter to be taught—in our case, Python fundamentals. It is a structured representation of concepts, exercises, and, critically, the prerequisite relationships between them (e.g., “variables” must be mastered before “loops”) [13, 16]. This ensures a logical learning progression. In our system, the domain model is realized as a structured curriculum of lessons and exercises. Each exercise is annotated with a set of automated test cases for immediate evaluation and a list of tiered hints to support scaffolding. This model not only defines the content but also provides the resources for assessment and intervention.

Tutoring (Interaction) Model. Often called the pedagogical model, it functions as the system’s “teaching brain.” It uses rules or algorithms to interpret data from the other two models and decide on the next instructional action [12, 17]. This model operationalizes pedagogical theories such as scaffolding and mastery learning. Our system’s tutoring model is implemented as a set of direct, rule-based heuristics that govern the platform’s interactivity. For instance, it enforces the prerequisite logic from the domain model, unlocking content only when a user’s learner model shows completion of the required prior lesson.

Crucially, this model orchestrates the feedback loop. To illustrate, consider how the models work in concert: when a student submits an incorrect solution, the system does not simply return a raw compiler error. Instead, the tutoring model intervenes. It references the learner model to see whether this is the first or a repeated attempt. Based on this context, it might select a specific, targeted hint from the domain model’s pre-authored list. For example, a rule might state: “If the learner model shows more than three failed attempts on an exercise, provide the first hint from the domain model.” After a successful submission, the tutoring model provides positive reinforcement and signals the learner model to update the student’s progress, marking the concept as “mastered.”

By explicitly adopting this three-model framework, we ensure our system’s design is not based on ad-hoc decisions but is guided by decades of proven ITS research [12, 17]. This structure provides a clear rationale for our implementation choices, detailed in the next chapter, and lends credibility to our approach by aligning it with established paradigms for creating effective, adaptive learning environments.

2.5Critical Evaluation of Existing Systems
With the theoretical framework established, this section critically evaluates representative online programming platforms through the analytical lens of the Learner, Content, and Interaction models. The objective is to identify common strengths and, more importantly, pedagogical weaknesses, thereby locating the research gap our project aims to address.
2.5.1Codecademy
Codecademy serves as a prime example of a platform with a highly-refined Content Model but a less developed Interaction and Learner Model. Its primary strength lies in a well-structured, linear curriculum that breaks down complex topics into "bite-sized" lessons[18]. This incremental approach, combined with an interactive coding environment and gamified elements like progress streaks, is effective for user engagement and introducing basic syntax[19].

However, a critical analysis of its pedagogical support reveals significant limitations. The platform's Interaction Model primarily functions as a simple pass/fail evaluator, checking a user's code against a predetermined solution. It offers limited adaptive feedback or scaffolded hints that guide a learner through their specific misunderstanding. This is largely because its Learner Model is rudimentary, tracking little more than lesson completion status. It does not capture crucial performance metrics—such as repeated attempts or common error patterns—that would be necessary to infer a learner's cognitive state and trigger a more supportive intervention.
This "one-size-fits-all" approach has been a subject of criticism in the literature. Research has highlighted that while Codecademy's "hand-holding" style makes the initial learning curve smooth, it can fail to cultivate independent problem-solving skills [18]. Learners may develop an "illusion of competence," successfully completing syntax puzzles within the platform's constraints but struggling to apply their knowledge to novel, real-world problems. The system's inability to adapt means it is simultaneously too rigid for advanced learners and potentially unsupportive for novices who require more than generic error messages to overcome conceptual hurdles.
In conclusion, while Codecademy provides an accessible and engaging entry point to programming, its pedagogical model lacks the depth to provide true adaptive scaffolding. The shallow integration between its Interaction and Learner Models highlights a clear opportunity for systems that can offer more personalized, data-driven, and pedagogically-informed guidance to foster robust and transferable programming skills.

2.5.2freeCodeCamp

freeCodeCamp offers a different pedagogical approach, leveraging a project-oriented Content Model to enhance skill transfer, but it shares with Codecademy similarly underdeveloped Learner and Interaction Models. Its strength lies in guiding learners to apply fundamental concepts in tangible portfolio projects, addressing a common criticism of other platforms that exercises can feel abstract and isolated. This emphasis on practical application is a significant pedagogical advantage[20].

However, the platform's adaptive support mechanism is minimal. Like Codecademy, its Learner Model is rudimentary, primarily tracking challenge completion rather than diagnosing misconceptions. Consequently, its Interaction Model cannot offer personalized, adaptive feedback. While it provides immediate feedback via pass/fail test cases, this is often insufficient for a struggling novice[21]. The platform's solution to this is to outsource pedagogical support to its community forum. Rather than the system providing scaffolded hints, the learner is expected to seek help from peers or static articles when they get stuck .

This reliance on external support highlights the central weakness: freeCodeCamp excels at providing curriculum scaffolding (a logical progression of projects with increasing complexity) but lacks adaptive scaffolding at the moment-to-moment problem-solving level. A learner encountering a common bug must rely on their own debugging skills or the availability and quality of community help, a process that can lead to significant frustration and attrition. Therefore, while its project-based model is valuable, freeCodeCamp exemplifies the gap this project seeks to fill: the absence of an integrated, intelligent tutoring layer that can provide real-time, context-aware guidance when a learner needs it most[22].

2.5.3Other Intelligent Tutoring Systems for Programming

Beyond commercial platforms, academic research in Intelligent Tutoring Systems (ITS) provides valuable insights into more sophisticated models of adaptivity. These systems, however, often employ different underlying approaches. A notable example is ITAP (Intelligent Teaching Assistant for Programming), a data-driven system that generates hints from mined solution spaces [23]. Rather than relying on a pre-authored knowledge base, ITAP functions by abstracting a student's code into a state within a "solution space" graph. It then automatically generates a path from the student's current incorrect state to the nearest correct one, producing a targeted, next-step hint. While the authors note that a full evaluation with students has not yet been performed, technical evaluations demonstrate that the system can generate viable hints from almost any intermediate state of a student's code, showing high coverage and potential [23].

Another line of research has explored Parsons Problems, which require learners to arrange mixed-up code blocks into the correct order. These tutors implement scaffolding through pre-set difficulty levels, such as by adding distractor blocks to increase complexity or by providing the initial block to reduce it, rather than through real-time dynamic adaptation based on performance. Studies have found that this form of scaffolding helps novices learn programming constructs more effectively than writing code from scratch with no guidance [24].

While more advanced systems, represented by examples like ITAP, are closer to implementing the full three-model ITS architecture, many research prototypes like Parsons Problems tutors primarily focus on task-level scaffolding without a persistent learner model. A common trade-off is that these sophisticated academic systems are often domain-specific and resource-intensive to develop, which explains their limited adoption by scalable commercial platforms. This leads to a critical insight: a gap persists between the engaging, scalable nature of mainstream platforms and the pedagogically powerful but limited scope of ITS prototypes. This conclusion is strongly supported by a systematic review of 101 programming learning tools, which found that the majority provided only basic error feedback, with little personalization or guidance on how to improve [25]. This clearly indicates a space for innovation for a platform that integrates the user-friendliness of popular MOOCs with the adaptive, supportive feedback mechanisms demonstrated in ITS literature.

To visually synthesize the preceding analysis, Table 2.1 provides a side-by-side comparison of these representative systems across several key pedagogical dimensions.
Feature / Dimension	Codecademy	freeCodeCamp	Academic ITS Prototype(e.g., ITAP)	Our Proposed System
Content Model	Structured, linear micro-lessons with in-browser exercises.	Project-driven curriculum that culminates in portfolio projects.	Narrow, research-specific domain with fine-grained concept mapping.	Modular curriculum; every Knowledge Component is tied to auto-graded exercises and tiered hints.
Learner Model	Basic overlay—tracks only lesson completion.	Certification-oriented—tracks finished challenges & projects.	Fine-grained knowledge tracing; infers mastery and solution paths.	Enhanced overlay—records completion plus attempts, average time, and common error types for intervention.
Interaction & Feedback	Immediate but generic pass/fail; compiler errors largely unchanged.	Immediate but non-adaptive; pass/fail from unit tests, community forum for help.	Highly targeted: generates next-step hints from the learner’s exact code state.	Rule-based adaptive engine: error-specific guidance (e.g., SyntaxError), proactive hint button when attempts > 3, etc.
Scaffolding & Adaptivity	Low—one-size-fits-all path, static hints.	Low—project progression but no real-time personalization.	High—graded hints that fade as competence grows.	Medium–High—dynamic, graded scaffolding triggered by learner metrics; aims to bridge commercial ease with ITS rigor.
Key Strength	Very low entry barrier; strongly guided onboarding.	Emphasizes real-world application and skill transfer.	Theoretically the most effective personalized guidance.	Combines mainstream usability with intelligent tutoring—adaptive support for true novices.
Key Limitation	May foster an “illusion of competence”; weak independent problem-solving.	No built-in tutoring; high risk of frustration when stuck.	Resource-intensive; hard to scale beyond a narrow domain.	Effectiveness depends on quality of author-defined rules and pedagogical content.
Table 2.1: A Comparative Analysis of Online Programming Platforms and the Proposed System
As visually synthesized in Table 2.1, the current landscape of online programming education presents a distinct trade-off. Mainstream platforms like Codecademy and freeCodeCamp offer impressive scalability and accessibility but often lack deep, personalized pedagogical support. Conversely, academic ITS prototypes demonstrate powerful adaptive mechanisms but remain difficult to scale. This dichotomy highlights a clear and compelling gap for a system that can bridge these two worlds.

2.6Identifying the Research Gap and Justifying the Project

Drawing directly from the insights synthesized in Table 2.1, the research gap this project addresses is the absence of a platform that successfully merges the broad accessibility of mainstream e-learning sites with the pedagogically-informed, adaptive support found in specialized Intelligent Tutoring Systems.

This deficiency manifests in several critical ways for novice learners, particularly those outside of computer science majors. On many large-scale platforms, they receive feedback that is merely binary (correct/incorrect) or overly generic, with minimal consideration of their individual learning needs. As a result, when beginners encounter obstacles, such as a confusing error or a difficult concept, the system offers little targeted assistance beyond showing a solution, which can lead to frustration, stagnation, or reliance on external help sources. From an educational theory standpoint, these platforms do not fully leverage scaffolding principles: they fail to provide "just-right" support in the Zone of Proximal Development, nor do they fade that support appropriately. Furthermore, their learner modeling is often primitive, typically limited to tracking progress rather than diagnosing misconceptions or adapting to a learner’s pace.
There is thus a demonstrable need for a programming learning system that can dynamically assess a learner’s difficulties and respond with personalized, scaffolded support. This system would monitor signals like repeated failed attempts, unusual delay in solving a problem, or common error patterns (as stored in a learner model), and use those to trigger helpful interventions via the interaction model. For instance, if a student has attempted an exercise multiple times and keeps encountering a SyntaxError, the system could proactively offer a hint about the correct syntax or point out the error location in a friendly manner. If a student’s average solving time on recent tasks is far above their norm, the system could suggest a review of prerequisite material or provide a more step-by-step breakdown of the current problem. Essentially, the gap is the absence of an “online coding tutor” that replicates the guidance a human teacher might provide during practice—encouraging, giving tips, and ensuring the student does not remain stuck for too long on any single hurdle.
Additionally, our review highlighted that even when hints are available in existing platforms, they are typically static and not context-aware. The literature suggests that making feedback context-specific and incremental (from gentle nudges to detailed explanations) is far more effective for learning[4, 25]. Therefore, another aspect of the gap is qualitative: it’s not just about having any feedback, but having the right kind of feedback at the right time. The system we propose aims to fill this qualitative gap by integrating research-backed feedback strategies (immediate, specific, and scaffolded) into the learning loop of an interactive coding website.
From a practical perspective, addressing this gap could significantly improve learning outcomes and learner retention. Beginners, particularly non-CS students, would benefit from a platform that not only teaches them syntax and concepts, but also teaches them how to learn and debug code. Instead of being left to either sink or find help elsewhere, novices would have a safety net that catches misconceptions early and guides them back on track. This supportive learning environment could reduce the frustration barrier that now causes many to abandon programming early in the learning process.

In conclusion, the unique contribution of this project is the development of a web-based programming learning platform that integrates a novice-centric adaptive tutoring mechanism into an interactive coding practice environment. By combining the scalability and content richness of existing e-learning platforms with the personalized feedback mechanisms found in intelligent tutoring systems, the project aims to create a more effective and engaging learning experience for beginners. This approach directly addresses the identified gap in literature and practice. No longer is the project just an arbitrary idea – it stands as a response to concrete shortcomings documented in prior work. In the next chapters, we will detail the design and implementation of this system, demonstrating how each component (learner model, content model, interaction model) has been realized to achieve the goal of filling this educational gap.

2.7Summary

In this chapter, we surveyed the landscape of online programming education and examined key educational theories relevant to teaching novices. We discussed how scaffolding and immediate feedback are critical for beginner engagement, and described the standard learner/content/tutoring model framework for intelligent learning systems. Through a critical analysis of existing platforms (Codecademy, freeCodeCamp, etc.), we identified that while current solutions excel in accessibility and content delivery, they often lack adaptive guidance for students when they struggle. This analysis led us to pinpoint a gap: the need for an interactive learning platform that provides personalized, scaffolded support to novice programmers. The chapter established the theoretical foundation and justification for our project – a system explicitly designed to fill this gap. In the next chapter, we transition from “what” and “why” to “how,” by outlining the requirements and design of the proposed solution in light of the insights gained here.