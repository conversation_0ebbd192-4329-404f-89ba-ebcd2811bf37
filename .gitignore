# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/

# Flask
instance/
.webassets-cache
.env

# Database
*.db
*.db-journal
*.backup_*

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Node.js / Frontend
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn/
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
temp/
tmp/

# API Keys (重要：不要提交API密钥)
*api_key*
*secret*
config.json
backend/.env
frontend/.env.local

# Database backups
*.backup_*
backend/instance/learning_platform.db*

# Migration files (不需要提交到Git)
migrate_*.py
*.backup

# OS generated files
.directory
.fuse_hidden*
.nfs*

# Editor files
*.sublime-project
*.sublime-workspace

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Testing
.pytest_cache/
.coverage

# 测试和调试文件
test_*.py
debug_*.py
check_*.py
*_test.py
*_debug.py

# Deployment
*.tar.gz
*.zip

.github/
.claude/