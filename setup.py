#!/usr/bin/env python3
"""
交互式Python学习平台 - 一键安装脚本
自动化安装和配置整个项目环境
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

class Colors:
    """终端颜色定义"""
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(text, color=Colors.BLUE):
    """打印彩色文本"""
    print(f"{color}{text}{Colors.END}")

def print_step(step_num, description):
    """打印步骤信息"""
    print_colored(f"\n📋 步骤 {step_num}: {description}", Colors.BOLD)

def print_success(text):
    """打印成功信息"""
    print_colored(f"✅ {text}", Colors.GREEN)

def print_warning(text):
    """打印警告信息"""
    print_colored(f"⚠️  {text}", Colors.YELLOW)

def print_error(text):
    """打印错误信息"""
    print_colored(f"❌ {text}", Colors.RED)

def run_command(command, description="", check=True, cwd=None):
    """运行命令并处理结果"""
    if description:
        print(f"   执行: {description}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        if result.stdout:
            print(f"      {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"命令执行失败: {command}")
        if e.stderr:
            print(f"      错误: {e.stderr.strip()}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_error("需要Python 3.8或更高版本")
        return False
    print_success(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print_success(f"Node.js版本检查通过: {version}")
            return True
    except FileNotFoundError:
        pass
    
    print_warning("未找到Node.js，将尝试自动安装...")
    return False

def install_backend_dependencies():
    """安装后端依赖"""
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print_error("未找到backend目录")
        return False
    
    # 检查是否存在虚拟环境
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print("   创建Python虚拟环境...")
        if not run_command(f"python -m venv {venv_dir}", "创建虚拟环境"):
            return False
    
    # 确定激活脚本路径
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate"
        pip_path = venv_dir / "Scripts" / "pip"
    else:
        activate_script = venv_dir / "bin" / "activate"
        pip_path = venv_dir / "bin" / "pip"
    
    # 安装依赖
    print("   安装Python依赖包...")
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        if not run_command(f"{pip_path} install -r requirements.txt", "安装依赖", cwd="backend"):
            return False
    else:
        print_warning("未找到requirements.txt，手动安装基本依赖...")
        basic_deps = [
            "flask==2.3.3",
            "flask-cors==4.0.0",
            "flask-jwt-extended==4.5.3",
            "flask-sqlalchemy==3.0.5",
            "requests==2.31.0",
            "werkzeug==2.3.7"
        ]
        for dep in basic_deps:
            if not run_command(f"{pip_path} install {dep}", f"安装 {dep}", cwd="backend"):
                return False
    
    print_success("后端依赖安装完成")
    return True

def install_frontend_dependencies():
    """安装前端依赖"""
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print_error("未找到frontend目录")
        return False
    
    print("   安装前端依赖包...")
    if not run_command("npm install", "安装npm依赖", cwd="frontend"):
        print_warning("npm安装失败，尝试使用cnpm...")
        if not run_command("cnpm install", "使用cnpm安装", cwd="frontend"):
            print_error("前端依赖安装失败")
            return False
    
    print_success("前端依赖安装完成")
    return True

def initialize_database():
    """初始化数据库"""
    backend_dir = Path("backend")
    init_script = backend_dir / "init_db.py"
    
    if not init_script.exists():
        print_error("未找到数据库初始化脚本")
        return False
    
    # 确定Python解释器路径
    if platform.system() == "Windows":
        python_path = backend_dir / "venv" / "Scripts" / "python"
    else:
        python_path = backend_dir / "venv" / "bin" / "python"
    
    print("   初始化数据库和示例数据...")
    if not run_command(f"{python_path} init_db.py", "初始化数据库", cwd="backend"):
        return False
    
    print_success("数据库初始化完成")
    return True

def create_run_scripts():
    """创建启动脚本"""
    
    # Windows启动脚本
    windows_script = """@echo off
echo 启动交互式Python学习平台...

echo.
echo 启动后端服务...
start "Backend" cmd /k "cd backend && venv\\Scripts\\activate && python app.py"

echo.
echo 等待后端启动...
timeout /t 3 /nobreak > nul

echo.
echo 启动前端服务...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo 平台启动完成！
echo 学生端: http://localhost:3000
echo 管理员: http://localhost:3000/admin
echo API文档: http://localhost:5000
echo.
echo 按任意键关闭此窗口...
pause > nul
"""

    # Linux/macOS启动脚本
    unix_script = """#!/bin/bash
echo "🚀 启动交互式Python学习平台..."

echo ""
echo "📡 启动后端服务..."
cd backend
source venv/bin/activate
python app.py &
BACKEND_PID=$!
cd ..

echo ""
echo "⏳ 等待后端启动..."
sleep 3

echo ""
echo "🌐 启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "✅ 平台启动完成！"
echo "🎓 学生端: http://localhost:3000"
echo "👨‍💼 管理员: http://localhost:3000/admin"
echo "📖 API文档: http://localhost:5000"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "echo ''; echo '🛑 停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
"""

    # 创建Windows脚本
    with open("start.bat", "w", encoding="utf-8") as f:
        f.write(windows_script)
    
    # 创建Unix脚本
    with open("start.sh", "w", encoding="utf-8") as f:
        f.write(unix_script)
    
    # 给Unix脚本执行权限
    if platform.system() != "Windows":
        os.chmod("start.sh", 0o755)
    
    print_success("启动脚本创建完成")
    return True

def cleanup_migration_files():
    """清理不必要的迁移文件"""
    migration_files = [
        "backend/migrate_database.py",
        "backend/migrate_feedback_tables.py"
    ]
    
    for file_path in migration_files:
        if os.path.exists(file_path):
            # 重命名而不是删除，以防需要参考
            backup_name = f"{file_path}.backup"
            shutil.move(file_path, backup_name)
            print(f"   迁移文件已备份: {backup_name}")
    
    print_success("迁移文件清理完成")

def show_final_instructions():
    """显示最终使用说明"""
    print_colored("\n🎉 安装完成！", Colors.BOLD + Colors.GREEN)
    
    print("\n📖 使用说明:")
    print("="*50)
    
    if platform.system() == "Windows":
        print("• 双击 start.bat 启动平台")
    else:
        print("• 运行 ./start.sh 启动平台")
    
    print("• 学生端: http://localhost:3000")
    print("• 管理员后台: http://localhost:3000/admin")
    print("  - 用户名: admin")
    print("  - 密码: admin123")
    print("• API文档: http://localhost:5000")
    
    print("\n🔧 手动启动:")
    print("="*50)
    print("后端:")
    if platform.system() == "Windows":
        print("  cd backend && venv\\Scripts\\activate && python app.py")
    else:
        print("  cd backend && source venv/bin/activate && python app.py")
    
    print("前端:")
    print("  cd frontend && npm run dev")
    
    print("\n📚 项目特色:")
    print("="*50)
    print("• 基于ITS智能辅导系统理论")
    print("• 个性化学习路径和进度追踪")
    print("• 浏览器内代码编辑和执行")
    print("• 智能反馈和分级提示系统")
    print("• 完整的学习分析功能")
    
    print("\n🆘 如果遇到问题:")
    print("="*50)
    print("• 检查Python版本 >= 3.8")
    print("• 检查Node.js是否正确安装")
    print("• 查看终端错误信息")
    print("• 手动运行安装步骤")

def main():
    """主安装流程"""
    print_colored("="*60, Colors.BLUE)
    print_colored("🐍 交互式Python学习平台 - 自动安装程序", Colors.BOLD)
    print_colored("="*60, Colors.BLUE)
    
    # 检查基本环境
    print_step(1, "检查系统环境")
    if not check_python_version():
        return False
    
    node_available = check_node_version()
    
    # 安装后端依赖
    print_step(2, "安装后端依赖")
    if not install_backend_dependencies():
        print_error("后端依赖安装失败")
        return False
    
    # 安装前端依赖（如果Node.js可用）
    if node_available:
        print_step(3, "安装前端依赖")
        if not install_frontend_dependencies():
            print_warning("前端依赖安装失败，但不影响后端功能")
    else:
        print_warning("跳过前端依赖安装，请手动安装Node.js后运行: npm install")
    
    # 初始化数据库
    print_step(4, "初始化数据库")
    if not initialize_database():
        print_error("数据库初始化失败")
        return False
    
    # 创建启动脚本
    print_step(5, "创建启动脚本")
    create_run_scripts()
    
    # 清理迁移文件
    print_step(6, "清理项目文件")
    cleanup_migration_files()
    
    # 显示最终说明
    show_final_instructions()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        print_colored("\n\n用户取消安装", Colors.YELLOW)
        sys.exit(1)
    except Exception as e:
        print_error(f"安装过程出现意外错误: {str(e)}")
        sys.exit(1)