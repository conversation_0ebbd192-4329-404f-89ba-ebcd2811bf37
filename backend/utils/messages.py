"""
双语消息管理器 - 用于统一管理API返回的错误消息和提示信息
"""

class MessageManager:
    """双语消息管理器"""
    
    def __init__(self, language: str = 'zh'):
        self.language = language
        self.messages = {
            'zh': {
                # 通用错误消息
                'missing_required_fields': '练习ID和代码都是必填项',
                'exercise_not_found': '练习不存在',
                'submission_failed': '代码提交失败',
                'submission_not_found': '提交记录不存在或无权限',
                'exercise_not_found_for_submission': '关联的练习不存在',
                'analysis_failed': '{model} 分析失败',
                'additional_feedback_failed': '请求额外反馈失败',
                'hint_not_available': '暂不提供提示，请先尝试解决问题',
                'no_hints_available': '暂无提示可用',
                'no_more_hints': '没有更多提示了',
                'get_hint_failed': '获取提示失败',
                'get_history_failed': '获取提交历史失败',
                'missing_rating_params': '缺少必要参数',
                'feedback_record_not_found': '反馈记录不存在',
                'rating_submit_failed': '评分提交失败: {error}',
                'rating_submit_success': '评分提交成功',
                
                # 反馈消息
                'excellent_first_try': '🎉 太棒了！你一次就答对了！',
                'good_job_few_tries': '✅ 很好！经过努力你找到了正确答案！',
                'persistent_success': '💪 坚持不懈！你最终成功了！',
                'code_elegant': '👍 代码简洁优雅！',
                'code_good_structure': '👌 代码结构良好。',
                'code_can_optimize': '💡 代码可以进一步优化。',
                'code_good_style': '✨ 代码风格很好！',
                'syntax_error_hint': '💡 语法错误：请检查代码的拼写和标点符号。注意Python对缩进很敏感！',
                'name_error_hint': '💡 名称错误：请检查变量名或函数名是否正确，是否已经定义。',
                'type_error_hint': '💡 类型错误：请检查数据类型是否匹配，比如数字和字符串的操作。',
                'logic_error_hint': "💡 输出不正确：期望输出 '{expected}'，但得到了 '{actual}'。检查逻辑是否正确。",
                'timeout_error_hint': '⏰ 代码运行超时：可能存在无限循环，请检查循环条件。',
                'general_error_hint': '💡 请仔细检查代码，也许可以尝试不同的方法。',
                'suggestions': '建议：'
            },
            'en': {
                # 通用错误消息
                'missing_required_fields': 'Exercise ID and code are required fields',
                'exercise_not_found': 'Exercise not found',
                'submission_failed': 'Code submission failed',
                'submission_not_found': 'Submission record not found or no permission',
                'exercise_not_found_for_submission': 'Associated exercise not found',
                'analysis_failed': '{model} analysis failed',
                'additional_feedback_failed': 'Failed to request additional feedback',
                'hint_not_available': 'No hints available yet, please try solving the problem first',
                'no_hints_available': 'No hints available',
                'no_more_hints': 'No more hints available',
                'get_hint_failed': 'Failed to get hint',
                'get_history_failed': 'Failed to get submission history',
                'missing_rating_params': 'Missing required parameters',
                'feedback_record_not_found': 'Feedback record not found',
                'rating_submit_failed': 'Failed to submit rating: {error}',
                'rating_submit_success': 'Rating submitted successfully',
                
                # 反馈消息
                'excellent_first_try': '🎉 Excellent! You got it right on the first try!',
                'good_job_few_tries': '✅ Good job! You found the correct answer with effort!',
                'persistent_success': '💪 Persistence pays off! You finally succeeded!',
                'code_elegant': '👍 Code is elegant and concise!',
                'code_good_structure': '👌 Code structure is good.',
                'code_can_optimize': '💡 Code can be further optimized.',
                'code_good_style': '✨ Great code style!',
                'syntax_error_hint': '💡 Syntax error: Please check spelling and punctuation. Note that Python is sensitive to indentation!',
                'name_error_hint': '💡 Name error: Please check if variable or function names are correct and defined.',
                'type_error_hint': '💡 Type error: Please check if data types match, such as operations between numbers and strings.',
                'logic_error_hint': "💡 Incorrect output: Expected '{expected}', but got '{actual}'. Check if the logic is correct.",
                'timeout_error_hint': '⏰ Code execution timeout: There might be an infinite loop, please check loop conditions.',
                'general_error_hint': '💡 Please carefully check your code, maybe try a different approach.',
                'suggestions': 'Suggestions:'
            }
        }
    
    def get_message(self, key: str, **kwargs) -> str:
        """获取本地化消息"""
        message_dict = self.messages.get(self.language, self.messages['zh'])
        message = message_dict.get(key, f"Message key '{key}' not found")
        return message.format(**kwargs) if kwargs else message
    
    def set_language(self, language: str):
        """设置语言"""
        if language in self.messages:
            self.language = language
