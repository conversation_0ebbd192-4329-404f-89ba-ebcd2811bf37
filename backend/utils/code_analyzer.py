"""
代码分析器 - 支持多个API进行代码分析
按老师建议，集成多个API进行代码分析对比
"""

import ast
import re
import json
import requests
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from config import get_config
import os

class CodeAnalyzer:
    """代码分析器 - 支持多个API和本地分析"""
    
    def __init__(self, language='en'):
        # 使用配置管理系统
        self.config = get_config()
        self.language = language  # 添加语言支持
        
        # OpenRouter API配置
        self.openrouter_api_key = self.config.OPENROUTER_API_KEY
        self.openrouter_base_url = self.config.OPENROUTER_BASE_URL
        
        # 模型配置
        self.models = {
            'openai': 'openai/gpt-4o-mini',
            'deepseek': 'deepseek/deepseek-chat-v3-0324'
        }
        
    def analyze_code(self, code: str, problem_statement: str = "", analysis_mode: str = 'initial') -> Dict[str, Any]:
        """
        综合分析代码，包括：
        1. 本地静态分析
        2. 多API分析结果对比
        
        :param analysis_mode: 'initial' (只运行OpenAI) 或 'full' (运行所有)
        """
        results = {}
        
        # 1. 本地静态分析（必须成功）
        try:
            results['local_analysis'] = self._local_static_analysis(code)
        except Exception as e:
            # 即使本地分析失败，也要提供基础数据
            results['local_analysis'] = {
                'error': str(e),
                'stats': {'lines': len(code.splitlines())},
                'complexity': 1,
                'quality_issues': ['分析失败']
            }
        
        # 2. 多API分析（失败不影响主流程）
        api_results = {}
        
        if self.openrouter_api_key and self.openrouter_api_key.startswith('sk-'):
            import concurrent.futures
            
            def call_openai():
                try:
                    result = self._analyze_with_openrouter_model('openai', code, problem_statement)
                    print("✅ OpenAI分析完成")
                    return ('openai', result)
                except Exception as e:
                    print(f"⚠️ OpenAI API调用失败: {str(e)}")
                    return ('openai', {'error': str(e), 'disabled': True})
            
            def call_deepseek():
                try:
                    result = self._analyze_with_openrouter_model('deepseek', code, problem_statement)
                    print("✅ DeepSeek分析完成")
                    return ('deepseek', result)
                except Exception as e:
                    print(f"⚠️ DeepSeek API调用失败: {str(e)}")
                    return ('deepseek', {'error': str(e), 'disabled': True})

            # 使用线程池并发调用API
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                    # 根据模式决定要提交的任务
                    futures = [executor.submit(call_openai)]
                    if analysis_mode == 'full':
                        futures.append(executor.submit(call_deepseek))
                    
                    # 等待任务完成
                    timeout = 15 if analysis_mode == 'full' else 10
                    for future in concurrent.futures.as_completed(futures, timeout=timeout):
                        try:
                            api_name, result = future.result(timeout=1)
                            api_results[api_name] = result
                            print(f"📊 {api_name}分析完成")
                        except Exception as e:
                            print(f"⚠️ 单个任务结果获取失败: {str(e)}")
                    
                    print(f"✅ AI分析完成，模式: {analysis_mode}，获得 {len(api_results)} 个结果")

            except concurrent.futures.TimeoutError:
                print(f"⚠️ AI分析总体超时 (模式: {analysis_mode})，收集已完成结果")
                for future in futures:
                    if future.done():
                        try:
                            api_name, result = future.result(timeout=0.1)
                            api_results[api_name] = result
                        except Exception:
                            pass
                    else:
                        future.cancel()
            except Exception as e:
                print(f"⚠️ AI分析并发执行失败: {str(e)}")
        else:
            print("💡 OpenRouter API密钥无效或未设置，跳过AI分析")
        
        # 3. 本地规则分析作为备选
        try:
            api_results['local_rules'] = self._analyze_with_local_rules(code, problem_statement)
        except Exception as e:
            api_results['local_rules'] = {'error': str(e)}
        
        results['api_analysis'] = api_results
        
        # 4. 生成综合评估
        try:
            results['summary'] = self._generate_summary(results)
        except Exception as e:
            results['summary'] = {
                'lines_of_code': len(code.splitlines()),
                'complexity_score': 5,
                'syntax_score': 5.0,
                'total_suggestions': 0,
                'api_count': 0,
                'analysis_timestamp': datetime.now().isoformat()
            }
        
        return results

    def analyze_single_model(self, model_key: str, code: str, problem_statement: str = "") -> Dict[str, Any]:
        """
        按需分析单个AI模型
        """
        if model_key not in self.models:
            return {'error': f'不支持的模型: {model_key}'}

        if not (self.openrouter_api_key and self.openrouter_api_key.startswith('sk-')):
            return {'error': 'AI分析功能未配置'}

        try:
            # 直接调用对应的模型分析函数
            result = self._analyze_with_openrouter_model(model_key, code, problem_statement)
            print(f"✅ 按需分析完成: {model_key}")
            return {model_key: result}
        except Exception as e:
            print(f"⚠️ 按需分析失败: {model_key}, Error: {str(e)}")
            return {model_key: {'error': str(e), 'disabled': True}}
    
    def _local_static_analysis(self, code: str) -> Dict[str, Any]:
        """本地静态代码分析"""
        try:
            # 解析AST
            tree = ast.parse(code)
            
            # 基本统计
            stats = {
                'lines': len(code.splitlines()),
                'non_empty_lines': len([line for line in code.splitlines() if line.strip()]),
                'functions': len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]),
                'classes': len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]),
                'imports': len([node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))]),
                'loops': len([node for node in ast.walk(tree) if isinstance(node, (ast.For, ast.While))]),
                'conditions': len([node for node in ast.walk(tree) if isinstance(node, ast.If)]),
                'variables': len(set(node.id for node in ast.walk(tree) if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store)))
            }
            
            # 复杂度分析
            complexity = self._calculate_complexity(tree)
            
            # 代码质量检查
            quality_issues = self._check_code_quality(code, tree)
            
            return {
                'stats': stats,
                'complexity': complexity,
                'quality_issues': quality_issues,
                'analysis_time': datetime.now().isoformat()
            }
            
        except SyntaxError as e:
            if self.language == 'zh':
                error_msg = f'语法错误: {str(e)}'
                quality_issues = ['语法错误']
            else:
                error_msg = f'Syntax error: {str(e)}'
                quality_issues = ['Syntax error']
                
            return {
                'error': error_msg,
                'stats': {'lines': len(code.splitlines())},
                'complexity': 0,
                'quality_issues': quality_issues
            }
        except Exception as e:
            if self.language == 'zh':
                error_msg = f'分析失败: {str(e)}'
                quality_issues = ['分析失败']
            else:
                error_msg = f'Analysis failed: {str(e)}'
                quality_issues = ['Analysis failed']
                
            return {
                'error': error_msg,
                'stats': {'lines': len(code.splitlines())},
                'complexity': 0,
                'quality_issues': quality_issues
            }
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """计算代码复杂度（简化版循环复杂度）"""
        complexity = 1  # 基础复杂度
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
        
        return complexity
    
    def _check_code_quality(self, code: str, tree: ast.AST) -> List[str]:
        """检查代码质量问题"""
        issues = []
        
        # 检查常见问题
        if len(code.splitlines()) > 50:
            if self.language == 'zh':
                issues.append("代码过长，建议拆分为多个函数")
            else:
                issues.append("Code is too long, consider splitting into multiple functions")
        
        # 检查变量命名
        for node in ast.walk(tree):
            if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                if len(node.id) == 1 and node.id not in ['i', 'j', 'k', 'x', 'y', 'z']:
                    if self.language == 'zh':
                        issues.append(f"变量名 '{node.id}' 过短，建议使用更有意义的名称")
                    else:
                        issues.append(f"Variable name '{node.id}' is too short, consider using a more meaningful name")
                elif node.id.isupper() and len(node.id) > 1:
                    if self.language == 'zh':
                        issues.append(f"变量名 '{node.id}' 建议使用小写")
                    else:
                        issues.append(f"Variable name '{node.id}' should use lowercase")
        
        # 检查缩进
        lines = code.splitlines()
        for i, line in enumerate(lines):
            if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                continue
            if '\t' in line and ' ' in line.lstrip():
                if self.language == 'zh':
                    issues.append(f"第{i+1}行：混合使用tab和空格")
                else:
                    issues.append(f"Line {i+1}: Mixed use of tabs and spaces")
        
        return issues
    
    def _analyze_with_openrouter_model(self, model_key: str, code: str, problem_statement: str, error_context: Optional[dict] = None) -> Dict[str, Any]:
        """使用OpenRouter API调用指定模型进行教育性代码分析"""
        
        model_name = self.models.get(model_key)
        if not model_name:
            raise Exception(f"未知的模型键: {model_key}")
        
        # 根据不同模型选择不同的提示风格
        if model_key == 'openai':
            return self._generate_openai_style_prompt(model_name, code, problem_statement, error_context)
        elif model_key == 'deepseek':
            return self._generate_deepseek_style_prompt(model_name, code, problem_statement, error_context)
        else:
            raise Exception(f"不支持的模型类型: {model_key}")
    
    def _call_openrouter_api(self, model_name: str, messages: list, max_tokens: int = 800, temperature: float = 0.7) -> Dict[str, Any]:
        """通用OpenRouter API调用方法"""
        
        headers = {
            'Authorization': f'Bearer {self.openrouter_api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/your-repo',  # OpenRouter要求的referer
            'X-Title': 'Python Learning Platform'  # 可选的标题
        }
        
        payload = {
            'model': model_name,
            'messages': messages,
            'max_tokens': max_tokens,
            'temperature': temperature,
            'stream': False
        }
        
        start_time = time.time()
        
        response = requests.post(
            f'{self.openrouter_base_url}/chat/completions',
            headers=headers,
            data=json.dumps(payload),
            timeout=self.config.OPENROUTER_TIMEOUT
        )
        
        generation_time = (time.time() - start_time) * 1000  # 毫秒
        
        if response.status_code != 200:
            raise Exception(f"OpenRouter API错误: {response.status_code} - {response.text}")
        
        result = response.json()
        
        # 估算成本（OpenRouter通常在响应中包含使用信息）
        usage = result.get('usage', {})
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        
        # 根据模型估算成本（这些是大概的价格，实际以OpenRouter为准）
        if 'gpt-4o-mini' in model_name:
            estimated_cost = (input_tokens * 0.00015 + output_tokens * 0.0006) / 1000
        elif 'deepseek' in model_name:
            estimated_cost = (input_tokens * 0.00014 + output_tokens * 0.00028) / 1000  # DeepSeek更便宜
        else:
            estimated_cost = 0.0
        
        return {
            'response': result,
            'generation_time_ms': generation_time,
            'estimated_cost': estimated_cost,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens
        }
    
    def _generate_openai_style_prompt(self, model_name: str, code: str, problem_statement: str, error_context: Optional[dict] = None) -> Dict[str, Any]:
        """生成OpenAI风格的教育性提示"""
        
        if self.language == 'zh':
            # 中文提示
            base_prompt = f"""你是一位耐心、专业的Python编程导师，正在指导编程初学者。你的目标是帮助学生理解和改进他们的代码，而不是直接给出答案。

题目要求：{problem_statement}

学生提交的代码：
```python
{code}
```
"""
            
            if error_context:
                base_prompt += f"\n错误信息：{error_context.get('error_message', '无特定错误')}\n"
            
            educational_prompt = base_prompt + """
请作为编程导师，提供以下类型的反馈：

1. 鼓励性开场：首先承认学生的努力
2. 问题识别：用初学者能理解的语言解释代码中的问题
3. 概念解释：如果涉及概念误解，简单解释相关Python概念
4. 启发性提示：不直接给答案，而是引导学生思考正确方向
5. 学习建议：提供下一步学习的具体建议

请用温和、鼓励的语气，避免使用过于技术性的术语。以JSON格式返回，包含以下字段：
{
    "encouragement": "鼓励性开场语",
    "problem_identification": "问题识别和解释",
    "concept_explanation": "相关概念解释（如需要）",
    "guiding_hints": "启发性提示（不直接给答案）",
    "learning_suggestions": "学习建议",
    "estimated_difficulty": "错误难度级别（1-5）",
    "common_mistake": "是否为常见错误（true/false）",
    "educational_value": "这个错误的教育价值（1-5）"
}
"""
            
            system_message = '你是一位专业的Python编程教育专家，擅长用简单易懂的方式解释编程概念，帮助初学者克服学习困难。'
            fallback_feedback = {
                'encouragement': '很棒的尝试！编程是一个学习过程。',
                'problem_identification': 'API分析暂时不可用，但不要气馁。',
                'guiding_hints': '尝试运行代码，观察输出结果，思考如何改进。',
                'learning_suggestions': '继续练习，每次错误都是学习的机会！'
            }
        else:
            # 英文提示
            base_prompt = f"""You are a patient, professional Python programming tutor guiding programming beginners. Your goal is to help students understand and improve their code, not to give direct answers.

Problem requirements: {problem_statement}

Student's submitted code:
```python
{code}
```
"""
            
            if error_context:
                base_prompt += f"\nError information: {error_context.get('error_message', 'No specific error')}\n"
            
            educational_prompt = base_prompt + """
As a programming tutor, please provide the following types of feedback:

1. Encouraging opening: First acknowledge the student's effort
2. Problem identification: Explain code issues in language beginners can understand
3. Concept explanation: If there are conceptual misunderstandings, briefly explain relevant Python concepts
4. Guiding hints: Don't give direct answers, but guide students to think in the right direction
5. Learning suggestions: Provide specific suggestions for next steps in learning

Please use a gentle, encouraging tone and avoid overly technical terminology. Return in JSON format with the following fields:
{
    "encouragement": "Encouraging opening statement",
    "problem_identification": "Problem identification and explanation",
    "concept_explanation": "Concept explanation (if needed)",
    "guiding_hints": "Guiding hints (not direct answers)",
    "learning_suggestions": "Learning suggestions",
    "estimated_difficulty": "Error difficulty level (1-5)",
    "common_mistake": "Whether this is a common mistake (true/false)",
    "educational_value": "Educational value of this error (1-5)"
}
"""
            
            system_message = 'You are a professional Python programming education expert, skilled at explaining programming concepts in simple and understandable ways, helping beginners overcome learning difficulties.'
            fallback_feedback = {
                'encouragement': 'Great attempt! Programming is a learning process.',
                'problem_identification': 'API analysis is temporarily unavailable, but don\'t be discouraged.',
                'guiding_hints': 'Try running the code, observe the output, and think about how to improve it.',
                'learning_suggestions': 'Keep practicing, every mistake is a learning opportunity!'
            }
        
        try:
            messages = [
                {
                    'role': 'system', 
                    'content': system_message
                },
                {
                    'role': 'user', 
                    'content': educational_prompt
                }
            ]
            
            api_result = self._call_openrouter_api(model_name, messages, max_tokens=800, temperature=0.7)
            
            analysis_text = api_result['response']['choices'][0]['message']['content']
            
            # 尝试解析JSON (处理markdown代码块)
            try:
                # 清理markdown代码块标记
                json_content = analysis_text.strip()
                if json_content.startswith('```json'):
                    json_content = json_content[7:]
                if json_content.startswith('```'):
                    json_content = json_content[3:]
                if json_content.endswith('```'):
                    json_content = json_content[:-3]
                json_content = json_content.strip()
                
                parsed_feedback = json.loads(json_content)
                return {
                    'type': 'educational',
                    'source': 'openai_gpt4_educational',
                    'feedback': parsed_feedback,
                    'generation_time_ms': api_result['generation_time_ms'],
                    'estimated_cost': api_result['estimated_cost'],
                    'tokens_used': api_result['input_tokens'] + api_result['output_tokens'],
                    'timestamp': datetime.now().isoformat()
                }
            except json.JSONDecodeError:
                # 如果JSON解析失败，提取关键信息
                return {
                    'type': 'educational',
                    'source': 'openai_gpt4_educational',
                    'feedback': {
                        'raw_analysis': analysis_text,
                        **fallback_feedback
                    },
                    'generation_time_ms': api_result['generation_time_ms'],
                    'estimated_cost': api_result['estimated_cost'],
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'type': 'educational',
                'source': 'openai_educational_error',
                'error': str(e),
                'fallback_feedback': fallback_feedback
            }
    
    def _generate_deepseek_style_prompt(self, model_name: str, code: str, problem_statement: str, error_context: Optional[dict] = None) -> Dict[str, Any]:
        """生成DeepSeek风格的教育性提示"""
        
        if self.language == 'zh':
            # 中文提示
            base_prompt = f"""我是一名Python编程初学者，正在学习编程基础。请你作为我的编程导师，帮助我理解和改进代码。

题目要求：{problem_statement}

我写的代码：
```python
{code}
```
"""
            
            if error_context:
                base_prompt += f"\n我遇到的错误：{error_context.get('error_message', '代码无法正常运行')}\n"
            
            educational_prompt = base_prompt + """
请你作为耐心的编程导师，用初学者能理解的语言给我反馈。我希望：

1. 先鼓励我的尝试，然后指出需要改进的地方
2. 用简单的语言解释问题所在，避免复杂的技术术语
3. 不要直接告诉我正确答案，而是给我一些提示让我自己思考
4. 如果我理解错了某个概念，请简单解释一下这个概念
5. 给我一些学习建议，帮助我更好地掌握Python

请用JSON格式回复，包含以下信息：
{
    "encouragement": "鼓励的话",
    "problem_explanation": "问题解释",
    "concept_clarification": "概念澄清（如果需要）",
    "hints_not_answers": "提示而非答案",
    "next_steps": "下一步学习建议",
    "mistake_level": "错误严重程度（1-5分）",
    "learning_opportunity": "这个错误能学到什么"
}
"""
            
            system_message = '你是一位专业、耐心的Python编程教育专家。你善于用简单易懂的语言解释复杂概念，总是鼓励学生，从不直接给出答案，而是引导学生思考。'
            fallback_feedback = {
                'encouragement': '编程需要练习，你已经在正确的道路上了！',
                'problem_explanation': 'API分析暂时无法使用，但这不影响你的学习进度。',
                'hints_not_answers': '仔细读题，理解要求，然后一步一步编写代码。',
                'next_steps': '继续练习，遇到问题时多思考解决方案。'
            }
        else:
            # 英文提示
            base_prompt = f"""I am a Python programming beginner learning programming basics. Please act as my programming tutor and help me understand and improve my code.

Problem requirements: {problem_statement}

My code:
```python
{code}
```
"""
            
            if error_context:
                base_prompt += f"\nThe error I encountered: {error_context.get('error_message', 'Code cannot run properly')}\n"
            
            educational_prompt = base_prompt + """
Please act as a patient programming tutor and give me feedback in language that beginners can understand. I hope you can:

1. First encourage my attempt, then point out areas that need improvement
2. Explain problems in simple language, avoiding complex technical terms
3. Don't tell me the correct answer directly, but give me some hints to think for myself
4. If I misunderstood a concept, please briefly explain that concept
5. Give me some learning suggestions to help me better master Python

Please reply in JSON format with the following information:
{
    "encouragement": "Encouraging words",
    "problem_explanation": "Problem explanation",
    "concept_clarification": "Concept clarification (if needed)",
    "hints_not_answers": "Hints rather than answers",
    "next_steps": "Next learning steps",
    "mistake_level": "Error severity level (1-5 points)",
    "learning_opportunity": "What can be learned from this error"
}
"""
            
            system_message = 'You are a professional, patient Python programming education expert. You are good at explaining complex concepts in simple and understandable language, always encouraging students, never giving direct answers, but guiding students to think.'
            fallback_feedback = {
                'encouragement': 'Programming requires practice, you are already on the right track!',
                'problem_explanation': 'API analysis is temporarily unavailable, but this does not affect your learning progress.',
                'hints_not_answers': 'Read the problem carefully, understand the requirements, then write code step by step.',
                'next_steps': 'Keep practicing and think more about solutions when you encounter problems.'
            }
        
        try:
            messages = [
                {
                    'role': 'system',
                    'content': system_message
                },
                {
                    'role': 'user', 
                    'content': educational_prompt
                }
            ]
            
            api_result = self._call_openrouter_api(model_name, messages, max_tokens=800, temperature=0.7)
            
            analysis_text = api_result['response']['choices'][0]['message']['content']
            
            # 尝试解析JSON (处理markdown代码块)
            try:
                # 清理markdown代码块标记
                json_content = analysis_text.strip()
                if json_content.startswith('```json'):
                    json_content = json_content[7:]
                if json_content.startswith('```'):
                    json_content = json_content[3:]
                if json_content.endswith('```'):
                    json_content = json_content[:-3]
                json_content = json_content.strip()
                
                parsed_feedback = json.loads(json_content)
                return {
                    'type': 'educational',
                    'source': 'deepseek_educational',
                    'feedback': parsed_feedback,
                    'generation_time_ms': api_result['generation_time_ms'],
                    'estimated_cost': api_result['estimated_cost'],
                    'input_tokens': api_result['input_tokens'],
                    'output_tokens': api_result['output_tokens'],
                    'timestamp': datetime.now().isoformat()
                }
            except json.JSONDecodeError:
                # JSON解析失败的备选方案
                return {
                    'type': 'educational',
                    'source': 'deepseek_educational',
                    'feedback': {
                        'raw_analysis': analysis_text,
                        **fallback_feedback
                    },
                    'generation_time_ms': api_result['generation_time_ms'],
                    'estimated_cost': api_result['estimated_cost'],
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                'type': 'educational',
                'source': 'deepseek_educational_error',
                'error': str(e),
                'fallback_feedback': fallback_feedback
            }

    def _analyze_with_openai(self, code: str, problem_statement: str) -> Dict[str, Any]:
        """使用OpenAI模型进行教育性代码分析"""
        return self._analyze_with_openrouter_model('openai', code, problem_statement)
    
    def _analyze_with_anthropic(self, code: str, problem_statement: str) -> Dict[str, Any]:
        """使用DeepSeek模型进行教育性代码分析"""
        return self._analyze_with_openrouter_model('deepseek', code, problem_statement)
    
    def _analyze_with_local_rules(self, code: str, problem_statement: str) -> Dict[str, Any]:
        """使用本地规则分析代码"""
        analysis = {
            'correctness': 'unknown',
            'efficiency': 'good',
            'style': 'good',
            'suggestions': [],
            'source': 'local_rules'
        }
        
        # 🆕 增强：检查无意义或问题代码
        code_lines = [line.strip() for line in code.splitlines() if line.strip() and not line.strip().startswith('#')]
        
        # 🔧 增强：检测只有变量名的无意义代码
        if len(code_lines) == 1 and re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', code_lines[0]):
            if self.language == 'zh':
                analysis['suggestions'].append(f'代码"{code_lines[0]}"只是一个变量引用，可能会导致NameError。考虑赋值、调用函数或添加具体逻辑')
                analysis['correctness'] = 'likely_error'
                analysis['style'] = 'poor'  # 标记为糟糕的代码风格
            else:
                analysis['suggestions'].append(f'Code "{code_lines[0]}" is just a variable reference, may cause NameError. Consider assignment, function call, or adding specific logic')
                analysis['correctness'] = 'likely_error'
                analysis['style'] = 'poor'
        
        # 🆕 新增：检测可能导致错误的常见模式
        error_prone_patterns = {
            r'^[a-zA-Z_][a-zA-Z0-9_]*\s*$': 'undefined_variable',  # 未定义变量
            r'^\d+\s*$': 'meaningless_number',  # 无意义数字
            r'^["\'].*["\']\s*$': 'meaningless_string',  # 无意义字符串
            r'^\s*pass\s*$': 'placeholder_code',  # 占位符代码
        }
        
        for line in code_lines:
            for pattern, error_type in error_prone_patterns.items():
                if re.match(pattern, line):
                    if error_type == 'undefined_variable' and len(code_lines) == 1:
                        continue  # 已经在上面处理过
                    elif error_type == 'meaningless_number':
                        if self.language == 'zh':
                            analysis['suggestions'].append(f'数字"{line}"单独存在没有意义，考虑将其用于计算或赋值')
                        else:
                            analysis['suggestions'].append(f'Number "{line}" alone is meaningless, consider using it for calculation or assignment')
                        if analysis['correctness'] != 'likely_error':
                            analysis['correctness'] = 'questionable'
                    elif error_type == 'meaningless_string':
                        if self.language == 'zh':
                            analysis['suggestions'].append(f'字符串"{line}"单独存在没有意义，考虑将其赋值给变量或用于输出')
                        else:
                            analysis['suggestions'].append(f'String "{line}" alone is meaningless, consider assigning it to a variable or using it for output')
                        if analysis['correctness'] != 'likely_error':
                            analysis['correctness'] = 'questionable'
                    break
        
        # 检测空代码或仅注释
        if len(code_lines) == 0:
            if self.language == 'zh':
                analysis['suggestions'].append('代码为空或只包含注释，请添加具体的Python代码来解决问题')
            else:
                analysis['suggestions'].append('Code is empty or contains only comments, please add specific Python code to solve the problem')
        
        # 检测常见的无意义表达式
        meaningless_patterns = [r'^\d+$', r'^".*"$', r"^'.*'$"]  # 纯数字、纯字符串
        for line in code_lines:
            for pattern in meaningless_patterns:
                if re.match(pattern, line):
                    if self.language == 'zh':
                        analysis['suggestions'].append(f'表达式"{line}"没有实际操作，考虑将其赋值给变量或用于计算')
                    else:
                        analysis['suggestions'].append(f'Expression "{line}" has no actual operation, consider assigning it to a variable or using it in calculations')
                    break
        
        # 原有规则检查
        if 'for' in code and 'range' in code:
            if self.language == 'zh':
                analysis['suggestions'].append('使用for循环遍历，效率较好')
            else:
                analysis['suggestions'].append('Using for loop with range, good efficiency')
        
        if 'print' in code:
            if self.language == 'zh':
                analysis['suggestions'].append('包含输出语句，符合题目要求')
            else:
                analysis['suggestions'].append('Contains print statement, meets problem requirements')
        
        if len(code.splitlines()) > 20:
            analysis['efficiency'] = 'moderate'
            if self.language == 'zh':
                analysis['suggestions'].append('代码较长，可以考虑优化')
            else:
                analysis['suggestions'].append('Code is lengthy, consider optimization')
        
        # 🆕 增强：检查是否包含基本的Python操作
        if not any(keyword in code.lower() for keyword in ['print', '=', 'def', 'if', 'for', 'while', 'import', 'return']):
            if len(code_lines) > 0:  # 确保不是空代码
                if self.language == 'zh':
                    analysis['suggestions'].append('代码缺少基本的Python操作（如赋值、函数调用、控制流等），请添加具体的实现逻辑')
                else:
                    analysis['suggestions'].append('Code lacks basic Python operations (like assignment, function calls, control flow, etc.), please add specific implementation logic')
        
        if re.search(r'[a-zA-Z_][a-zA-Z0-9_]*', code):
            analysis['style'] = 'good'
        
        return analysis
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合分析摘要"""
        local_stats = results.get('local_analysis', {}).get('stats', {})
        
        # 🔧 修复：增强评分算法，考虑代码可执行性和实用性
        local_analysis = results.get('local_analysis', {})
        api_analysis = results.get('api_analysis', {})
        
        # 基础复杂度评分
        complexity = local_analysis.get('complexity', 5)
        complexity_score = min(10, max(1, 11 - complexity))
        
        # 质量问题评分
        quality_issues = local_analysis.get('quality_issues', [])
        quality_score = max(1, 10 - len(quality_issues))
        
        # 🆕 新增：可执行性评分
        executability_score = 10  # 默认满分
        
        # 检查本地规则分析的正确性评估
        local_rules = api_analysis.get('local_rules', {})
        correctness = local_rules.get('correctness', 'unknown')
        
        if correctness == 'likely_error':
            executability_score = 3  # 可能有错误的代码降到3分
        elif correctness == 'questionable':
            executability_score = 6  # 有疑问的代码降到6分
        
        # 检查是否是无意义代码
        local_suggestions = local_rules.get('suggestions', [])
        meaningless_indicators = ['只是一个变量引用', 'just a variable reference', '没有实际操作', 'has no actual operation', '缺少基本的Python操作', 'lacks basic Python operations']
        
        has_meaningless_code = any(
            any(indicator in suggestion for indicator in meaningless_indicators)
            for suggestion in local_suggestions
        )
        
        if has_meaningless_code:
            executability_score = min(executability_score, 4)  # 无意义代码最高4分
        
        # 检查是否是空代码
        empty_code_indicators = ['代码为空', 'Code is empty', '只包含注释', 'contains only comments']
        has_empty_code = any(
            any(indicator in suggestion for indicator in empty_code_indicators)
            for suggestion in local_suggestions
        )
        
        if has_empty_code:
            executability_score = 1  # 空代码给1分
        
        # 🆕 综合评分：权重分配
        # 复杂度评分权重: 20%
        # 质量问题权重: 30% 
        # 可执行性权重: 50%
        syntax_score = (
            complexity_score * 0.2 + 
            quality_score * 0.3 + 
            executability_score * 0.5
        )
        
        # 🔧 修复：增强建议收集逻辑
        all_suggestions = []
        api_results = results.get('api_analysis', {})
        
        for api_name, api_result in api_results.items():
            if isinstance(api_result, dict):
                # 原有逻辑：收集direct suggestions
                if 'suggestions' in api_result:
                    all_suggestions.extend(api_result['suggestions'])
                
                # 🆕 新增：从AI反馈中提取建议
                if 'feedback' in api_result:
                    feedback = api_result['feedback']
                    if isinstance(feedback, dict):
                        # OpenAI风格的反馈提取
                        if 'learning_suggestions' in feedback:
                            all_suggestions.append(feedback['learning_suggestions'])
                        if 'guiding_hints' in feedback:
                            all_suggestions.append(feedback['guiding_hints'])
                        if 'problem_identification' in feedback:
                            all_suggestions.append(feedback['problem_identification'])
                        
                        # DeepSeek风格的反馈提取
                        if 'hints_not_answers' in feedback:
                            all_suggestions.append(feedback['hints_not_answers'])
                        if 'next_steps' in feedback:
                            all_suggestions.append(feedback['next_steps'])
                        if 'problem_explanation' in feedback:
                            all_suggestions.append(feedback['problem_explanation'])
                        
                        # 处理原始分析文本
                        if 'raw_analysis' in feedback and len(all_suggestions) == 0:
                            # 如果没有结构化建议，使用原始分析的一部分
                            raw_text = feedback['raw_analysis'][:200] + "..." if len(feedback['raw_analysis']) > 200 else feedback['raw_analysis']
                            all_suggestions.append(raw_text)
        
        # 添加本地分析的质量问题作为建议
        local_issues = results.get('local_analysis', {}).get('quality_issues', [])
        all_suggestions.extend(local_issues)
        
        # 🆕 新增：如果仍然没有建议，添加默认建议
        if len(all_suggestions) == 0:
            if self.language == 'zh':
                all_suggestions.append("代码已提交分析，请查看具体的反馈信息获取改进建议")
            else:
                all_suggestions.append("Code submitted for analysis, please check specific feedback for improvement suggestions")
        
        return {
            'lines_of_code': local_stats.get('lines', 0),
            'complexity_score': complexity_score,
            'syntax_score': syntax_score,
            'total_suggestions': len(all_suggestions),
            'suggestions_list': all_suggestions,  # 🆕 新增：返回具体的建议列表
            'api_count': len([k for k, v in api_results.items() if not isinstance(v, dict) or 'error' not in v]),
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def generate_structural_feedback(self, execution_result: Dict[str, Any], user_progress = None) -> Dict[str, Any]:
        """生成传统的结构化反馈（基于Judge0结果）"""
        feedback = {
            'type': 'structural',
            'source': 'judge0_traditional'
        }
        
        if execution_result.get('is_correct', False):
            # 成功情况的反馈
            if self.language == 'zh':
                base_message = "✅ 代码运行成功！"
                if user_progress and user_progress.attempts == 1:
                    feedback['message'] = f"{base_message} 太棒了，一次就成功了！"
                elif user_progress and user_progress.attempts <= 3:
                    feedback['message'] = f"{base_message} 经过努力终于成功了！"
                else:
                    feedback['message'] = f"{base_message} 坚持不懈，最终成功！"
            else:
                base_message = "✅ Code executed successfully!"
                if user_progress and user_progress.attempts == 1:
                    feedback['message'] = f"{base_message} Excellent, you got it right on the first try!"
                elif user_progress and user_progress.attempts <= 3:
                    feedback['message'] = f"{base_message} Great work, you succeeded after some effort!"
                else:
                    feedback['message'] = f"{base_message} Persistence pays off, you finally succeeded!"
            
            feedback['tone'] = 'excellent' if user_progress and user_progress.attempts == 1 else 'good'
        else:
            # 错误情况的分类反馈
            error_type = execution_result.get('error_type', '')
            error_message = execution_result.get('error_message', '')
            
            if self.language == 'zh':
                if error_type == 'syntax_error':
                    feedback['message'] = "❌ 语法错误：请检查代码的拼写和标点符号"
                    feedback['details'] = "Python对语法要求很严格，检查括号、冒号、缩进是否正确"
                elif error_type == 'name_error':
                    feedback['message'] = "❌ 名称错误：变量或函数名不存在"
                    feedback['details'] = "检查变量名是否已定义，拼写是否正确"
                elif error_type == 'type_error':
                    feedback['message'] = "❌ 类型错误：数据类型不匹配"
                    feedback['details'] = "检查是否在错误的数据类型上使用了操作"
                elif error_type == 'logic_error':
                    expected = execution_result.get('expected_output', '')
                    actual = execution_result.get('output', '')
                    feedback['message'] = "❌ 逻辑错误：输出不符合预期"
                    feedback['details'] = f"期望输出：{expected}，实际输出：{actual}"
                else:
                    feedback['message'] = "❌ 运行错误：代码执行过程中出现问题"
                    feedback['details'] = error_message if error_message else "请检查代码逻辑"
            else:
                if error_type == 'syntax_error':
                    feedback['message'] = "❌ Syntax Error: Please check code spelling and punctuation"
                    feedback['details'] = "Python is strict about syntax. Check brackets, colons, and indentation"
                elif error_type == 'name_error':
                    feedback['message'] = "❌ Name Error: Variable or function name does not exist"
                    feedback['details'] = "Check if variable names are defined and spelled correctly"
                elif error_type == 'type_error':
                    feedback['message'] = "❌ Type Error: Data type mismatch"
                    feedback['details'] = "Check if operations are used on incorrect data types"
                elif error_type == 'logic_error':
                    expected = execution_result.get('expected_output', '')
                    actual = execution_result.get('output', '')
                    feedback['message'] = "❌ Logic Error: Output does not match expected result"
                    feedback['details'] = f"Expected output: {expected}, Actual output: {actual}"
                else:
                    feedback['message'] = "❌ Runtime Error: Problem occurred during code execution"
                    feedback['details'] = error_message if error_message else "Please check code logic"
            
            feedback['tone'] = 'instructional'
            feedback['error_type'] = error_type
            feedback['technical_details'] = error_message
            
        return feedback
    
    def generate_comprehensive_feedback(self, execution_result: Dict[str, Any], 
                                      analysis_result: Dict[str, Any], 
                                      user_progress = None) -> Dict[str, Any]:
        """生成包含多种分析方法的综合反馈"""
        
        # 1. 生成结构化反馈
        structural_feedback = self.generate_structural_feedback(execution_result, user_progress)
        
        # 2. 提取AI反馈
        ai_feedbacks = {}
        api_analysis = analysis_result.get('api_analysis', {})
        
        for api_name, api_result in api_analysis.items():
            if isinstance(api_result, dict) and 'feedback' in api_result:
                ai_feedbacks[api_name] = {
                    'feedback': api_result['feedback'],
                    'generation_time': api_result.get('generation_time_ms', 0),
                    'cost': api_result.get('estimated_cost', 0),
                    'source': api_result.get('source', api_name)
                }
        
        # 3. 生成对比分析
        comparison_insights = self._generate_feedback_comparison(structural_feedback, ai_feedbacks)
        
        return {
            'comprehensive_feedback': {
                'structural': structural_feedback,
                'ai_generated': ai_feedbacks,
                'comparison': comparison_insights,
                'recommended_primary': self._recommend_primary_feedback(execution_result, ai_feedbacks),
                'learning_stage_adapted': self._adapt_for_learning_stage(user_progress, structural_feedback, ai_feedbacks)
            },
            'metadata': {
                'total_feedback_sources': 1 + len(ai_feedbacks),
                'analysis_timestamp': datetime.now().isoformat(),
                'user_progress_context': {
                    'attempts': user_progress.attempts if user_progress else 0,
                    'avg_time': user_progress.avg_time_sec if user_progress else 0,
                    'should_get_hints': user_progress.should_show_hint() if user_progress else False
                }
            }
        }
    
    def _generate_feedback_comparison(self, structural: Dict, ai_feedbacks: Dict) -> Dict[str, Any]:
        """生成不同反馈方法的对比分析"""
        
        if self.language == 'zh':
            comparison = {
                'feedback_diversity': len(ai_feedbacks) + 1,
                'structural_advantages': [
                    '快速准确的错误分类',
                    '标准化的技术术语',
                    '可靠的一致性'
                ],
                'ai_advantages': [
                    '人性化的解释',
                    '教育导向的引导',
                    '个性化的鼓励'
                ]
            }
        else:
            comparison = {
                'feedback_diversity': len(ai_feedbacks) + 1,
                'structural_advantages': [
                    'Fast and accurate error classification',
                    'Standardized technical terminology',
                    'Reliable consistency'
                ],
                'ai_advantages': [
                    'Human-friendly explanations',
                    'Educational guidance',
                    'Personalized encouragement'
                ]
            }
        
        # 分析AI反馈的一致性
        if len(ai_feedbacks) >= 2:
            encouragement_consistency = self._check_encouragement_consistency(ai_feedbacks)
            comparison['ai_consistency'] = encouragement_consistency
        
        return comparison
    
    def _check_encouragement_consistency(self, ai_feedbacks: Dict) -> Dict[str, Any]:
        """检查AI反馈的鼓励性一致性"""
        encouragement_scores = []
        
        for api_name, feedback_data in ai_feedbacks.items():
            feedback = feedback_data.get('feedback', {})
            encouragement = feedback.get('encouragement', '') or feedback.get('raw_analysis', '')
            
            # 简单的情感分析 - 计算积极词汇比例
            positive_words = ['很好', '不错', '棒', '努力', '继续', '加油', '鼓励', '尝试']
            word_count = len(encouragement.split())
            positive_count = sum(1 for word in positive_words if word in encouragement)
            
            if word_count > 0:
                encouragement_scores.append(positive_count / word_count)
        
        avg_encouragement = sum(encouragement_scores) / len(encouragement_scores) if encouragement_scores else 0
        
        return {
            'average_encouragement_score': avg_encouragement,
            'consistency_level': 'high' if abs(max(encouragement_scores) - min(encouragement_scores)) < 0.1 else 'moderate',
            'total_sources': len(encouragement_scores)
        }
    
    def _recommend_primary_feedback(self, execution_result: Dict, ai_feedbacks: Dict) -> str:
        """推荐主要显示的反馈类型"""
        
        # 如果代码正确，优先显示AI的鼓励性反馈
        if execution_result.get('is_correct', False):
            if 'openai' in ai_feedbacks:
                return 'openai_educational'
            elif 'deepseek' in ai_feedbacks:
                return 'deepseek_educational'
            else:
                return 'structural'
        
        # 如果代码有错误，根据错误类型决定
        error_type = execution_result.get('error_type', '')
        
        if error_type in ['syntax_error', 'name_error']:
            # 语法错误优先显示结构化反馈（更准确）
            return 'structural'
        else:
            # 逻辑错误优先显示AI反馈（更有启发性）
            if 'openai' in ai_feedbacks:
                return 'openai_educational'
            elif 'deepseek' in ai_feedbacks:
                return 'deepseek_educational'
            else:
                return 'structural'
    
    def _adapt_for_learning_stage(self, user_progress, structural_feedback: Dict, ai_feedbacks: Dict) -> Dict[str, Any]:
        """根据学习阶段调整反馈"""
        
        if not user_progress:
            return {'stage': 'unknown', 'recommendation': 'balanced'}
        
        attempts = user_progress.attempts
        avg_time = user_progress.avg_time_sec or 0
        
        if attempts == 1 and avg_time < 60:
            # 快速解决者 - 可以看更技术性的反馈
            if self.language == 'zh':
                return {
                    'stage': 'advanced_beginner',
                    'recommendation': 'show_structural_first',
                    'rationale': '学习者能快速理解问题，可以接受更技术性的反馈'
                }
            else:
                return {
                    'stage': 'advanced_beginner',
                    'recommendation': 'show_structural_first',
                    'rationale': 'Learner can quickly understand problems and can accept more technical feedback'
                }
        elif attempts <= 3 and avg_time < 300:
            # 正常学习者 - 平衡显示
            if self.language == 'zh':
                return {
                    'stage': 'typical_learner', 
                    'recommendation': 'balanced_display',
                    'rationale': '学习者需要技术准确性和教育引导的平衡'
                }
            else:
                return {
                    'stage': 'typical_learner', 
                    'recommendation': 'balanced_display',
                    'rationale': 'Learner needs a balance of technical accuracy and educational guidance'
                }
        else:
            # 需要更多帮助 - 优先显示AI的鼓励性反馈
            if self.language == 'zh':
                return {
                    'stage': 'needs_support',
                    'recommendation': 'show_ai_first',
                    'rationale': '学习者可能感到困惑，需要更多鼓励和引导'
                }
            else:
                return {
                    'stage': 'needs_support',
                    'recommendation': 'show_ai_first',
                    'rationale': 'Learner may feel confused and needs more encouragement and guidance'
                }