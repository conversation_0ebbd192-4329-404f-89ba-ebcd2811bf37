#!/usr/bin/env python3
"""
English Database Setup Script
Creates an English version of the Python Learning Platform database
Perfect for UK MSc Computer Science project demonstrations
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db
from models.content_models import Module, Lesson, Exercise, TestCase
from models.learning_models import User, UserRole, ProgressStatus, Submission, UserProgress, ErrorType, FeedbackEffectiveness, HintUsage

# English database path
ENGLISH_DB_PATH = os.path.join(os.path.dirname(__file__), 'instance', 'learning_platform_english.db')

def backup_existing_database():
    """Backup existing English database if it exists"""
    if os.path.exists(ENGLISH_DB_PATH):
        backup_path = f"{ENGLISH_DB_PATH}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(ENGLISH_DB_PATH, backup_path)
        print(f"✅ Existing English database backed up to: {backup_path}")
        return backup_path
    return None

def create_fresh_english_database():
    """Create a fresh English database"""
    # 需要在创建app之前设置环境变量，或者直接创建app并配置
    from flask import Flask
    from flask_cors import CORS
    from config import get_config
    
    # 获取配置
    config = get_config()
    
    # 创建Flask应用
    app = Flask(__name__)
    
    # 配置应用 - 使用英文数据库路径
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{ENGLISH_DB_PATH}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = config.JWT_SECRET_KEY
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = config.JWT_ACCESS_TOKEN_EXPIRES
    
    # 初始化扩展
    from extensions import db, jwt
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins=config.CORS_ORIGINS or ['*'])
    
    with app.app_context():
        # Drop all tables
        db.drop_all()
        print("📝 Cleared old database tables...")
        
        # Create all tables
        db.create_all()
        print("📊 Created new database tables...")
        
        # Initialize English content
        init_english_content()
        
        # Create default admin
        create_default_admin()
        
        # Create test data
        create_test_data()
        
        # Commit all changes
        db.session.commit()
        print("💾 English database setup completed!")

def init_english_content():
    """Initialize English educational content"""
    print("📚 Creating English course content...")
    
    # Module 1: Programming Fundamentals
    module1 = Module(
        title="Programming Fundamentals",
        description="Your first steps into Python programming - learn basic concepts and syntax",
        order_index=1
    )
    db.session.add(module1)
    db.session.flush()
    
    # 1.1 What is Programming?
    lesson1_1 = Lesson(
        module_id=module1.module_id,
        title="What is Programming?",
        content_md="""# What is Programming?

Programming is telling a computer what to do using a language it understands. Just like you communicate with friends in English, we communicate with computers using Python.

## Why Learn Python?
- **Easy to learn**: Python syntax is close to human language
- **Powerful**: Can build websites, analyze data, create AI applications
- **Widely used**: Major companies like Google, Instagram, and Spotify use Python

## Your First Step
Let's start with the simplest thing - making the computer say "Hello"!

## Key Programming Concepts
- **Code**: Instructions written for the computer
- **Program**: A collection of code that performs a task
- **Syntax**: The rules of the programming language
""",
        prerequisite_lesson_id=None,
        order_index=1
    )
    db.session.add(lesson1_1)
    db.session.flush()
    
    # 1.2 Your First Program
    lesson1_2 = Lesson(
        module_id=module1.module_id,
        title="Your First Program: Hello, World!",
        content_md="""# Your First Python Program

In the programming world, the first program is traditionally printing "Hello, World!". It's a rite of passage!

## The print() Function
`print()` is one of the most basic functions in Python. It displays text on the screen.

```python
print("Hello, World!")
```

## Syntax Rules
- Use parentheses `()`
- Text must be surrounded by quotes `""`
- Pay attention to capitalization: `Print` and `print` are different
- Don't forget the closing parenthesis!

## Try It Yourself
Now it's your turn! Write your first program and join millions of programmers worldwide.
""",
        prerequisite_lesson_id=lesson1_1.lesson_id,
        order_index=2
    )
    db.session.add(lesson1_2)
    db.session.flush()
    
    # Exercise for lesson 1.2
    exercise1_2 = Exercise(
        lesson_id=lesson1_2.lesson_id,
        problem_statement="""Write a program that prints "Hello, World!" to the screen.

**Instructions:**
- Use the print() function
- Make sure to include the quotes around the text
- The output should be exactly: Hello, World!""",
        hints='["Remember to use the print() function", "Text needs to be surrounded by quotes", "Check for any spelling mistakes"]'
    )
    db.session.add(exercise1_2)
    db.session.flush()
    
    # Test case
    test_case1_2 = TestCase(
        exercise_id=exercise1_2.exercise_id,
        input_data="",
        expected_output="Hello, World!"
    )
    db.session.add(test_case1_2)
    
    # 1.3 Code Comments
    lesson1_3 = Lesson(
        module_id=module1.module_id,
        title="Code Comments",
        content_md="""# Code Comments

Comments are explanatory notes written for humans - the computer ignores them. Think of them as notes in your textbook!

## Why Write Comments?
- Help others understand your code
- Help future you remember what the code does
- Make code clearer and more maintainable
- Document complex logic

## Single-line Comments
Use `#` to start a comment:

```python
# This is a comment
print("Hello!")  # This is also a comment
```

## Multi-line Comments
For longer explanations:

```python
# This program demonstrates
# how to print messages
# to the screen
print("Learning Python is fun!")
```

## Good Comment Practices
- Explain **why** you're doing something, not just **what**
- Keep comments concise and clear
- Update comments when you change code
- Don't comment obvious things
""",
        prerequisite_lesson_id=lesson1_2.lesson_id,
        order_index=3
    )
    db.session.add(lesson1_3)
    db.session.flush()
    
    exercise1_3 = Exercise(
        lesson_id=lesson1_3.lesson_id,
        problem_statement="""Write a program that:
1. Includes a comment explaining what the program does
2. Prints "Learning Python is awesome!"

The comment should be placed above the print statement and should explain the purpose of the program.""",
        hints='["Use # to start a comment", "Place the comment above your code", "Remember the print() function syntax"]'
    )
    db.session.add(exercise1_3)
    db.session.flush()
    
    test_case1_3 = TestCase(
        exercise_id=exercise1_3.exercise_id,
        input_data="",
        expected_output="Learning Python is awesome!"
    )
    db.session.add(test_case1_3)
    
    # 1.4 Variables
    lesson1_4 = Lesson(
        module_id=module1.module_id,
        title="Variables",
        content_md="""# Variables

Variables are like containers that store data. You can think of them as labeled boxes where you keep different things.

## Creating Variables
```python
name = "Alice"
age = 25
```

## Variable Naming Rules
- Can contain letters, numbers, and underscores
- Cannot start with a number
- Case-sensitive (`Name` and `name` are different)
- Cannot use Python keywords (`if`, `for`, etc.)

## Good Naming Practices
- Use descriptive names: `student_name` is better than `x`
- Use lowercase with underscores: `user_age`
- Be consistent with your naming style
- Avoid abbreviations that aren't clear

## Using Variables
```python
name = "Alice"
print(name)  # Output: Alice

# You can change variable values
name = "Bob"
print(name)  # Output: Bob
```

## Why Use Variables?
- Store data for later use
- Make code more readable
- Avoid repeating the same values
- Easy to update values in one place
""",
        prerequisite_lesson_id=lesson1_3.lesson_id,
        order_index=4
    )
    db.session.add(lesson1_4)
    db.session.flush()
    
    exercise1_4 = Exercise(
        lesson_id=lesson1_4.lesson_id,
        problem_statement="""Create a variable to store your name and then print it.

**Requirements:**
1. Create a variable called `my_name`
2. Assign it the value "Python Learner"
3. Use the print function to display the variable's value""",
        hints='["Variable syntax: variable_name = value", "Strings need quotes around them", "Use print(variable_name) to print a variable"]'
    )
    db.session.add(exercise1_4)
    db.session.flush()
    
    test_case1_4 = TestCase(
        exercise_id=exercise1_4.exercise_id,
        input_data="",
        expected_output="Python Learner"
    )
    db.session.add(test_case1_4)
    
    # Module 2: Data Types and Operations
    module2 = Module(
        title="Data Types and Operations",
        description="Learn to work with different types of data in Python",
        order_index=2
    )
    db.session.add(module2)
    db.session.flush()
    
    # 2.1 Strings
    lesson2_1 = Lesson(
        module_id=module2.module_id,
        title="Working with Strings",
        content_md="""# Working with Strings

Strings are used to represent text in Python. They must be enclosed in quotes.

## Creating Strings
```python
name = "Alice"
message = 'Hello, World!'
long_text = "This is a longer piece of text"
```

## String Concatenation
You can join strings using the `+` operator:
```python
first_name = "John"
last_name = "Smith"
full_name = first_name + " " + last_name
print(full_name)  # Output: John Smith
```

## String Methods
Strings have built-in methods to manipulate text:
```python
text = "hello world"
print(text.upper())      # HELLO WORLD
print(text.title())      # Hello World
print(text.capitalize()) # Hello world
```

## f-Strings (String Formatting)
A modern way to include variables in strings:
```python
name = "Alice"
age = 25
message = f"My name is {name} and I am {age} years old"
print(message)
```

## Escape Characters
Use backslash to include special characters:
```python
print("She said, \"Hello!\"")  # She said, "Hello!"
print("Line 1\nLine 2")        # Creates a new line
```
""",
        prerequisite_lesson_id=lesson1_4.lesson_id,
        order_index=1
    )
    db.session.add(lesson2_1)
    db.session.flush()
    
    exercise2_1 = Exercise(
        lesson_id=lesson2_1.lesson_id,
        problem_statement="""Create a self-introduction using string concatenation.

**Requirements:**
1. Create a variable `name` with the value "Alex"
2. Create a variable `hobby` with the value "coding"
3. Combine them to create the message: "Hi, I'm Alex and I love coding!"
4. Print the result

Use string concatenation with the + operator.""",
        hints='["Use + to join strings together", "Don\'t forget spaces and punctuation", "Remember the exclamation mark at the end"]'
    )
    db.session.add(exercise2_1)
    db.session.flush()
    
    test_case2_1 = TestCase(
        exercise_id=exercise2_1.exercise_id,
        input_data="",
        expected_output="Hi, I'm Alex and I love coding!"
    )
    db.session.add(test_case2_1)
    
    # 2.2 Numbers and Math
    lesson2_2 = Lesson(
        module_id=module2.module_id,
        title="Numbers and Mathematical Operations",
        content_md="""# Numbers and Mathematical Operations

Python can work with different types of numbers and perform mathematical calculations.

## Number Types
```python
# Integers (whole numbers)
age = 25
score = 100

# Floats (decimal numbers)
price = 19.99
temperature = 98.6
```

## Basic Mathematical Operations
```python
# Addition
result = 5 + 3    # 8

# Subtraction
result = 10 - 4   # 6

# Multiplication
result = 6 * 7    # 42

# Division
result = 15 / 3   # 5.0 (always returns a float)

# Integer Division
result = 15 // 4  # 3 (removes decimal part)

# Remainder (Modulo)
result = 10 % 3   # 1

# Exponentiation
result = 2 ** 3   # 8 (2 to the power of 3)
```

## Order of Operations
Python follows mathematical order of operations (PEMDAS):
```python
result = 2 + 3 * 4    # 14 (not 20)
result = (2 + 3) * 4  # 20 (parentheses first)
```

## Working with Variables
```python
a = 10
b = 3
sum_result = a + b
print(f"The sum of {a} and {b} is {sum_result}")
```
""",
        prerequisite_lesson_id=lesson2_1.lesson_id,
        order_index=2
    )
    db.session.add(lesson2_2)
    db.session.flush()
    
    exercise2_2 = Exercise(
        lesson_id=lesson2_2.lesson_id,
        problem_statement="""Create a simple calculator program.

**Requirements:**
1. Create two variables: `num1 = 15` and `num2 = 4`
2. Calculate and print the sum: "Sum: 19"
3. Calculate and print the difference: "Difference: 11"
4. Calculate and print the product: "Product: 60"

Each result should be printed on a separate line with the format shown above.""",
        hints='["Use + for addition, - for subtraction, * for multiplication", "Use f-strings or string concatenation for output", "Each calculation should be on its own line"]'
    )
    db.session.add(exercise2_2)
    db.session.flush()
    
    test_case2_2 = TestCase(
        exercise_id=exercise2_2.exercise_id,
        input_data="",
        expected_output="Sum: 19\nDifference: 11\nProduct: 60"
    )
    db.session.add(test_case2_2)
    
    print("   ✅ English course content created successfully")

def create_default_admin():
    """Create default admin account"""
    print("👨‍💼 Creating default admin account...")
    
    # Check if admin already exists
    admin_exists = User.query.filter_by(role=UserRole.ADMIN).first()
    if admin_exists:
        print(f"   ⚠️ Admin account already exists: {admin_exists.username}")
        return
    
    # Create default admin
    admin = User(
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("admin123")
    
    db.session.add(admin)
    print("   ✅ Admin account created successfully")
    print("   📋 Login credentials:")
    print("      Username: admin")
    print("      Password: admin123")

def create_test_data():
    """Create test data for demonstration"""
    print("🧪 Creating test data...")
    
    # Create test student users
    students = []
    student_names = ['alice_johnson', 'bob_smith', 'charlie_brown', 'diana_wilson', 'evan_davis']
    
    for i, name in enumerate(student_names):
        student = User(
            username=name,
            email=f'{name}@example.com',
            role=UserRole.STUDENT
        )
        student.set_password('password123')
        db.session.add(student)
        students.append(student)
    
    db.session.flush()
    
    # Get exercises
    exercises = Exercise.query.all()
    
    # Create submission records and learning progress
    import random
    error_types = [ErrorType.SYNTAX_ERROR, ErrorType.NAME_ERROR, ErrorType.LOGIC_ERROR, ErrorType.NO_ERROR]
    
    sample_codes = [
        'print("Hello, World!")',
        '# My first Python program\nprint("Learning Python is awesome!")',
        'my_name = "Python Learner"\nprint(my_name)',
        'name = "Alex"\nhobby = "coding"\nprint("Hi, I\'m " + name + " and I love " + hobby + "!")'
    ]
    
    for student in students:
        for i, exercise in enumerate(exercises):
            # Create multiple submission records (simulating learning process)
            num_attempts = random.randint(2, 5)
            
            for attempt in range(num_attempts):
                is_correct = attempt >= num_attempts - 1  # Last attempt is usually correct
                error_type = ErrorType.NO_ERROR if is_correct else random.choice(error_types[:-1])
                
                code = sample_codes[i] if i < len(sample_codes) else f'print("Attempt {attempt + 1} by {student.username}")'
                
                submission = Submission(
                    user_id=student.user_id,
                    exercise_id=exercise.exercise_id,
                    code=code,
                    is_correct=is_correct,
                    error_type=error_type,
                    error_message=None if is_correct else f'Sample error message for {error_type.value}',
                    output='Expected output' if is_correct else 'Incorrect output',
                    exec_time_sec=random.uniform(0.1, 2.0),
                    code_lines=len(code.split('\n')),
                    code_complexity=random.uniform(1, 5),
                    syntax_score=random.uniform(7, 10) if is_correct else random.uniform(3, 7),
                    timestamp=datetime.now() - timedelta(hours=random.randint(1, 168))  # Last week
                )
                db.session.add(submission)
        
        # Create learning progress records
        lesson_ids = [ex.lesson_id for ex in exercises]
        for lesson_id in set(lesson_ids):
            completed = random.choice([True, True, False])  # 2/3 chance of completion
            
            progress = UserProgress(
                user_id=student.user_id,
                lesson_id=lesson_id,
                status=ProgressStatus.COMPLETED if completed else ProgressStatus.IN_PROGRESS,
                attempts=random.randint(2, 6),
                avg_time_sec=random.uniform(60, 300),
                first_attempt_at=datetime.now() - timedelta(days=random.randint(1, 14)),
                completed_at=datetime.now() - timedelta(days=random.randint(0, 7)) if completed else None
            )
            db.session.add(progress)
    
    print("   ✅ Test data created successfully")
    print(f"      Student users: {len(students)}")
    print(f"      Submission records: ~{len(students) * len(exercises) * 3}")
    print(f"      Progress records: ~{len(students) * len(set([ex.lesson_id for ex in exercises]))}")

def verify_english_database():
    """Verify English database setup"""
    print("\n🔍 Verifying English database setup...")
    
    # 创建独立的app实例用于验证
    from flask import Flask
    from config import get_config
    
    config = get_config()
    app = Flask(__name__)
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{ENGLISH_DB_PATH}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    from extensions import db
    db.init_app(app)
    
    with app.app_context():
        try:
            # Check basic tables
            modules = Module.query.count()
            lessons = Lesson.query.count()
            exercises = Exercise.query.count()
            users = User.query.count()
            
            print(f"   📊 Database Statistics:")
            print(f"      Modules: {modules}")
            print(f"      Lessons: {lessons}")
            print(f"      Exercises: {exercises}")
            print(f"      Users: {users}")
            
            # Check English content
            first_module = Module.query.first()
            if first_module and "Programming Fundamentals" in first_module.title:
                print("   🇬🇧 English content verified!")
            
            if modules > 0 and lessons > 0 and exercises > 0 and users > 0:
                print("   ✅ Database verification passed!")
                return True
            else:
                print("   ❌ Database data incomplete")
                return False
                
        except Exception as e:
            print(f"   ❌ Database verification failed: {str(e)}")
            return False

def main():
    """Main function"""
    print("="*70)
    print("🐍 Python Learning Platform - English Database Setup")
    print("🇬🇧 Perfect for UK MSc Computer Science Project Demonstrations")
    print("="*70)
    
    try:
        # Backup existing English database
        backup_path = backup_existing_database()
        
        # Create new English database
        create_fresh_english_database()
        
        # Verify setup
        if verify_english_database():
            print("\n🎉 English database setup completed successfully!")
            print(f"\n📍 Database Location: {ENGLISH_DB_PATH}")
            print("\n📖 Next Steps:")
            print("   1. Update your config to use the English database")
            print("   2. Start backend: python app.py")
            print("   3. Start frontend: cd ../frontend && npm run dev")
            print("   4. Access application: http://localhost:3000")
            print("\n🌟 Features:")
            print("   • All content in English")
            print("   • Professional course structure")
            print("   • Ready for academic presentations")
            print("   • Sample student data included")
            
            if backup_path:
                print(f"\n💾 Previous English database backed up to: {backup_path}")
        else:
            print("\n❌ English database setup verification failed")
            
    except Exception as e:
        print(f"\n❌ English database setup failed: {str(e)}")
        print("💡 Suggestions:")
        print("   1. Check Python environment")
        print("   2. Ensure no other programs are using the database file")
        print("   3. Check file permissions")
        return False
    
    print("="*70)
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎓 Your English database is ready for your MSc project presentation!")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")