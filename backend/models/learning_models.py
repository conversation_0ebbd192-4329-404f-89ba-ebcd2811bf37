from extensions import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from typing import Optional
from enum import Enum
import json

class UserRole(Enum):
    """用户角色枚举"""
    STUDENT = "student"
    ADMIN = "admin"

class User(db.Model):
    """用户模型 - Learner Model的核心组件"""
    __tablename__ = 'users'
    
    user_id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.Enum(UserRole), default=UserRole.STUDENT, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    progress = db.relationship('UserProgress', backref='user', lazy=True)
    submissions = db.relationship('Submission', backref='user', lazy=True)
    
    def set_password(self, password: str) -> None:
        """设置加密密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.role == UserRole.ADMIN
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'role': self.role.value,
            'created_at': self.created_at.isoformat()
        }

class ProgressStatus(Enum):
    """学习进度状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"

class UserProgress(db.Model):
    """用户学习进度模型 - Learner Model核心组件（Overlay Model实现）"""
    __tablename__ = 'user_progress'
    
    progress_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    lesson_id = db.Column(db.Integer, db.ForeignKey('lessons.lesson_id'), nullable=False)
    status = db.Column(db.Enum(ProgressStatus), default=ProgressStatus.NOT_STARTED, nullable=False)
    attempts = db.Column(db.Integer, default=0)
    avg_time_sec = db.Column(db.Float, nullable=True)
    first_attempt_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 复合唯一约束
    __table_args__ = (db.UniqueConstraint('user_id', 'lesson_id', name='unique_user_lesson'),)
    
    def start_lesson(self) -> None:
        """开始学习课程"""
        if self.status == ProgressStatus.NOT_STARTED:
            self.status = ProgressStatus.IN_PROGRESS
            self.first_attempt_at = datetime.utcnow()
    
    def complete_lesson(self) -> None:
        """完成课程"""
        self.status = ProgressStatus.COMPLETED
        self.completed_at = datetime.utcnow()
    
    def increment_attempts(self) -> None:
        """增加尝试次数"""
        self.attempts += 1
    
    def update_avg_time(self, solving_time_sec: float) -> None:
        """更新平均做题时间（从开始写代码到提交的时间）"""
        # 确保输入是数字类型
        if isinstance(solving_time_sec, str):
            try:
                solving_time_sec = float(solving_time_sec)
            except (ValueError, TypeError):
                solving_time_sec = 0.0
        elif solving_time_sec is None:
            solving_time_sec = 0.0
        
        print(f"DEBUG: update_avg_time调用 - 输入时间: {solving_time_sec}, 当前平均: {self.avg_time_sec}, 尝试次数: {self.attempts}")
        
        if self.avg_time_sec is None:
            self.avg_time_sec = solving_time_sec
        else:
            # 简单移动平均
            self.avg_time_sec = (self.avg_time_sec * (self.attempts - 1) + solving_time_sec) / self.attempts
        
        print(f"DEBUG: 计算后平均时间: {self.avg_time_sec}")
    
    def should_show_hint(self) -> bool:
        """判断是否应该显示提示（基于尝试次数和时间）"""
        return (self.attempts >= 3) or (self.avg_time_sec and self.avg_time_sec > 90)
    
    def to_dict(self) -> dict:
        return {
            'progress_id': self.progress_id,
            'user_id': self.user_id,
            'lesson_id': self.lesson_id,
            'status': self.status.value,
            'attempts': self.attempts,
            'avg_time_sec': self.avg_time_sec,
            'first_attempt_at': self.first_attempt_at.isoformat() if self.first_attempt_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'should_show_hint': self.should_show_hint()
        }

class ErrorType(Enum):
    """代码错误类型枚举"""
    SYNTAX_ERROR = "syntax_error"
    NAME_ERROR = "name_error"
    TYPE_ERROR = "type_error"
    RUNTIME_ERROR = "runtime_error"
    LOGIC_ERROR = "logic_error"
    TIMEOUT_ERROR = "timeout_error"
    NO_ERROR = "no_error"

class Submission(db.Model):
    """代码提交记录模型 - Interaction Model核心组件"""
    __tablename__ = 'submissions'
    
    submission_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    exercise_id = db.Column(db.Integer, db.ForeignKey('exercises.exercise_id'), nullable=False)
    code = db.Column(db.Text, nullable=False)
    is_correct = db.Column(db.Boolean, nullable=False)
    error_type = db.Column(db.Enum(ErrorType), default=ErrorType.NO_ERROR)
    error_message = db.Column(db.Text, nullable=True)
    output = db.Column(db.Text, nullable=True)
    exec_time_sec = db.Column(db.Float, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 新增代码分析统计字段
    code_lines = db.Column(db.Integer, nullable=True)  # 代码行数
    code_complexity = db.Column(db.Float, nullable=True)  # 代码复杂度
    syntax_score = db.Column(db.Float, nullable=True)  # 语法质量分数
    api_analysis_result = db.Column(db.Text, nullable=True)  # API分析结果（JSON格式）
    
    def to_dict(self) -> dict:
        return {
            'submission_id': self.submission_id,
            'user_id': self.user_id,
            'exercise_id': self.exercise_id,
            'code': self.code,
            'is_correct': self.is_correct,
            'error_type': self.error_type.value if self.error_type else None,
            'error_message': self.error_message,
            'output': self.output,
            'exec_time_sec': self.exec_time_sec,
            'timestamp': self.timestamp.isoformat(),
            'code_lines': self.code_lines,
            'code_complexity': self.code_complexity,
            'syntax_score': self.syntax_score,
            'api_analysis_result': json.loads(self.api_analysis_result) if self.api_analysis_result else None
        }

class HintUsage(db.Model):
    """提示使用记录模型 - Interaction Model组件"""
    __tablename__ = 'hint_usage'
    
    hint_usage_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    exercise_id = db.Column(db.Integer, db.ForeignKey('exercises.exercise_id'), nullable=False)
    hint_level = db.Column(db.Integer, nullable=False)  # 提示级别（1,2,3...）
    used_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self) -> dict:
        return {
            'hint_usage_id': self.hint_usage_id,
            'user_id': self.user_id,
            'exercise_id': self.exercise_id,
            'hint_level': self.hint_level,
            'used_at': self.used_at.isoformat()
        }

class FeedbackType(Enum):
    """反馈类型枚举"""
    JUDGE0_STRUCTURAL = "judge0_structural"  # Judge0结构化反馈
    OPENAI_EDUCATIONAL = "openai_educational"  # OpenAI教育性反馈
    DEEPSEEK_EDUCATIONAL = "deepseek_educational"  # DeepSeek教育性反馈
    LOCAL_RULES = "local_rules"  # 本地规则反馈
    HYBRID = "hybrid"  # 混合反馈

class FeedbackInstance(db.Model):
    """反馈实例模型 - 用于对比不同反馈方法的效果"""
    __tablename__ = 'feedback_instances'
    
    feedback_id = db.Column(db.Integer, primary_key=True)
    submission_id = db.Column(db.Integer, db.ForeignKey('submissions.submission_id'), nullable=False)
    feedback_type = db.Column(db.Enum(FeedbackType), nullable=False)
    feedback_content = db.Column(db.Text, nullable=False)  # 反馈内容
    generation_time_ms = db.Column(db.Float, nullable=True)  # 生成时间（毫秒）
    api_cost = db.Column(db.Float, nullable=True)  # API调用成本
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    submission = db.relationship('Submission', backref='feedback_instances')
    effectiveness_records = db.relationship('FeedbackEffectiveness', backref='feedback_instance', lazy=True)
    
    def to_dict(self) -> dict:
        return {
            'feedback_id': self.feedback_id,
            'submission_id': self.submission_id,
            'feedback_type': self.feedback_type.value,
            'feedback_content': self.feedback_content,
            'generation_time_ms': self.generation_time_ms,
            'api_cost': self.api_cost,
            'created_at': self.created_at.isoformat()
        }

class FeedbackEffectiveness(db.Model):
    """反馈效果评估模型 - 追踪不同反馈方法的学习效果"""
    __tablename__ = 'feedback_effectiveness'
    
    effectiveness_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    exercise_id = db.Column(db.Integer, db.ForeignKey('exercises.exercise_id'), nullable=False)
    feedback_id = db.Column(db.Integer, db.ForeignKey('feedback_instances.feedback_id'), nullable=False)
    
    # 效果指标
    subsequent_attempts = db.Column(db.Integer, default=0)  # 收到反馈后的尝试次数
    time_to_success_sec = db.Column(db.Float, nullable=True)  # 从反馈到成功的活跃学习时间
    did_succeed = db.Column(db.Boolean, default=False)  # 最终是否成功
    
    # 用户体验评分（可选，通过前端收集）
    helpfulness_rating = db.Column(db.Float, nullable=True)  # 1-5分，有用性评分
    clarity_rating = db.Column(db.Float, nullable=True)  # 1-5分，清晰度评分
    
    # 触发反馈的错误类型 - 用于论文分析
    triggering_error_type = db.Column(db.Enum(ErrorType), nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    success_achieved_at = db.Column(db.DateTime, nullable=True)
    
    def to_dict(self) -> dict:
        return {
            'effectiveness_id': self.effectiveness_id,
            'user_id': self.user_id,
            'exercise_id': self.exercise_id,
            'feedback_id': self.feedback_id,
            'subsequent_attempts': self.subsequent_attempts,
            'time_to_success_sec': self.time_to_success_sec,
            'did_succeed': self.did_succeed,
            'helpfulness_rating': self.helpfulness_rating,
            'clarity_rating': self.clarity_rating,
            'triggering_error_type': self.triggering_error_type.value if self.triggering_error_type else None,
            'created_at': self.created_at.isoformat(),
            'success_achieved_at': self.success_achieved_at.isoformat() if self.success_achieved_at else None
        }
