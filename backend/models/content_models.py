from extensions import db
from datetime import datetime
from typing import Optional
import json

class Module(db.Model):
    """模块模型 - Content Model组件"""
    __tablename__ = 'modules'
    
    module_id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    order_index = db.Column(db.Integer, nullable=False)
    
    # 关系
    lessons = db.relationship('Lesson', backref='module', lazy=True, order_by='Lesson.order_index')
    
    def to_dict(self) -> dict:
        return {
            'module_id': self.module_id,
            'title': self.title,
            'description': self.description,
            'order_index': self.order_index,
            'lessons': [lesson.to_summary_dict() for lesson in self.lessons]
        }

class Lesson(db.Model):
    """课程模型 - Content Model核心组件（Knowledge Components）"""
    __tablename__ = 'lessons'
    
    lesson_id = db.Column(db.Integer, primary_key=True)
    module_id = db.Column(db.Integer, db.ForeignKey('modules.module_id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content_md = db.Column(db.Text, nullable=False)
    prerequisite_lesson_id = db.Column(db.Integer, db.ForeignKey('lessons.lesson_id'), nullable=True)
    order_index = db.Column(db.Integer, nullable=False)
    
    # 关系
    exercises = db.relationship('Exercise', backref='lesson', lazy=True)
    progress_records = db.relationship('UserProgress', backref='lesson', lazy=True)
    
    def to_dict(self) -> dict:
        return {
            'lesson_id': self.lesson_id,
            'module_id': self.module_id,
            'title': self.title,
            'content_md': self.content_md,
            'prerequisite_lesson_id': self.prerequisite_lesson_id,
            'order_index': self.order_index,
            'exercises': [exercise.to_dict() for exercise in self.exercises]
        }

    def to_summary_dict(self) -> dict:
        return {
            'lesson_id': self.lesson_id,
            'module_id': self.module_id,
            'title': self.title,
            'prerequisite_lesson_id': self.prerequisite_lesson_id,
            'order_index': self.order_index
        }

class Exercise(db.Model):
    """练习模型 - Content Model组件"""
    __tablename__ = 'exercises'
    
    exercise_id = db.Column(db.Integer, primary_key=True)
    lesson_id = db.Column(db.Integer, db.ForeignKey('lessons.lesson_id'), nullable=False)
    problem_statement = db.Column(db.Text, nullable=False)
    hints = db.Column(db.Text)  # JSON格式存储分级提示
    
    # 关系
    test_cases = db.relationship('TestCase', backref='exercise', lazy=True)
    submissions = db.relationship('Submission', backref='exercise', lazy=True)
    
    def get_hints(self) -> list:
        """获取分级提示列表"""
        if self.hints:
            return json.loads(self.hints)
        return []
    
    def set_hints(self, hints_list: list) -> None:
        """设置分级提示"""
        self.hints = json.dumps(hints_list, ensure_ascii=False)
    
    def to_dict(self) -> dict:
        return {
            'exercise_id': self.exercise_id,
            'lesson_id': self.lesson_id,
            'problem_statement': self.problem_statement,
            'hints': self.get_hints(),
            'test_cases': [tc.to_dict() for tc in self.test_cases]
        }

class TestCase(db.Model):
    """测试用例模型 - Content Model组件"""
    __tablename__ = 'test_cases'
    
    test_case_id = db.Column(db.Integer, primary_key=True)
    exercise_id = db.Column(db.Integer, db.ForeignKey('exercises.exercise_id'), nullable=False)
    input_data = db.Column(db.Text)
    expected_output = db.Column(db.Text, nullable=False)
    is_hidden = db.Column(db.Boolean, default=False)  # 隐藏测试用例
    
    def to_dict(self) -> dict:
        return {
            'test_case_id': self.test_case_id,
            'exercise_id': self.exercise_id,
            'input_data': self.input_data,
            'expected_output': self.expected_output,
            'is_hidden': self.is_hidden
        }
