# 后端环境配置示例
# 复制此文件为 .env 并根据需要修改

# 环境模式
FLASK_ENV=development

# 服务器配置
HOST=0.0.0.0
PORT=5000
DEBUG=true

# 前端配置
FRONTEND_URL=http://localhost:3000
FRONTEND_PORT=3000

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here

# 数据库配置 (可选，默认使用SQLite)
# DATABASE_URL=sqlite:///learning_platform.db
# DATABASE_URL=postgresql://user:pass@localhost/dbname 

# 外部API配置
# Judge0 API (代码执行服务)
JUDGE0_API_KEY=your-judge0-api-key-here
JUDGE0_BASE_URL=https://judge0-ce.p.rapidapi.com
JUDGE0_HOST=judge0-ce.p.rapidapi.com
JUDGE0_TIMEOUT=15
PYTHON_LANGUAGE_ID=71

# OpenRouter API (AI分析服务)
OPENROUTER_API_KEY=your-openrouter-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_TIMEOUT=15