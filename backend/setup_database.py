#!/usr/bin/env python3
"""
简化的数据库设置脚本
用于全新安装，替代复杂的迁移脚本
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, DB_PATH
from extensions import db
from models.content_models import Module, Lesson, Exercise, TestCase
from models.learning_models import User, UserRole, ProgressStatus, Submission, UserProgress, ErrorType, FeedbackEffectiveness, HintUsage

def backup_existing_database():
    """备份现有数据库（如果存在）"""
    if os.path.exists(DB_PATH):
        backup_path = f"{DB_PATH}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(DB_PATH, backup_path)
        print(f"✅ 现有数据库已备份到: {backup_path}")
        return backup_path
    return None

def create_fresh_database():
    """创建全新的数据库"""
    app = create_app()
    
    with app.app_context():
        # 删除所有表
        db.drop_all()
        print("📝 清除旧的数据库表...")
        
        # 创建所有表
        db.create_all()
        print("📊 创建新的数据库表...")
        
        # 初始化示例数据
        init_sample_content()
        
        # 创建默认管理员
        create_default_admin()
        
        # 创建测试数据
        create_test_data()
        
        # 提交所有更改
        db.session.commit()
        print("💾 数据库设置完成！")

def init_sample_content():
    """初始化示例教学内容"""
    print("📚 创建示例课程内容...")
    
    # 创建模块1：入门基础
    module1 = Module(
        title="入门基础",
        description="Python编程的第一步，学习基本概念和语法",
        order_index=1
    )
    db.session.add(module1)
    db.session.flush()
    
    # 1.1 什么是编程？
    lesson1_1 = Lesson(
        module_id=module1.module_id,
        title="什么是编程？",
        content_md="""# 什么是编程？

编程就是用计算机能理解的语言来告诉计算机做什么。就像你用中文跟朋友交流一样，我们用Python语言跟计算机交流。

## 为什么学习Python？
- **简单易学**：Python语法接近人类语言
- **功能强大**：可以做网站、数据分析、人工智能等
- **广泛应用**：很多大公司都在使用Python

## 你的第一步
让我们从最简单的开始 - 让计算机说"你好"！
""",
        prerequisite_lesson_id=None,
        order_index=1
    )
    db.session.add(lesson1_1)
    db.session.flush()
    
    # 1.2 第一个程序
    lesson1_2 = Lesson(
        module_id=module1.module_id,
        title="第一个程序：Hello, World!",
        content_md="""# 你的第一个Python程序

在编程世界里，第一个程序通常是让计算机打印"Hello, World!"。这是一个传统！

## print() 函数
`print()` 是Python中最基本的函数之一，它可以在屏幕上显示文字。

```python
print("Hello, World!")
```

## 语法要点
- 使用英文括号 `()`
- 文字要用引号包围 `""`
- 注意大小写：`Print` 和 `print` 是不同的

现在轮到你了！试着写出你的第一个程序。
""",
        prerequisite_lesson_id=lesson1_1.lesson_id,
        order_index=2
    )
    db.session.add(lesson1_2)
    db.session.flush()
    
    # 为lesson1_2创建练习
    exercise1_2 = Exercise(
        lesson_id=lesson1_2.lesson_id,
        problem_statement="""编写一个程序，让它打印出 "Hello, World!" 

提示：使用 print() 函数，记住要加引号！""",
        hints='["记住使用 print() 函数", "文字需要用引号包围", "检查是否有拼写错误"]'
    )
    db.session.add(exercise1_2)
    db.session.flush()
    
    # 测试用例
    test_case1_2 = TestCase(
        exercise_id=exercise1_2.exercise_id,
        input_data="",
        expected_output="Hello, World!"
    )
    db.session.add(test_case1_2)
    
    # 1.3 代码注释
    lesson1_3 = Lesson(
        module_id=module1.module_id,
        title="代码注释",
        content_md="""# 代码注释

注释是写给人看的说明文字，计算机会忽略它们。这就像在课本上做笔记一样！

## 为什么要写注释？
- 帮助别人理解你的代码
- 帮助未来的自己记住代码的作用
- 让代码更清晰易懂

## 单行注释
使用 `#` 开始一行注释：

```python
# 这是一行注释
print("Hello!")  # 这也是注释
```

## 好的注释习惯
- 解释**为什么**这样做，而不只是**做了什么**
- 保持注释简洁明了
- 及时更新注释内容
""",
        prerequisite_lesson_id=lesson1_2.lesson_id,
        order_index=3
    )
    db.session.add(lesson1_3)
    db.session.flush()
    
    exercise1_3 = Exercise(
        lesson_id=lesson1_3.lesson_id,
        problem_statement="""编写一个程序：
1. 添加一行注释说明程序的作用
2. 打印 "学习Python真有趣！"

注释应该写在print语句的上方。""",
        hints='["使用 # 开始注释", "注释写在代码上方", "记住print函数的用法"]'
    )
    db.session.add(exercise1_3)
    db.session.flush()
    
    test_case1_3 = TestCase(
        exercise_id=exercise1_3.exercise_id,
        input_data="",
        expected_output="学习Python真有趣！"
    )
    db.session.add(test_case1_3)
    
    # 1.4 变量
    lesson1_4 = Lesson(
        module_id=module1.module_id,
        title="变量",
        content_md="""# 变量

变量就像一个盒子，可以用来存储数据。你可以给盒子起个名字，然后往里面放东西。

## 创建变量
```python
name = "小明"
age = 18
```

## 变量命名规则
- 只能包含字母、数字和下划线
- 不能以数字开头
- 区分大小写
- 不能使用Python的关键字

## 好的命名习惯
- 使用有意义的名字：`student_name` 比 `x` 更好
- 使用小写字母和下划线：`user_age`
- 避免使用拼音：用 `name` 而不是 `mingzi`

## 使用变量
```python
name = "小明"
print(name)  # 输出：小明
```
""",
        prerequisite_lesson_id=lesson1_3.lesson_id,
        order_index=4
    )
    db.session.add(lesson1_4)
    db.session.flush()
    
    exercise1_4 = Exercise(
        lesson_id=lesson1_4.lesson_id,
        problem_statement="""创建一个变量来存储你的名字，然后打印它。

要求：
1. 创建一个名为 `my_name` 的变量
2. 给它赋值为 "Python学习者"
3. 使用print函数打印这个变量""",
        hints='["变量名 = 值", "字符串要用引号", "print(变量名) 来打印变量"]'
    )
    db.session.add(exercise1_4)
    db.session.flush()
    
    test_case1_4 = TestCase(
        exercise_id=exercise1_4.exercise_id,
        input_data="",
        expected_output="Python学习者"
    )
    db.session.add(test_case1_4)
    
    # 创建模块2：数据处理
    module2 = Module(
        title="数据处理",
        description="学习处理不同类型的数据",
        order_index=2
    )
    db.session.add(module2)
    db.session.flush()
    
    # 2.1 字符串
    lesson2_1 = Lesson(
        module_id=module2.module_id,
        title="字符串",
        content_md="""# 字符串

字符串是用来表示文字的数据类型。在Python中，字符串要用引号包围。

## 创建字符串
```python
name = "小明"
message = '你好，世界！'
```

## 字符串拼接
可以用 `+` 连接字符串：
```python
first_name = "张"
last_name = "三"
full_name = first_name + last_name
print(full_name)  # 输出：张三
```

## 字符串方法
```python
text = "hello"
print(text.upper())  # 转大写：HELLO
print(text.title())  # 首字母大写：Hello
```

## f-字符串（格式化）
```python
name = "小明"
age = 18
message = f"我叫{name}，今年{age}岁"
print(message)
```
""",
        prerequisite_lesson_id=lesson1_4.lesson_id,
        order_index=1
    )
    db.session.add(lesson2_1)
    db.session.flush()
    
    exercise2_1 = Exercise(
        lesson_id=lesson2_1.lesson_id,
        problem_statement="""使用字符串拼接创建一条自我介绍。

要求：
1. 创建变量 `name` 存储 "小李"
2. 创建变量 `hobby` 存储 "编程"
3. 将它们拼接成 "大家好，我是小李，我喜欢编程！"
4. 打印结果""",
        hints='["使用 + 连接字符串", "注意空格和标点符号", "别忘了感叹号"]'
    )
    db.session.add(exercise2_1)
    db.session.flush()
    
    test_case2_1 = TestCase(
        exercise_id=exercise2_1.exercise_id,
        input_data="",
        expected_output="大家好，我是小李，我喜欢编程！"
    )
    db.session.add(test_case2_1)
    
    print("   ✅ 基础课程内容创建完成")

def create_default_admin():
    """创建默认管理员账户"""
    print("👨‍💼 创建默认管理员账户...")
    
    # 检查是否已存在管理员
    admin_exists = User.query.filter_by(role=UserRole.ADMIN).first()
    if admin_exists:
        print(f"   ⚠️ 管理员账户已存在: {admin_exists.username}")
        return
    
    # 创建默认管理员
    admin = User(
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("admin123")
    
    db.session.add(admin)
    print("   ✅ 管理员账户创建成功")
    print("   📋 登录信息:")
    print("      用户名: admin")
    print("      密码: admin123")

def create_test_data():
    """创建测试数据用于演示"""
    print("🧪 创建测试数据...")
    
    # 创建几个测试学生用户
    students = []
    for i in range(5):
        student = User(
            username=f'student{i+1}',
            email=f'student{i+1}@test.com',
            role=UserRole.STUDENT
        )
        student.set_password('password123')
        db.session.add(student)
        students.append(student)
    
    db.session.flush()
    
    # 获取练习
    exercises = Exercise.query.all()
    
    # 创建一些提交记录和学习进度
    import random
    import json
    error_types = [ErrorType.SYNTAX_ERROR, ErrorType.NAME_ERROR, ErrorType.LOGIC_ERROR, ErrorType.NO_ERROR]
    
    for student in students:
        for exercise in exercises[:3]:  # 每个学生做前3个练习
            # 创建多次提交记录（模拟学习过程）
            for attempt in range(random.randint(2, 6)):
                is_correct = attempt >= random.randint(1, 3)  # 后面的尝试更容易成功
                error_type = ErrorType.NO_ERROR if is_correct else random.choice(error_types[:-1])
                
                submission = Submission(
                    user_id=student.user_id,
                    exercise_id=exercise.exercise_id,
                    code=f'print("Hello from {student.username}, attempt {attempt+1}")',
                    is_correct=is_correct,
                    error_type=error_type,
                    error_message=None if is_correct else f'模拟错误信息 {error_type.value}',
                    output='Hello World' if is_correct else '',
                    exec_time_sec=random.uniform(0.5, 3.0),
                    code_lines=random.randint(1, 10),
                    code_complexity=random.uniform(1, 10),
                    syntax_score=random.uniform(3, 10),
                    timestamp=datetime.now() - timedelta(days=random.randint(0, 7))
                )
                db.session.add(submission)
        
        # 创建学习进度记录
        lesson_ids = [ex.lesson_id for ex in exercises[:3]]
        for lesson_id in set(lesson_ids):
            progress = UserProgress(
                user_id=student.user_id,
                lesson_id=lesson_id,
                status=random.choice([ProgressStatus.COMPLETED, ProgressStatus.IN_PROGRESS]),
                attempts=random.randint(2, 8),
                avg_time_sec=random.uniform(30, 180),
                first_attempt_at=datetime.now() - timedelta(days=random.randint(1, 7)),
                completed_at=datetime.now() - timedelta(days=random.randint(0, 3)) if random.choice([True, False]) else None
            )
            db.session.add(progress)
    
    print("   ✅ 测试数据创建完成")
    print(f"      学生用户: {len(students)} 个")
    print(f"      提交记录: ~{len(students) * 3 * 4} 条")
    print(f"      学习进度: ~{len(students) * 3} 条")

def verify_database():
    """验证数据库设置"""
    print("\n🔍 验证数据库设置...")
    
    app = create_app()
    with app.app_context():
        try:
            # 检查基本表
            modules = Module.query.count()
            lessons = Lesson.query.count()
            exercises = Exercise.query.count()
            users = User.query.count()
            
            print(f"   📊 数据统计:")
            print(f"      模块: {modules} 个")
            print(f"      课程: {lessons} 个")
            print(f"      练习: {exercises} 个")
            print(f"      用户: {users} 个")
            
            if modules > 0 and lessons > 0 and exercises > 0 and users > 0:
                print("   ✅ 数据库验证通过！")
                return True
            else:
                print("   ❌ 数据库数据不完整")
                return False
                
        except Exception as e:
            print(f"   ❌ 数据库验证失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("="*60)
    print("🐍 交互式Python学习平台 - 数据库设置")
    print("="*60)
    
    try:
        # 备份现有数据库
        backup_path = backup_existing_database()
        
        # 创建新数据库
        create_fresh_database()
        
        # 验证设置
        if verify_database():
            print("\n🎉 数据库设置成功完成！")
            print("\n📖 下一步:")
            print("   1. 启动后端: python app.py")
            print("   2. 启动前端: cd ../frontend && npm run dev")
            print("   3. 访问应用: http://localhost:3000")
            
            if backup_path:
                print(f"\n💾 旧数据库备份位置: {backup_path}")
        else:
            print("\n❌ 数据库设置验证失败")
            
    except Exception as e:
        print(f"\n❌ 数据库设置失败: {str(e)}")
        print("💡 建议:")
        print("   1. 检查Python环境")
        print("   2. 确保没有其他程序占用数据库文件")
        print("   3. 检查文件权限")
        return False
    
    print("="*60)
    return True

if __name__ == "__main__":
    main()