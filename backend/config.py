"""
后端配置管理
统一管理数据库、API、端口等配置信息
"""

import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

@dataclass
class Config:
    """基础配置类"""
    # 服务器配置
    HOST: str = '0.0.0.0'
    PORT: int = 5000
    DEBUG: bool = False
    
    # 数据库配置
    DATABASE_URL: Optional[str] = None
    
    # 安全配置
    SECRET_KEY: str = 'dev-secret-key-change-in-production'
    JWT_SECRET_KEY: str = 'jwt-secret-string-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES: bool = False
    
    # 前端配置
    FRONTEND_URL: str = 'http://localhost:3000'
    FRONTEND_PORT: int = 3000
    
    # CORS配置
    CORS_ORIGINS: Optional[list] = None
    
    # 外部API配置 (使用环境变量，不在代码中存储真实密钥)
    JUDGE0_API_KEY: str = 'your-judge0-api-key'  # 占位符，实际密钥通过环境变量设置
    JUDGE0_BASE_URL: str = 'https://judge0-ce.p.rapidapi.com'
    JUDGE0_HOST: str = 'judge0-ce.p.rapidapi.com'
    JUDGE0_TIMEOUT: int = 15
    PYTHON_LANGUAGE_ID: int = 71
    
    OPENROUTER_API_KEY: str = 'your-openrouter-api-key'  # 占位符，实际密钥通过环境变量设置
    OPENROUTER_BASE_URL: str = 'https://openrouter.ai/api/v1'
    OPENROUTER_TIMEOUT: int = 15
    
    def __post_init__(self):
        if self.CORS_ORIGINS is None:
            self.CORS_ORIGINS = [self.FRONTEND_URL]

@dataclass 
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG: bool = True
    HOST: str = '0.0.0.0'
    PORT: int = 5000
    FRONTEND_URL: str = 'http://localhost:3000'
    FRONTEND_PORT: int = 3000

@dataclass
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG: bool = False
    HOST: str = '0.0.0.0'
    PORT: int = 80
    FRONTEND_URL: str = 'https://your-domain.com'
    FRONTEND_PORT: int = 80
    JWT_ACCESS_TOKEN_EXPIRES: bool = True

@dataclass
class TestingConfig(Config):
    """测试环境配置"""
    DEBUG: bool = True
    HOST: str = '127.0.0.1'
    PORT: int = 5001
    FRONTEND_URL: str = 'http://localhost:3001'
    FRONTEND_PORT: int = 3001

# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(env: Optional[str] = None) -> Config:
    """获取配置实例"""
    if env is None:
        env = os.getenv('FLASK_ENV', 'development')
    
    config_class = config_map.get(env, config_map['default'])
    config = config_class()
    
    # 允许环境变量覆盖配置
    config.HOST = os.getenv('HOST', config.HOST)
    config.PORT = int(os.getenv('PORT', config.PORT))
    config.DEBUG = os.getenv('DEBUG', str(config.DEBUG)).lower() == 'true'
    config.SECRET_KEY = os.getenv('SECRET_KEY', config.SECRET_KEY)
    config.JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', config.JWT_SECRET_KEY)
    config.FRONTEND_URL = os.getenv('FRONTEND_URL', config.FRONTEND_URL)
    config.FRONTEND_PORT = int(os.getenv('FRONTEND_PORT', config.FRONTEND_PORT))
    
    # 数据库URL
    if os.getenv('DATABASE_URL'):
        config.DATABASE_URL = os.getenv('DATABASE_URL')
    
    # 外部API配置
    config.JUDGE0_API_KEY = os.getenv('JUDGE0_API_KEY', config.JUDGE0_API_KEY)
    config.JUDGE0_BASE_URL = os.getenv('JUDGE0_BASE_URL', config.JUDGE0_BASE_URL)
    config.JUDGE0_HOST = os.getenv('JUDGE0_HOST', config.JUDGE0_HOST)
    config.JUDGE0_TIMEOUT = int(os.getenv('JUDGE0_TIMEOUT', config.JUDGE0_TIMEOUT))
    config.PYTHON_LANGUAGE_ID = int(os.getenv('PYTHON_LANGUAGE_ID', config.PYTHON_LANGUAGE_ID))
    
    config.OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY', config.OPENROUTER_API_KEY)
    config.OPENROUTER_BASE_URL = os.getenv('OPENROUTER_BASE_URL', config.OPENROUTER_BASE_URL)
    config.OPENROUTER_TIMEOUT = int(os.getenv('OPENROUTER_TIMEOUT', config.OPENROUTER_TIMEOUT))
    
    print(f"🔧 后端配置 [{env}]: HOST={config.HOST}:{config.PORT}, FRONTEND={config.FRONTEND_URL}")
    
    return config

# 默认配置实例
current_config = get_config() 