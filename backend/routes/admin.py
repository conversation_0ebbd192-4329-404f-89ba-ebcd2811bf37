from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.content_models import Module, Lesson, Exercise, TestCase
from models.learning_models import User, UserProgress, Submission, UserRole, ProgressStatus, HintUsage
from extensions import db
from typing import Tuple, Dict, List
from functools import wraps
import json
from sqlalchemy import func, desc
from datetime import datetime, timedelta

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user or not user.is_admin():
            return {'error': '需要管理员权限'}, 403
        
        return f(*args, **kwargs)
    return decorated_function

# =============================================================================
# 仪表板和统计
# =============================================================================

@admin_bp.route('/dashboard', methods=['GET'])
@jwt_required()
@admin_required
def get_admin_dashboard() -> Tuple[Dict, int]:
    """获取管理员仪表板数据"""
    try:
        # 用户统计
        total_users = User.query.filter_by(role=UserRole.STUDENT).count()
        active_users = User.query.filter(
            User.role == UserRole.STUDENT,
            User.created_at >= datetime.utcnow() - timedelta(days=30)
        ).count()
        
        # 内容统计
        total_modules = Module.query.count()
        total_lessons = Lesson.query.count()
        total_exercises = Exercise.query.count()
        
        # 学习活动统计
        total_submissions = Submission.query.count()
        correct_submissions = Submission.query.filter_by(is_correct=True).count()
        success_rate = (correct_submissions / total_submissions * 100) if total_submissions > 0 else 0
        
        # 最近活动
        recent_users = User.query.filter_by(role=UserRole.STUDENT).order_by(desc(User.created_at)).limit(5).all()
        recent_submissions = Submission.query.order_by(desc(Submission.timestamp)).limit(10).all()
        
        return {
            'users': {
                'total': total_users,
                'active_monthly': active_users,
                'recent': [user.to_dict() for user in recent_users]
            },
            'content': {
                'modules': total_modules,
                'lessons': total_lessons,
                'exercises': total_exercises
            },
            'activity': {
                'total_submissions': total_submissions,
                'success_rate': round(success_rate, 2),
                'recent_submissions': [
                    {
                        'user_id': sub.user_id,
                        'exercise_id': sub.exercise_id,
                        'is_correct': sub.is_correct,
                        'timestamp': sub.timestamp.isoformat()
                    } for sub in recent_submissions
                ]
            }
        }, 200
        
    except Exception as e:
        return {'error': '获取仪表板数据失败'}, 500

# =============================================================================
# 课程管理
# =============================================================================

@admin_bp.route('/modules', methods=['GET'])
@jwt_required()
@admin_required
def get_admin_modules() -> Tuple[Dict, int]:
    """获取所有模块（管理员视图）"""
    try:
        modules = Module.query.order_by(Module.order_index).all()
        modules_data = []
        
        for module in modules:
            module_dict = module.to_dict()
            # 添加学习统计
            lesson_ids = [lesson.lesson_id for lesson in module.lessons]
            if lesson_ids:
                total_progress = UserProgress.query.filter(UserProgress.lesson_id.in_(lesson_ids)).count()
                completed_progress = UserProgress.query.filter(
                    UserProgress.lesson_id.in_(lesson_ids),
                    UserProgress.status == ProgressStatus.COMPLETED
                ).count()
                module_dict['learning_stats'] = {
                    'total_enrollments': total_progress,
                    'completions': completed_progress,
                    'completion_rate': round(completed_progress / total_progress * 100, 2) if total_progress > 0 else 0
                }
            else:
                module_dict['learning_stats'] = {'total_enrollments': 0, 'completions': 0, 'completion_rate': 0}
            
            modules_data.append(module_dict)
        
        return {'modules': modules_data}, 200
        
    except Exception as e:
        return {'error': '获取模块失败'}, 500

@admin_bp.route('/modules', methods=['POST'])
@jwt_required()
@admin_required
def create_module() -> Tuple[Dict, int]:
    """创建新模块"""
    try:
        data = request.get_json()
        
        if not data or not data.get('title'):
            return {'error': '模块标题是必填项'}, 400
        
        # 获取下一个排序索引
        max_order = db.session.query(func.max(Module.order_index)).scalar() or 0
        
        module = Module(
            title=data['title'],
            description=data.get('description', ''),
            order_index=max_order + 1
        )
        
        db.session.add(module)
        db.session.commit()
        
        return {'message': '模块创建成功', 'module': module.to_dict()}, 201
        
    except Exception as e:
        db.session.rollback()
        return {'error': '创建模块失败'}, 500

@admin_bp.route('/modules/<int:module_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_module(module_id: int) -> Tuple[Dict, int]:
    """更新模块信息"""
    try:
        module = Module.query.get(module_id)
        if not module:
            return {'error': '模块不存在'}, 404
        
        data = request.get_json()
        
        if data.get('title'):
            module.title = data['title']
        if data.get('description') is not None:
            module.description = data['description']
        if data.get('order_index') is not None:
            module.order_index = data['order_index']
        
        db.session.commit()
        
        return {'message': '模块更新成功', 'module': module.to_dict()}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '更新模块失败'}, 500

@admin_bp.route('/modules/<int:module_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_module(module_id: int) -> Tuple[Dict, int]:
    """删除模块"""
    try:
        module = Module.query.get(module_id)
        if not module:
            return {'error': '模块不存在'}, 404
        
        # 检查是否有课程
        if module.lessons:
            return {'error': '模块下还有课程，无法删除'}, 400
        
        db.session.delete(module)
        db.session.commit()
        
        return {'message': '模块删除成功'}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '删除模块失败'}, 500

# =============================================================================
# 课程管理
# =============================================================================

@admin_bp.route('/lessons', methods=['POST'])
@jwt_required()
@admin_required
def create_lesson() -> Tuple[Dict, int]:
    """创建新课程"""
    try:
        data = request.get_json()
        
        required_fields = ['module_id', 'title', 'content_md']
        for field in required_fields:
            if not data or not data.get(field):
                return {'error': f'{field} 是必填项'}, 400
        
        # 验证模块存在
        module = Module.query.get(data['module_id'])
        if not module:
            return {'error': '模块不存在'}, 404
        
        # 获取模块内下一个排序索引
        max_order = db.session.query(func.max(Lesson.order_index)).filter_by(module_id=data['module_id']).scalar() or 0
        
        lesson = Lesson(
            module_id=data['module_id'],
            title=data['title'],
            content_md=data['content_md'],
            prerequisite_lesson_id=data.get('prerequisite_lesson_id'),
            order_index=max_order + 1
        )
        
        db.session.add(lesson)
        db.session.commit()
        
        return {'message': '课程创建成功', 'lesson': lesson.to_dict()}, 201
        
    except Exception as e:
        db.session.rollback()
        return {'error': '创建课程失败'}, 500

@admin_bp.route('/lessons/<int:lesson_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_lesson(lesson_id: int) -> Tuple[Dict, int]:
    """更新课程信息"""
    try:
        lesson = Lesson.query.get(lesson_id)
        if not lesson:
            return {'error': '课程不存在'}, 404
        
        data = request.get_json()
        
        if data.get('title'):
            lesson.title = data['title']
        if data.get('content_md') is not None:
            lesson.content_md = data['content_md']
        if data.get('prerequisite_lesson_id') is not None:
            lesson.prerequisite_lesson_id = data['prerequisite_lesson_id']
        if data.get('order_index') is not None:
            lesson.order_index = data['order_index']
        
        db.session.commit()
        
        return {'message': '课程更新成功', 'lesson': lesson.to_dict()}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '更新课程失败'}, 500

@admin_bp.route('/lessons/<int:lesson_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_lesson(lesson_id: int) -> Tuple[Dict, int]:
    """删除课程"""
    try:
        lesson = Lesson.query.get(lesson_id)
        if not lesson:
            return {'error': '课程不存在'}, 404
        
        # 检查是否有练习
        if lesson.exercises:
            return {'error': '课程下还有练习，无法删除'}, 400
        
        # 检查是否被其他课程作为前置课程
        dependent_lessons = Lesson.query.filter_by(prerequisite_lesson_id=lesson_id).all()
        if dependent_lessons:
            return {'error': '该课程被其他课程设为前置课程，无法删除'}, 400
        
        db.session.delete(lesson)
        db.session.commit()
        
        return {'message': '课程删除成功'}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '删除课程失败'}, 500

# =============================================================================
# 练习管理
# =============================================================================

@admin_bp.route('/exercises', methods=['POST'])
@jwt_required()
@admin_required
def create_exercise() -> Tuple[Dict, int]:
    """创建新练习"""
    try:
        data = request.get_json()
        
        required_fields = ['lesson_id', 'problem_statement']
        for field in required_fields:
            if not data or not data.get(field):
                return {'error': f'{field} 是必填项'}, 400
        
        # 验证课程存在
        lesson = Lesson.query.get(data['lesson_id'])
        if not lesson:
            return {'error': '课程不存在'}, 404
        
        exercise = Exercise(
            lesson_id=data['lesson_id'],
            problem_statement=data['problem_statement'],
            hints=json.dumps(data.get('hints', []), ensure_ascii=False)
        )
        
        db.session.add(exercise)
        db.session.flush()  # 获取exercise_id
        
        # 添加测试用例
        test_cases = data.get('test_cases', [])
        for tc_data in test_cases:
            test_case = TestCase(
                exercise_id=exercise.exercise_id,
                input_data=tc_data.get('input_data', ''),
                expected_output=tc_data.get('expected_output', ''),
                is_hidden=tc_data.get('is_hidden', False)
            )
            db.session.add(test_case)
        
        db.session.commit()
        
        return {'message': '练习创建成功', 'exercise': exercise.to_dict()}, 201
        
    except Exception as e:
        db.session.rollback()
        return {'error': '创建练习失败'}, 500

@admin_bp.route('/exercises/<int:exercise_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_exercise(exercise_id: int) -> Tuple[Dict, int]:
    """更新练习"""
    try:
        exercise = Exercise.query.get(exercise_id)
        if not exercise:
            return {'error': '练习不存在'}, 404
        
        data = request.get_json()
        
        if data.get('problem_statement'):
            exercise.problem_statement = data['problem_statement']
        if data.get('hints') is not None:
            exercise.hints = json.dumps(data['hints'], ensure_ascii=False)
        
        # 更新测试用例
        if 'test_cases' in data:
            # 删除现有测试用例
            TestCase.query.filter_by(exercise_id=exercise_id).delete()
            
            # 添加新测试用例
            for tc_data in data['test_cases']:
                test_case = TestCase(
                    exercise_id=exercise_id,
                    input_data=tc_data.get('input_data', ''),
                    expected_output=tc_data.get('expected_output', ''),
                    is_hidden=tc_data.get('is_hidden', False)
                )
                db.session.add(test_case)
        
        db.session.commit()
        
        return {'message': '练习更新成功', 'exercise': exercise.to_dict()}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '更新练习失败'}, 500

@admin_bp.route('/exercises/<int:exercise_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_exercise(exercise_id: int) -> Tuple[Dict, int]:
    """删除练习"""
    try:
        exercise = Exercise.query.get(exercise_id)
        if not exercise:
            return {'error': '练习不存在'}, 404
        
        # 删除相关的提交记录和测试用例
        Submission.query.filter_by(exercise_id=exercise_id).delete()
        TestCase.query.filter_by(exercise_id=exercise_id).delete()
        
        db.session.delete(exercise)
        db.session.commit()
        
        return {'message': '练习删除成功'}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '删除练习失败'}, 500

# =============================================================================
# 用户管理
# =============================================================================

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
@admin_required
def get_users() -> Tuple[Dict, int]:
    """获取所有用户"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        pagination = User.query.filter_by(role=UserRole.STUDENT).order_by(desc(User.created_at)).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users_data = []
        for user in pagination.items:
            user_dict = user.to_dict()
            
            # 添加学习进度统计
            total_lessons = Lesson.query.count()
            completed_lessons = UserProgress.query.filter_by(
                user_id=user.user_id,
                status=ProgressStatus.COMPLETED
            ).count()
            
            user_dict['learning_stats'] = {
                'completed_lessons': completed_lessons,
                'total_lessons': total_lessons,
                'completion_rate': round(completed_lessons / total_lessons * 100, 2) if total_lessons > 0 else 0
            }
            
            users_data.append(user_dict)
        
        return {
            'users': users_data,
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total
            }
        }, 200
        
    except Exception as e:
        return {'error': '获取用户列表失败'}, 500

@admin_bp.route('/users/<int:user_id>/reset-progress', methods=['POST'])
@jwt_required()
@admin_required
def reset_user_progress(user_id: int) -> Tuple[Dict, int]:
    """重置用户学习进度（管理员功能）"""
    try:
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return {'error': '用户不存在'}, 404
        
        if user.role != UserRole.STUDENT:
            return {'error': '只能重置学生用户的进度'}, 400
        
        # 获取用户所有进度记录
        progress_records = UserProgress.query.filter_by(user_id=user_id).all()
        
        if not progress_records:
            return {'message': '用户暂无学习进度'}, 200
        
        # 重置所有进度记录
        reset_count = 0
        for progress in progress_records:
            progress.status = ProgressStatus.NOT_STARTED
            progress.attempts = 0
            progress.avg_time_sec = None
            progress.first_attempt_at = None
            progress.completed_at = None
            reset_count += 1
        
        # 删除用户的所有代码提交记录（可选）
        submission_count = Submission.query.filter_by(user_id=user_id).count()
        Submission.query.filter_by(user_id=user_id).delete()
        
        # 删除用户的提示使用记录
        HintUsage.query.filter_by(user_id=user_id).delete()
        
        db.session.commit()
        
        return {
            'message': f'成功重置用户进度',
            'details': {
                'reset_lessons': reset_count,
                'deleted_submissions': submission_count,
                'username': user.username
            }
        }, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '重置用户进度失败'}, 500

@admin_bp.route('/lessons', methods=['GET'])
@jwt_required()
@admin_required
def get_admin_lessons() -> Tuple[Dict, int]:
    """获取所有课程（管理员视图）"""
    try:
        module_id = request.args.get('module_id', type=int)
        
        query = Lesson.query
        if module_id:
            query = query.filter_by(module_id=module_id)
        
        lessons = query.order_by(Lesson.module_id, Lesson.order_index).all()
        lessons_data = []
        
        for lesson in lessons:
            lesson_dict = lesson.to_dict()
            # 添加练习数量
            lesson_dict['exercise_count'] = len(lesson.exercises)
            # 添加学习统计
            total_progress = UserProgress.query.filter_by(lesson_id=lesson.lesson_id).count()
            completed_progress = UserProgress.query.filter_by(
                lesson_id=lesson.lesson_id,
                status=ProgressStatus.COMPLETED
            ).count()
            lesson_dict['learning_stats'] = {
                'enrollments': total_progress,
                'completions': completed_progress
            }
            lessons_data.append(lesson_dict)
        
        return {'lessons': lessons_data}, 200
        
    except Exception as e:
        return {'error': '获取课程失败'}, 500

@admin_bp.route('/exercises', methods=['GET'])
@jwt_required()
@admin_required
def get_admin_exercises() -> Tuple[Dict, int]:
    """获取所有练习（管理员视图）"""
    try:
        lesson_id = request.args.get('lesson_id', type=int)
        
        query = Exercise.query
        if lesson_id:
            query = query.filter_by(lesson_id=lesson_id)
        
        exercises = query.all()
        exercises_data = []
        
        for exercise in exercises:
            exercise_dict = exercise.to_dict()
            # 添加测试用例数量
            exercise_dict['test_case_count'] = len(exercise.test_cases)
            # 添加提交统计
            total_submissions = Submission.query.filter_by(exercise_id=exercise.exercise_id).count()
            correct_submissions = Submission.query.filter_by(
                exercise_id=exercise.exercise_id,
                is_correct=True
            ).count()
            exercise_dict['submission_stats'] = {
                'total': total_submissions,
                'correct': correct_submissions,
                'success_rate': round(correct_submissions / total_submissions * 100, 2) if total_submissions > 0 else 0
            }
            exercises_data.append(exercise_dict)
        
        return {'exercises': exercises_data}, 200
        
    except Exception as e:
        return {'error': '获取练习失败'}, 500

@admin_bp.route('/analytics', methods=['GET'])
@jwt_required()
@admin_required
def get_analytics() -> Tuple[Dict, int]:
    """获取学习分析数据"""
    try:
        # 基础统计
        total_users = User.query.filter_by(role=UserRole.STUDENT).count()
        total_lessons = Lesson.query.count()
        total_exercises = Exercise.query.count()
        total_submissions = Submission.query.count()
        
        # 完成率统计
        completed_lessons = UserProgress.query.filter_by(status=ProgressStatus.COMPLETED).count()
        correct_submissions = Submission.query.filter_by(is_correct=True).count()
        
        # 错误类型分析
        error_stats = db.session.query(
            Submission.error_type,
            func.count(Submission.submission_id).label('count')
        ).filter(
            Submission.is_correct == False,
            Submission.error_type.isnot(None)
        ).group_by(Submission.error_type).all()
        
        # 最活跃用户
        active_users = db.session.query(
            User.user_id,
            User.username,
            func.count(Submission.submission_id).label('submission_count')
        ).join(Submission).filter(
            User.role == UserRole.STUDENT
        ).group_by(User.user_id).order_by(
            desc('submission_count')
        ).limit(10).all()
        
        # 最近7天的活动趋势
        recent_activity = db.session.query(
            func.date(Submission.timestamp).label('date'),
            func.count(Submission.submission_id).label('submissions')
        ).filter(
            Submission.timestamp >= datetime.utcnow() - timedelta(days=7)
        ).group_by(func.date(Submission.timestamp)).all()
        
        return {
            'overview': {
                'total_users': total_users,
                'total_lessons': total_lessons,
                'total_exercises': total_exercises,
                'total_submissions': total_submissions,
                'lesson_completion_rate': round(completed_lessons / (total_users * total_lessons) * 100, 2) if total_users > 0 and total_lessons > 0 else 0,
                'exercise_success_rate': round(correct_submissions / total_submissions * 100, 2) if total_submissions > 0 else 0
            },
            'error_analysis': [
                {
                    'error_type': error.error_type.value if hasattr(error.error_type, 'value') else str(error.error_type),
                    'count': error.count
                } for error in error_stats
            ],
            'active_users': [
                {
                    'user_id': user.user_id,
                    'username': user.username,
                    'submission_count': user.submission_count
                } for user in active_users
            ],
            'activity_trend': [
                {
                    'date': activity.date.isoformat() if hasattr(activity.date, 'isoformat') else str(activity.date),
                    'submissions': activity.submissions
                } for activity in recent_activity
            ]
        }, 200
        
    except Exception as e:
        return {'error': '获取分析数据失败'}, 500

# ================ 测试用例管理 API ================

@admin_bp.route('/exercises/<int:exercise_id>/test-cases', methods=['GET'])
@jwt_required()
@admin_required
def get_test_cases(exercise_id: int) -> Tuple[Dict, int]:
    """获取练习的测试用例列表"""
    try:
        exercise = Exercise.query.get_or_404(exercise_id)
        test_cases = TestCase.query.filter_by(exercise_id=exercise_id).all()
        
        return {
            'test_cases': [tc.to_dict() for tc in test_cases]
        }, 200
        
    except Exception as e:
        return {'error': '获取测试用例失败'}, 500

@admin_bp.route('/exercises/<int:exercise_id>/test-cases', methods=['POST'])
@jwt_required()
@admin_required
def create_test_case(exercise_id: int) -> Tuple[Dict, int]:
    """创建测试用例"""
    try:
        data = request.get_json()
        
        # 验证练习是否存在
        exercise = Exercise.query.get_or_404(exercise_id)
        
        # 创建测试用例
        test_case = TestCase(
            exercise_id=exercise_id,
            input_data=data.get('input_data', ''),
            expected_output=data['expected_output'],
            is_hidden=data.get('is_hidden', False)
        )
        
        db.session.add(test_case)
        db.session.commit()
        
        return {
            'message': '测试用例创建成功',
            'test_case': test_case.to_dict()
        }, 201
        
    except Exception as e:
        db.session.rollback()
        return {'error': '创建测试用例失败'}, 500

@admin_bp.route('/exercises/<int:exercise_id>/test-cases/<int:test_case_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_test_case(exercise_id: int, test_case_id: int) -> Tuple[Dict, int]:
    """更新测试用例"""
    try:
        data = request.get_json()
        
        # 验证测试用例是否存在且属于指定练习
        test_case = TestCase.query.filter_by(
            test_case_id=test_case_id,
            exercise_id=exercise_id
        ).first_or_404()
        
        # 更新字段
        if 'input_data' in data:
            test_case.input_data = data['input_data']
        if 'expected_output' in data:
            test_case.expected_output = data['expected_output']
        if 'is_hidden' in data:
            test_case.is_hidden = data['is_hidden']
        
        db.session.commit()
        
        return {
            'message': '测试用例更新成功',
            'test_case': test_case.to_dict()
        }, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '更新测试用例失败'}, 500

@admin_bp.route('/exercises/<int:exercise_id>/test-cases/<int:test_case_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_test_case(exercise_id: int, test_case_id: int) -> Tuple[Dict, int]:
    """删除测试用例"""
    try:
        # 验证测试用例是否存在且属于指定练习
        test_case = TestCase.query.filter_by(
            test_case_id=test_case_id,
            exercise_id=exercise_id
        ).first_or_404()
        
        db.session.delete(test_case)
        db.session.commit()
        
        return {'message': '测试用例删除成功'}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '删除测试用例失败'}, 500