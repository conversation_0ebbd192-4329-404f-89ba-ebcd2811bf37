from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.content_models import Exercise
from models.learning_models import Submission, UserProgress, ErrorType, HintUsage, FeedbackInstance, FeedbackEffectiveness, FeedbackType
from utils.messages import Message<PERSON>anager
from typing import Tuple, Dict, Any, Optional
import json
from datetime import datetime, timedelta, timezone
from extensions import db
from sqlalchemy.orm import joinedload

submissions_bp = Blueprint('submissions', __name__)

def get_db():
    return db

@submissions_bp.route('/submit', methods=['POST'])
@jwt_required()
def submit_code() -> Tuple[Dict, int]:
    """提交代码进行评估 - Interaction Model核心功能"""
    user_id = int(get_jwt_identity())
    data = request.get_json()
    
    # 获取用户语言偏好
    language = request.headers.get('Accept-Language', 'en')
    if language not in ['zh', 'en']:
        language = 'en'  # 默认英文
    
    msg_manager = MessageManager(language)
    
    if not data or not data.get('exercise_id') or not data.get('code'):
        return {'error': msg_manager.get_message('missing_required_fields')}, 400
    
    try:
        exercise_id = data['exercise_id']
        code = data['code']
        solving_time_sec = data.get('solving_time_sec')  # 前端传来的做题耗时（秒）
        
        exercise = Exercise.query.options(joinedload(Exercise.test_cases)).get(exercise_id)
        if not exercise:
            return {'error': msg_manager.get_message('exercise_not_found')}, 404
        
        from utils.code_executor import CodeExecutor
        from utils.code_analyzer import CodeAnalyzer
        
        executor = CodeExecutor(language=language)
        result = executor.execute_code(code, exercise.test_cases)
        is_correct = result.get('is_correct', False)
        
        # === 逻辑块 A (修正逻辑) ===
        # 为待处理的反馈记录增加尝试次数（包括成功尝试）
        pending_records = FeedbackEffectiveness.query.filter_by(
            user_id=user_id,
            exercise_id=exercise_id,
            did_succeed=False
        ).all()
        for record in pending_records:
            record.subsequent_attempts += 1  # 无论成功与否都计数
        # ==============================
        
        # 传递语言参数给分析器
        analyzer = CodeAnalyzer(language=language)
        # 初始分析只调用OpenAI，更快返回
        analysis_result = analyzer.analyze_code(code, exercise.problem_statement, analysis_mode='initial')
        
        error_type_str = result.get('error_type', ErrorType.NO_ERROR.value)
        try:
            error_type_enum = ErrorType(error_type_str) if isinstance(error_type_str, str) else error_type_str
        except ValueError:
            error_type_enum = ErrorType.RUNTIME_ERROR
            
        local_analysis = analysis_result.get('local_analysis', {})
        summary = analysis_result.get('summary', {})
        
        submission = Submission(
            user_id=user_id,
            exercise_id=exercise_id,
            code=code,
            is_correct=is_correct,
            error_type=error_type_enum,
            error_message=result.get('error_message'),
            output=result.get('output'),
            exec_time_sec=result.get('exec_time'),
            code_lines=summary.get('lines_of_code', len(code.splitlines())),
            code_complexity=local_analysis.get('complexity', 0),
            syntax_score=summary.get('syntax_score', 5.0),
            api_analysis_result=json.dumps(analysis_result, ensure_ascii=False)
        )
        
        db.session.add(submission)
        db.session.flush()
        
        # 获取或创建进度记录
        progress = UserProgress.query.filter_by(user_id=user_id, lesson_id=exercise.lesson_id).first()
        if not progress:
            progress = UserProgress(user_id=user_id, lesson_id=exercise.lesson_id)
            db.session.add(progress)
            db.session.flush()
        
        # 更新进度
        progress.increment_attempts()
        
        # 如果前端传来了做题时间，更新平均时间
        if solving_time_sec is not None:
            print(f"DEBUG: 接收到做题时间: {solving_time_sec}秒, 用户ID: {user_id}, 课程ID: {exercise.lesson_id}")
            progress.update_avg_time(solving_time_sec)
            print(f"DEBUG: 更新后的平均时间: {progress.avg_time_sec}秒")
        
        if is_correct:
            progress.complete_lesson()

        comprehensive_feedback = analyzer.generate_comprehensive_feedback(result, analysis_result, progress)
        
        # 保存初始反馈
        _save_feedback_instances(submission.submission_id, comprehensive_feedback, user_id, exercise_id, is_correct, error_type_enum)

        # === 逻辑块 B (成功处理提交，实现反馈闭环) ===
        if is_correct:
            records_to_close = FeedbackEffectiveness.query.filter_by(
                user_id=user_id,
                exercise_id=exercise_id,
                did_succeed=False
            ).all()

            for record in records_to_close:
                record.did_succeed = True
                record.success_achieved_at = datetime.now(timezone.utc)
                # 使用新的会话时间算法计算 time_to_success_sec
                record.time_to_success_sec = _calculate_session_time(record, submission)
        # =======================================
        
        db.session.commit() # 提交所有数据库更改
        
        # 重新从数据库加载，确保ID可用
        db.session.refresh(submission)
        
        # 准备返回给前端的数据
        final_result = _prepare_response_data(submission, result, comprehensive_feedback, progress)
        
        return {
            'submission': submission.to_dict(),
            'result': final_result
        }, 200
        
    except Exception as e:
        db.session.rollback()
        from flask import current_app
        current_app.logger.error("Error in submit_code: %s", str(e), exc_info=True)
        return {'error': msg_manager.get_message('submission_failed')}, 500

def _calculate_session_time(effectiveness_record: FeedbackEffectiveness, final_submission: Submission) -> float:
    """计算从收到反馈到成功解决问题的活跃学习时间"""
    SESSION_TIMEOUT_MINUTES = 30  # 会话超时阈值
    
    # 获取从收到反馈到成功的所有提交记录（包括最终成功提交）
    submissions = Submission.query.filter(
        Submission.user_id == effectiveness_record.user_id,
        Submission.exercise_id == effectiveness_record.exercise_id,
        Submission.timestamp > effectiveness_record.created_at,
        Submission.timestamp <= final_submission.timestamp  # 包含成功提交
    ).order_by(Submission.timestamp.asc()).all()
    
    # 计算活跃会话时间：反馈时间 → 第1次提交 → 第2次提交 → ... → 成功提交
    total_session_time = 0
    last_timestamp = effectiveness_record.created_at

    for sub in submissions:
        time_diff = (sub.timestamp - last_timestamp).total_seconds()
        # 如果时间间隔超过阈值，则只计入阈值时间（排除长时间休息）
        session_time = min(time_diff, SESSION_TIMEOUT_MINUTES * 60)
        total_session_time += session_time
        last_timestamp = sub.timestamp
        
    return total_session_time

@submissions_bp.route('/<int:submission_id>/request_additional_feedback', methods=['POST'])
@jwt_required()
def request_additional_feedback(submission_id: int) -> Tuple[Dict, int]:
    """按需请求额外的AI反馈（如DeepSeek）"""
    user_id = int(get_jwt_identity())
    data = request.get_json()
    model_to_run = data.get('model', 'deepseek') # 默认请求deepseek

    try:
        # 获取用户语言偏好
        language = request.headers.get('Accept-Language', 'en')
        if language not in ['zh', 'en']:
            language = 'en'  # 默认英文
            
        msg_manager = MessageManager(language)
            
        submission = Submission.query.filter_by(user_id=user_id, submission_id=submission_id).first()
        if not submission:
            return {'error': msg_manager.get_message('submission_not_found')}, 404

        exercise = Exercise.query.get(submission.exercise_id)
        if not exercise:
            return {'error': msg_manager.get_message('exercise_not_found_for_submission')}, 404

        from utils.code_analyzer import CodeAnalyzer
        # 传递语言参数给分析器
        analyzer = CodeAnalyzer(language=language)

        # 按需调用单个模型
        additional_analysis = analyzer.analyze_single_model(model_to_run, submission.code, exercise.problem_statement)
        
        if 'error' in additional_analysis.get(model_to_run, {}):
             return {'error': msg_manager.get_message('analysis_failed', model=model_to_run)}, 500

        # 保存新的反馈实例
        feedback_data = additional_analysis[model_to_run]
        feedback_type = FeedbackType.DEEPSEEK_EDUCATIONAL if model_to_run == 'deepseek' else FeedbackType.OPENAI_EDUCATIONAL

        instance = FeedbackInstance(
            submission_id=submission_id,
            feedback_type=feedback_type,
            feedback_content=json.dumps(feedback_data.get('feedback', {}), ensure_ascii=False),
            generation_time_ms=feedback_data.get('generation_time_ms', 0),
            api_cost=feedback_data.get('estimated_cost', 0.0)
        )
        db.session.add(instance)
        db.session.flush()
        
        # 创建效果追踪记录
        if not submission.is_correct:
            effectiveness_record = FeedbackEffectiveness(
                user_id=user_id,
                exercise_id=submission.exercise_id,
                feedback_id=instance.feedback_id,
                triggering_error_type=submission.error_type
            )
            db.session.add(effectiveness_record)

        db.session.commit()
        
        # 准备返回给前端的数据
        response_data = {
            'feedback_id': instance.feedback_id,
            'feedback_type': instance.feedback_type.value,
            'feedback_content': instance.to_dict()
        }
        
        return jsonify(response_data), 200

    except Exception as e:
        db.session.rollback()
        from flask import current_app
        current_app.logger.error(f"Error in request_additional_feedback for submission {submission_id}: {str(e)}", exc_info=True)
        return {'error': msg_manager.get_message('additional_feedback_failed')}, 500

def _save_feedback_instances(submission_id: int, comprehensive_feedback: Dict, user_id: int, exercise_id: int, is_correct: bool, error_type: Optional[ErrorType]):
    """辅助函数：保存反馈实例到数据库"""
    feedback_instances = []
    
    # 保存结构化反馈
    structural_feedback = comprehensive_feedback['comprehensive_feedback']['structural']
    structural_instance = FeedbackInstance(
        submission_id=submission_id,
        feedback_type=FeedbackType.JUDGE0_STRUCTURAL,
        feedback_content=json.dumps(structural_feedback, ensure_ascii=False),
        generation_time_ms=0,
        api_cost=0.0
    )
    db.session.add(structural_instance)
    feedback_instances.append(structural_instance)
    
    # 保存AI反馈
    ai_feedbacks = comprehensive_feedback['comprehensive_feedback']['ai_generated']
    for api_name, feedback_data in ai_feedbacks.items():
        if 'error' in feedback_data: continue
        
        if api_name == 'openai':
            feedback_type = FeedbackType.OPENAI_EDUCATIONAL
        elif api_name == 'deepseek':
            feedback_type = FeedbackType.DEEPSEEK_EDUCATIONAL
        elif api_name == 'local_rules':
            feedback_type = FeedbackType.LOCAL_RULES
        else:
            continue
            
        ai_instance = FeedbackInstance(
            submission_id=submission_id,
            feedback_type=feedback_type,
            feedback_content=json.dumps(feedback_data.get('feedback', {}), ensure_ascii=False),
            generation_time_ms=feedback_data.get('generation_time', 0),
            api_cost=feedback_data.get('cost', 0.0)
        )
        db.session.add(ai_instance)
        feedback_instances.append(ai_instance)
    
    db.session.flush()
    
    # 创建效果追踪记录
    if not is_correct:
        for instance in feedback_instances:
            effectiveness_record = FeedbackEffectiveness(
                user_id=user_id,
                exercise_id=exercise_id,
                feedback_id=instance.feedback_id,
                triggering_error_type=error_type
            )
            db.session.add(effectiveness_record)

def _prepare_response_data(submission: Submission, result: Dict, comprehensive_feedback: Dict, progress: UserProgress) -> Dict:
    """辅助函数：准备返回给前端的最终结果字典"""
    # 重新从数据库加载反馈实例以获取ID
    feedback_instances = FeedbackInstance.query.filter_by(submission_id=submission.submission_id).all()
    feedback_id_map = {instance.feedback_type.name: instance.feedback_id for instance in feedback_instances}

    # 将feedback_id添加到返回的数据中
    comprehensive_feedback['comprehensive_feedback']['structural']['feedback_id'] = feedback_id_map.get(FeedbackType.JUDGE0_STRUCTURAL.name)
    for api_name, feedback_data in comprehensive_feedback['comprehensive_feedback']['ai_generated'].items():
        if api_name == 'openai':
            feedback_data['feedback_id'] = feedback_id_map.get(FeedbackType.OPENAI_EDUCATIONAL.name)
        elif api_name == 'deepseek':
            feedback_data['feedback_id'] = feedback_id_map.get(FeedbackType.DEEPSEEK_EDUCATIONAL.name)

    # 添加提示可用性标志
    result['hints_available'] = progress.should_show_hint() if progress and not result.get('is_correct', False) else False
    
    # 添加兼容性字段
    result['feedback'] = generate_legacy_feedback(comprehensive_feedback)
    result['comprehensive_feedback'] = comprehensive_feedback
    
    return result


def generate_legacy_feedback(comprehensive_feedback: Dict[str, Any]) -> str:
    """生成与现有前端兼容的反馈字符串 - 简化版本"""
    
    # 获取推荐的主要反馈
    recommended = comprehensive_feedback['comprehensive_feedback']['recommended_primary']
    
    if recommended == 'structural':
        structural = comprehensive_feedback['comprehensive_feedback']['structural']
        # 🔧 修复：简单模式只显示主要消息，避免与详细模式重复
        return structural.get('message', '代码需要修改，请查看详细分析')
    
    # 获取AI反馈
    ai_feedbacks = comprehensive_feedback['comprehensive_feedback']['ai_generated']
    
    if recommended.startswith('openai') and 'openai' in ai_feedbacks:
        openai_feedback = ai_feedbacks['openai']['feedback']
        # 优先显示鼓励性内容
        encouragement = openai_feedback.get('encouragement', '')
        if encouragement:
            return encouragement
        return (openai_feedback.get('problem_identification', '') + ' ' +
                openai_feedback.get('guiding_hints', '')).strip()
    
    elif recommended.startswith('deepseek') and 'deepseek' in ai_feedbacks:
        deepseek_feedback = ai_feedbacks['deepseek']['feedback']
        # 优先显示鼓励性内容
        encouragement = deepseek_feedback.get('encouragement', '')
        if encouragement:
            return encouragement
        return (deepseek_feedback.get('problem_explanation', '') + ' ' +
               deepseek_feedback.get('hints_not_answers', '')).strip()
    
    # 回退到结构化反馈的主要消息
    structural = comprehensive_feedback['comprehensive_feedback']['structural']
    return structural.get('message', '代码需要修改，点击查看详细分析')


@submissions_bp.route('/exercises/<int:exercise_id>/hint', methods=['POST'])
@jwt_required()
def request_hint(exercise_id: int) -> Tuple[Dict, int]:
    """请求练习提示 - Interaction Model脚手架功能"""
    user_id = int(get_jwt_identity())
    data = request.get_json() or {}
    hint_level = data.get('hint_level', 1)
    
    # 获取用户语言偏好
    language = request.headers.get('Accept-Language', 'en')
    if language not in ['zh', 'en']:
        language = 'en'  # 默认英文
    
    msg_manager = MessageManager(language)
    
    try:
        exercise = Exercise.query.get(exercise_id)
        if not exercise:
            return {'error': msg_manager.get_message('exercise_not_found')}, 404
        
        # 检查用户是否有权限获取提示
        progress = UserProgress.query.filter_by(
            user_id=user_id,
            lesson_id=exercise.lesson_id
        ).first()
        
        if not progress or not progress.should_show_hint():
            return {'error': msg_manager.get_message('hint_not_available')}, 403
        
        # 获取提示数组
        hints = exercise.get_hints()
        if not hints:
            return {'error': msg_manager.get_message('no_hints_available')}, 404
        
        # 获取指定级别的提示（从1开始，转换为索引）
        hint_index = hint_level - 1
        if hint_index >= len(hints):
            return {'error': msg_manager.get_message('no_more_hints')}, 404
        
        # 记录提示使用
        hint_usage = HintUsage(
            user_id=user_id,
            exercise_id=exercise_id,
            hint_level=hint_level
        )
        db.session.add(hint_usage)
        db.session.commit()
        
        return {
            'hint': hints[hint_index],
            'hint_level': hint_level,
            'total_hints': len(hints)
        }, 200
        
    except Exception:
        db.session.rollback()
        return {'error': msg_manager.get_message('get_hint_failed')}, 500

@submissions_bp.route('/exercises/<int:exercise_id>/history', methods=['GET'])
@jwt_required()
def get_submission_history(exercise_id: int) -> Tuple[Dict, int]:
    """获取提交历史 - Learner Model历史记录"""
    user_id = int(get_jwt_identity())
    
    # 获取用户语言偏好
    language = request.headers.get('Accept-Language', 'en')
    if language not in ['zh', 'en']:
        language = 'en'  # 默认英文
    
    msg_manager = MessageManager(language)
    
    try:
        submissions = Submission.query.filter_by(
            user_id=user_id,
            exercise_id=exercise_id
        ).order_by(Submission.timestamp.desc()).limit(10).all()
        
        return {
            'submissions': [submission.to_dict() for submission in submissions]
        }, 200
        
    except Exception:
        return {'error': msg_manager.get_message('get_history_failed')}, 500


@submissions_bp.route('/feedback-rating', methods=['POST'])
@jwt_required()
def rate_feedback() -> Tuple[Dict, int]:
    """用户为反馈质量评分 - 用于效果评估"""
    user_id = int(get_jwt_identity())
    data = request.get_json()
    
    # 获取用户语言偏好
    language = request.headers.get('Accept-Language', 'en')
    if language not in ['zh', 'en']:
        language = 'en'  # 默认英文
    
    msg_manager = MessageManager(language)
    
    try:
        feedback_id = data.get('feedback_id')
        helpfulness_rating = data.get('helpfulness_rating')  # 1-5
        clarity_rating = data.get('clarity_rating')  # 1-5
        
        if not feedback_id or not helpfulness_rating:
            return {'error': msg_manager.get_message('missing_rating_params')}, 400
        
        # 查找对应的效果记录
        effectiveness_record = FeedbackEffectiveness.query.filter_by(
            user_id=user_id,
            feedback_id=feedback_id
        ).first()
        
        if not effectiveness_record:
            return {'error': msg_manager.get_message('feedback_record_not_found')}, 404
        
        # 更新评分
        effectiveness_record.helpfulness_rating = helpfulness_rating
        effectiveness_record.clarity_rating = clarity_rating
        
        db.session.commit()
        
        return {
            'message': msg_manager.get_message('rating_submit_success'),
            'effectiveness_id': effectiveness_record.effectiveness_id
        }, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': msg_manager.get_message('rating_submit_failed', error=str(e))}, 500