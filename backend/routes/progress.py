from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.learning_models import UserProgress, Submission, HintUsage, ProgressStatus
from models.content_models import Lesson, Module
from extensions import db
from typing import Tuple, Dict, List
from sqlalchemy import func
from datetime import datetime, timedelta

progress_bp = Blueprint('progress', __name__)

@progress_bp.route('/dashboard', methods=['GET'])
@jwt_required()
def get_dashboard() -> Tuple[Dict, int]:
    """获取学习仪表板 - Learner Model核心功能"""
    user_id = int(get_jwt_identity())
    
    try:
        # 获取用户所有进度记录
        progress_records = UserProgress.query.filter_by(user_id=user_id).all()
        
        # 统计信息 - 修复：应该查询系统中的总课程数，而不是用户进度记录数
        total_lessons = Lesson.query.count()
        completed_lessons = sum(1 for p in progress_records if p.status == ProgressStatus.COMPLETED)
        in_progress_lessons = sum(1 for p in progress_records if p.status == ProgressStatus.IN_PROGRESS)
        
        # 计算完成率
        completion_rate = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
        
        # 获取最近学习的课程
        recent_lessons = []
        for progress in sorted(progress_records, key=lambda p: p.updated_at, reverse=True)[:5]:
            lesson = Lesson.query.get(progress.lesson_id)
            if lesson:
                recent_lessons.append({
                    'lesson_id': lesson.lesson_id,
                    'title': lesson.title,
                    'status': progress.status.value,
                    'last_updated': progress.updated_at.isoformat()
                })
        
        # 获取最近的代码提交活动
        recent_activity = []
        recent_submissions = Submission.query.filter_by(user_id=user_id).order_by(
            Submission.timestamp.desc()
        ).limit(10).all()
        
        for submission in recent_submissions:
            recent_activity.append({
                'submission_id': submission.submission_id,
                'exercise_id': submission.exercise_id,
                'is_correct': submission.is_correct,
                'timestamp': submission.timestamp.isoformat()
            })
            
        # 获取错误模式分析
        from models.learning_models import ErrorType
        error_counts = {}
        for submission in Submission.query.filter_by(user_id=user_id).all():
            error_type = submission.error_type.value if submission.error_type else 'no_error'
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        error_patterns = [
            {'error_type': error_type, 'count': count} 
            for error_type, count in error_counts.items()
        ]
        
        # 获取模块进度
        modules_progress = []
        modules = Module.query.all()
        for module in modules:
            module_lessons = Lesson.query.filter_by(module_id=module.module_id).all()
            module_completed = sum(1 for lesson in module_lessons 
                                 if any(p.lesson_id == lesson.lesson_id and p.status == ProgressStatus.COMPLETED 
                                       for p in progress_records))
            module_progress = (module_completed / len(module_lessons) * 100) if module_lessons else 0
            
            modules_progress.append({
                'module_id': module.module_id,
                'title': module.title,
                'completed_lessons': module_completed,
                'total_lessons': len(module_lessons),
                'progress_percentage': module_progress
            })
        
        return {
            'overview': {
                'total_lessons': total_lessons,
                'completed_lessons': completed_lessons,
                'in_progress_lessons': in_progress_lessons,
                'completion_rate': round(completion_rate, 2)
            },
            'recent_lessons': recent_lessons,
            'recent_activity': recent_activity,
            'error_patterns': error_patterns,
            'modules_progress': modules_progress
        }, 200
        
    except Exception as e:
        return {'error': '获取仪表板数据失败'}, 500

@progress_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_learning_stats() -> Tuple[Dict, int]:
    """获取学习统计 - Learner Model分析功能"""
    user_id = int(get_jwt_identity())
    
    try:
        # 获取用户进度记录
        progress_records = UserProgress.query.filter_by(user_id=user_id).all()
        
        # 计算平均尝试次数
        total_attempts = sum(p.attempts for p in progress_records)
        avg_attempts = total_attempts / len(progress_records) if progress_records else 0
        
        # 计算平均做题时间
        completed_progress = [p for p in progress_records if p.avg_time_sec]
        avg_completion_time = sum(p.avg_time_sec for p in completed_progress) / len(completed_progress) if completed_progress else 0
        
        # 错误模式分析
        from models.learning_models import Submission, ErrorType
        submissions = Submission.query.filter_by(user_id=user_id).all()
        
        error_counts = {}
        for submission in submissions:
            error_type = submission.error_type.value if submission.error_type else 'no_error'
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        # 学习时间分布
        time_distribution = {
            'fast': sum(1 for p in completed_progress if p.avg_time_sec and p.avg_time_sec < 120),      # 小于2分钟
            'medium': sum(1 for p in completed_progress if p.avg_time_sec and 120 <= p.avg_time_sec < 300),  # 2-5分钟  
            'slow': sum(1 for p in completed_progress if p.avg_time_sec and p.avg_time_sec >= 300)     # 大于5分钟
        }
        
        return {
            'performance': {
                'avg_attempts': round(avg_attempts, 2),
                'avg_completion_time': round(avg_completion_time, 2),
                'total_submissions': len(submissions)
            },
            'error_patterns': error_counts,
            'time_distribution': time_distribution
        }, 200
        
    except Exception as e:
        return {'error': '获取学习统计失败'}, 500

@progress_bp.route('/reset/<int:lesson_id>', methods=['POST'])
@jwt_required()
def reset_lesson_progress(lesson_id: int) -> Tuple[Dict, int]:
    """重置课程进度 - Learner Model重置功能"""
    user_id = int(get_jwt_identity())
    
    try:
        # 查找进度记录
        progress = UserProgress.query.filter_by(
            user_id=user_id,
            lesson_id=lesson_id
        ).first()
        
        if not progress:
            return {'error': '进度记录不存在'}, 404
        
        # 重置进度
        progress.status = ProgressStatus.NOT_STARTED
        progress.attempts = 0
        progress.avg_time_sec = None
        progress.first_attempt_at = None
        progress.completed_at = None
        
        db.session.commit()
        
        return {'message': '进度重置成功'}, 200
        
    except Exception as e:
        db.session.rollback()
        return {'error': '重置进度失败'}, 500