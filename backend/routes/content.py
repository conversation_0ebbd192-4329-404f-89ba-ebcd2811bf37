from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.content_models import Module, Lesson, Exercise, TestCase
from models.learning_models import UserProgress, ProgressStatus
from typing import Tuple, Dict, List
from sqlalchemy.orm import joinedload
from extensions import db

content_bp = Blueprint('content', __name__)

def get_db():
    return db

@content_bp.route('/modules', methods=['GET'])
@jwt_required()
def get_modules() -> Tuple[Dict, int]:
    """获取所有模块和课程列表 - Content Model核心功能"""
    user_id = int(get_jwt_identity())
    
    try:
        # 获取所有模块，预加载课程
        modules = Module.query.options(joinedload(Module.lessons)).order_by(Module.order_index).all()
        
        # 获取用户的学习进度
        user_progress = {
            progress.lesson_id: progress 
            for progress in UserProgress.query.filter_by(user_id=user_id).all()
        }
        
        # 构建响应数据，包含进度信息
        modules_data = []
        for module in modules:
            module_dict = module.to_dict() # 这现在会调用 lesson.to_summary_dict()
            
            # 为每个课程摘要添加进度和解锁状态
            for lesson_summary in module_dict['lessons']:
                lesson_id = lesson_summary['lesson_id']
                progress = user_progress.get(lesson_id)
                
                if progress:
                    lesson_summary['progress'] = progress.to_dict()
                else:
                    lesson_summary['progress'] = {
                        'status': ProgressStatus.NOT_STARTED.value,
                        'attempts': 0,
                        'should_show_hint': False
                    }
                
                # 检查前置课程是否完成
                lesson_summary['is_unlocked'] = check_lesson_unlocked(lesson_id, user_progress)
            
            modules_data.append(module_dict)
        
        return {'modules': modules_data}, 200
        
    except Exception as e:
        return {'error': '获取课程列表失败'}, 500

@content_bp.route('/lessons/<int:lesson_id>', methods=['GET'])
@jwt_required()
def get_lesson(lesson_id: int) -> Tuple[Dict, int]:
    """获取特定课程详情 - Interaction Model前置检查"""
    user_id = int(get_jwt_identity())
    
    try:
        from flask import current_app
        current_app.logger.info(f"Accessing lesson {lesson_id} for user {user_id}")
        
        # 查找课程
        lesson = Lesson.query.options(
            joinedload(Lesson.exercises).joinedload(Exercise.test_cases)
        ).get(lesson_id)
        
        if not lesson:
            current_app.logger.error(f"Lesson {lesson_id} not found")
            return {'error': '课程不存在'}, 404
        
        current_app.logger.info(f"Found lesson: {lesson.title}")
        
        # 检查前置课程是否完成 - Interaction Model规则
        user_progress = {
            progress.lesson_id: progress 
            for progress in UserProgress.query.filter_by(user_id=user_id).all()
        }
        
        current_app.logger.info(f"User has progress for lessons: {list(user_progress.keys())}")
        
        if not check_lesson_unlocked(lesson_id, user_progress):
            prerequisite = Lesson.query.get(lesson.prerequisite_lesson_id)
            current_app.logger.info(f"Lesson {lesson_id} locked, prerequisite: {prerequisite.title if prerequisite else 'Unknown'}")
            return {
                'error': f'请先完成前置课程：{prerequisite.title if prerequisite else "未知课程"}'
            }, 403
        
        current_app.logger.info(f"Lesson {lesson_id} is unlocked")
        
        # 获取或创建用户进度记录
        progress = UserProgress.query.filter_by(
            user_id=user_id, 
            lesson_id=lesson_id
        ).first()
        
        if not progress:
            current_app.logger.info(f"Creating new progress record for user {user_id}, lesson {lesson_id}")
            progress = UserProgress(user_id=user_id, lesson_id=lesson_id)
            db.session.add(progress)
            # 提交以获取progress_id
            db.session.commit()
            current_app.logger.info(f"Created progress record with status: {progress.status}")
        
        # 如果是第一次访问，标记为进行中
        if progress.status == ProgressStatus.NOT_STARTED:
            current_app.logger.info(f"Starting lesson for user {user_id}")
            progress.start_lesson()
            db.session.commit()
            current_app.logger.info(f"Progress status updated to: {progress.status}")
        
        # 如果课程没有练习，自动标记为完成
        if len(lesson.exercises) == 0 and progress.status == ProgressStatus.IN_PROGRESS:
            current_app.logger.info(f"Auto-completing lesson {lesson_id} (no exercises)")
            progress.complete_lesson()
            db.session.commit()
            current_app.logger.info(f"Lesson {lesson_id} marked as completed")
        
        # 构建课程数据
        current_app.logger.info(f"Building lesson data with {len(lesson.exercises)} exercises")
        lesson_data = lesson.to_dict()
        lesson_data['progress'] = progress.to_dict()
        
        # 为练习添加提示可见性
        for exercise in lesson_data['exercises']:
            exercise['hints_available'] = progress.should_show_hint()
            # 隐藏部分测试用例
            exercise['test_cases'] = [
                tc for tc in exercise['test_cases'] 
                if not tc.get('is_hidden', False)
            ]
        
        current_app.logger.info(f"Successfully returning lesson data for lesson {lesson_id}")
        return {'lesson': lesson_data}, 200
        
    except Exception as e:
        from flask import current_app
        current_app.logger.error(f"Error getting lesson {lesson_id}: {str(e)}", exc_info=True)
        db.session.rollback()
        return {'error': '获取课程详情失败'}, 500

@content_bp.route('/exercises/<int:exercise_id>/hints', methods=['GET'])
@jwt_required()
def get_exercise_hints(exercise_id: int) -> Tuple[Dict, int]:
    """获取练习提示 - Interaction Model脚手架功能"""
    user_id = int(get_jwt_identity())
    
    try:
        exercise = Exercise.query.get(exercise_id)
        if not exercise:
            return {'error': '练习不存在'}, 404
        
        # 检查用户是否有权限获取提示
        progress = UserProgress.query.filter_by(
            user_id=user_id,
            lesson_id=exercise.lesson_id
        ).first()
        
        if not progress or not progress.should_show_hint():
            return {'error': '暂不提供提示，请先尝试解决问题'}, 403
        
        hints = exercise.get_hints()
        return {'hints': hints}, 200
        
    except Exception as e:
        return {'error': '获取提示失败'}, 500

def check_lesson_unlocked(lesson_id: int, user_progress: Dict) -> bool:
    """检查课程是否已解锁 - Content Model依赖检查"""
    lesson = Lesson.query.get(lesson_id)
    if not lesson:
        return False
    
    # 如果没有前置课程，则解锁
    if not lesson.prerequisite_lesson_id:
        return True
    
    # 检查前置课程是否完成
    prerequisite_progress = user_progress.get(lesson.prerequisite_lesson_id)
    return (prerequisite_progress and 
            prerequisite_progress.status == ProgressStatus.COMPLETED)