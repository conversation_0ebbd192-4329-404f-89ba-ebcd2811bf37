from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from models.learning_models import User, UserRole
from extensions import db
from typing import Tuple, Dict

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register() -> Tuple[Dict, int]:
    """用户注册"""
    data = request.get_json()
    
    # 验证输入
    if not data or not data.get('username') or not data.get('email') or not data.get('password'):
        return {'error': '用户名、邮箱和密码都是必填项'}, 400
    
    # 检查用户是否已存在
    if User.query.filter_by(username=data['username']).first():
        return {'error': '用户名已存在'}, 409
    
    if User.query.filter_by(email=data['email']).first():
        return {'error': '邮箱已被使用'}, 409
    
    # 创建新用户
    try:
        user = User(
            username=data['username'],
            email=data['email'],
            role=UserRole.ADMIN if data.get('is_admin') else UserRole.STUDENT
        )
        user.set_password(data['password'])
        
        db.session.add(user)
        db.session.commit()
        
        # 创建访问令牌
        access_token = create_access_token(identity=str(user.user_id))
        
        return {
            'message': '注册成功',
            'access_token': access_token,
            'user': user.to_dict()
        }, 201
        
    except Exception as e:
        db.session.rollback()
        return {'error': '注册失败，请稍后重试'}, 500

@auth_bp.route('/login', methods=['POST'])
def login() -> Tuple[Dict, int]:
    """用户登录"""
    data = request.get_json()
    
    if not data or not data.get('username') or not data.get('password'):
        return {'error': '用户名和密码都是必填项'}, 400
    
    # 查找用户
    user = User.query.filter_by(username=data['username']).first()
    
    if user and user.check_password(data['password']):
        access_token = create_access_token(identity=str(user.user_id))
        return {
            'message': '登录成功',
            'access_token': access_token,
            'user': user.to_dict()
        }, 200
    else:
        return {'error': '用户名或密码错误'}, 401

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile() -> Tuple[Dict, int]:
    """获取用户资料"""
    user_id = int(get_jwt_identity())
    user = User.query.get(user_id)
    
    if not user:
        return {'error': '用户不存在'}, 404
    
    return {'user': user.to_dict()}, 200

@auth_bp.route('/verify', methods=['GET'])
@jwt_required()
def verify_token() -> Tuple[Dict, int]:
    """验证令牌有效性"""
    user_id = int(get_jwt_identity())
    user = User.query.get(user_id)
    
    if not user:
        return {'error': '无效的令牌'}, 401
    
    return {'valid': True, 'user_id': user_id}, 200