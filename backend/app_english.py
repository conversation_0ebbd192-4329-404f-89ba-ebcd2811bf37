#!/usr/bin/env python3
"""
English Version Application Launcher
Runs the Python Learning Platform with English content
Perfect for UK MSc Computer Science project demonstrations
"""

import os
from app import create_app

# Override database path to use English database
ENGLISH_DB_PATH = os.path.join(os.path.dirname(__file__), 'instance', 'learning_platform_english.db')

def create_english_app():
    """Create app instance with English database"""
    from flask import Flask
    from flask_cors import CORS
    from config import get_config
    from extensions import db, jwt
    
    # 获取配置
    config = get_config()
    
    # 创建Flask应用
    app = Flask(__name__)
    
    # 配置应用 - 使用英文数据库路径
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{ENGLISH_DB_PATH}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = config.JWT_SECRET_KEY
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = config.JWT_ACCESS_TOKEN_EXPIRES
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins=config.CORS_ORIGINS or ['*'])
    
    # 注册蓝图
    from routes.auth import auth_bp
    from routes.content import content_bp
    from routes.submissions import submissions_bp
    from routes.progress import progress_bp
    from routes.admin import admin_bp
    from routes.analytics import analytics_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(content_bp, url_prefix='/api/content')
    app.register_blueprint(submissions_bp, url_prefix='/api/submissions')
    app.register_blueprint(progress_bp, url_prefix='/api/progress')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(analytics_bp, url_prefix='/api/analytics')
    
    # 根路径 - API文档页面
    @app.route('/')
    def index():
        """English API Documentation"""
        api_docs = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Interactive Python Learning Platform - English Edition</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
                h2 { color: #34495e; margin-top: 30px; }
                .endpoint { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .method { font-weight: bold; color: #e74c3c; }
                .status { background: #2ecc71; color: white; padding: 5px 10px; border-radius: 3px; font-size: 14px; }
                .flag { font-size: 24px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🇬🇧 Interactive Python Learning Platform - English Edition</h1>
                <div class="status">Service Running</div>
                
                <h2>🎓 Academic Features</h2>
                <ul>
                    <li>Complete English course content</li>
                    <li>Intelligent Tutoring System (ITS) architecture</li>
                    <li>Sample student data for demonstrations</li>
                    <li>Code execution and feedback system</li>
                </ul>
                
                <h2>📚 API Endpoints</h2>
                <div class="endpoint">
                    <span class="method">GET</span> /api/content/modules - Get course modules<br>
                    <span class="method">POST</span> /api/submissions/submit - Submit code for evaluation<br>
                    <span class="method">GET</span> /api/progress/dashboard - Learning dashboard
                </div>
                
                <p><strong>Perfect for MSc Computer Science project presentations!</strong></p>
            </div>
        </body>
        </html>
        """
        return api_docs
    
    # 健康检查端点
    @app.route('/api/health')
    def health_check():
        from utils.code_executor import CodeExecutor
        executor = CodeExecutor()
        judge0_status = executor.health_check()
        
        return {
            'status': 'healthy', 
            'message': 'Python Learning Platform English Edition is running',
            'database': 'English content database',
            'judge0_api': 'available' if judge0_status else 'unavailable',
            'code_execution': 'judge0' if judge0_status else 'simulation'
        }
    
    print("🇬🇧 Using English database for content")
    print(f"📍 Database: {ENGLISH_DB_PATH}")
    
    return app

if __name__ == '__main__':
    from config import get_config
    
    config = get_config()
    app = create_english_app()
    
    print("🎓 Python Learning Platform - English Edition")
    print("="*50)
    print("🚀 Starting English version for MSc project...")
    print(f"📚 API Documentation: http://{config.HOST}:{config.PORT}")
    print(f"🔍 Health Check: http://{config.HOST}:{config.PORT}/api/health")
    print(f"🌐 Frontend URL: {config.FRONTEND_URL}")
    print("="*50)
    print("✨ Features:")
    print("   • All course content in English")
    print("   • Professional lesson structure")
    print("   • Sample student progress data")
    print("   • Ready for academic presentations")
    print("="*50)
    
    app.run(debug=config.DEBUG, host=config.HOST, port=config.PORT)