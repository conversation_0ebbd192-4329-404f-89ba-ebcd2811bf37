from flask import Flask, render_template_string
from flask_cors import CORS
import os
from extensions import db, jwt
from config import get_config

# 获取配置
config = get_config()

# 统一数据库路径
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
INSTANCE_DIR = os.path.join(BASE_DIR, 'instance')
if not os.path.exists(INSTANCE_DIR):
    os.makedirs(INSTANCE_DIR)
DB_PATH = os.path.join(INSTANCE_DIR, 'learning_platform.db')


def create_app():
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 使用配置管理
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['SQLALCHEMY_DATABASE_URI'] = config.DATABASE_URL or f'sqlite:///{DB_PATH}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = config.JWT_SECRET_KEY
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = config.JWT_ACCESS_TOKEN_EXPIRES
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app, origins=config.CORS_ORIGINS or ['*'])  # 使用配置的CORS源
    
    # 注册蓝图
    from routes.auth import auth_bp
    from routes.content import content_bp
    from routes.submissions import submissions_bp
    from routes.progress import progress_bp
    from routes.admin import admin_bp
    from routes.analytics import analytics_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(content_bp, url_prefix='/api/content')
    app.register_blueprint(submissions_bp, url_prefix='/api/submissions')
    app.register_blueprint(progress_bp, url_prefix='/api/progress')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(analytics_bp, url_prefix='/api/analytics')
    
    # 根路径 - API文档页面
    @app.route('/')
    def index():
        """API文档首页"""
        api_docs = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>交互式Python学习平台 - API文档</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
                h2 { color: #34495e; margin-top: 30px; }
                .endpoint { background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .method { font-weight: bold; color: #e74c3c; }
                .status { background: #2ecc71; color: white; padding: 5px 10px; border-radius: 3px; font-size: 14px; }
                .links { margin-top: 30px; }
                .link { display: inline-block; background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; }
                .link:hover { background: #2980b9; }
                .features { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                .feature-list { list-style-type: none; padding: 0; }
                .feature-list li { padding: 8px 0; border-bottom: 1px solid #dee2e6; }
                .feature-list li:before { content: "✅ "; color: #28a745; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🐍 交互式Python学习平台</h1>
                <div class="status">服务运行正常</div>
                
                <div class="features">
                    <h2>🎯 核心特性</h2>
                    <ul class="feature-list">
                        <li>智能辅导系统 (ITS) 三模型架构</li>
                        <li>个性化学习路径和进度跟踪</li>
                        <li>浏览器内代码编辑器</li>
                        <li>实时代码执行和自动评估</li>
                        <li>分级提示和脚手架支持</li>
                        <li>学习分析和错误模式识别</li>
                    </ul>
                </div>
                
                <h2>📚 API接口</h2>
                
                <h3>认证相关</h3>
                <div class="endpoint">
                    <span class="method">POST</span> /api/auth/register - 用户注册<br>
                    <span class="method">POST</span> /api/auth/login - 用户登录<br>
                    <span class="method">GET</span> /api/auth/profile - 获取用户资料
                </div>
                
                <h3>内容管理</h3>
                <div class="endpoint">
                    <span class="method">GET</span> /api/content/modules - 获取课程模块<br>
                    <span class="method">GET</span> /api/content/lessons/:id - 获取课程详情<br>
                    <span class="method">GET</span> /api/content/exercises/:id/hints - 获取练习提示
                </div>
                
                <h3>代码提交</h3>
                <div class="endpoint">
                    <span class="method">POST</span> /api/submissions/submit - 提交代码评估<br>
                    <span class="method">POST</span> /api/submissions/exercises/:id/hint - 请求提示<br>
                    <span class="method">GET</span> /api/submissions/exercises/:id/history - 提交历史
                </div>
                
                <h3>学习进度</h3>
                <div class="endpoint">
                    <span class="method">GET</span> /api/progress/dashboard - 学习仪表板<br>
                    <span class="method">GET</span> /api/progress/stats - 学习统计<br>
                    <span class="method">POST</span> /api/progress/reset/:id - 重置课程进度
                </div>
                
                <h3>学习分析</h3>
                <div class="endpoint">
                    <span class="method">GET</span> /api/analytics/user/code_analysis - 用户代码分析<br>
                    <span class="method">GET</span> /api/analytics/exercise/:id/analysis - 练习分析<br>
                    <span class="method">GET</span> /api/analytics/admin/platform_analysis - 平台分析 (管理员)
                </div>
                
                <div class="links">
                    <a href="/api/health" class="link">🔍 健康检查</a>
                    <a href="{config.FRONTEND_URL}" class="link">🖥️ 前端应用</a>
                    <a href="https://github.com" class="link">📖 项目文档</a>
                </div>
                
                <h2>🚀 快速开始</h2>
                <div class="features">
                    <p><strong>前端开发服务器：</strong></p>
                    <code>cd frontend && npm install && npm run dev</code>
                    <p style="margin-top: 15px;"><strong>数据库初始化：</strong></p>
                    <code>cd backend && python init_db.py</code>
                </div>
            </div>
        </body>
        </html>
        """
        return render_template_string(api_docs)
    
    # 健康检查端点
    @app.route('/api/health')
    def health_check():
        from utils.code_executor import CodeExecutor
        executor = CodeExecutor()
        judge0_status = executor.health_check()
        
        return {
            'status': 'healthy', 
            'message': 'Python Learning Platform API is running',
            'judge0_api': 'available' if judge0_status else 'unavailable',
            'code_execution': 'judge0' if judge0_status else 'simulation'
        }
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🚀 启动交互式Python学习平台后端服务...")
    print(f"📚 访问 http://{config.HOST}:{config.PORT} 查看API文档")
    print(f"🔍 访问 http://{config.HOST}:{config.PORT}/api/health 检查服务状态")
    print(f"🌐 前端地址: {config.FRONTEND_URL}")
    print("📖 查看 README.md 获取完整使用说明")
    app.run(debug=config.DEBUG, host=config.HOST, port=config.PORT)