# 🔍 调试 avg_time_sec 显示为 0:00 的问题

## 📋 调试步骤

### 1. 检查前端计时器
1. 打开浏览器开发者工具（F12）
2. 进入任意课程页面
3. 开始编写代码（修改默认代码）
4. 查看控制台是否输出：`开始做题时间: [时间]`
5. 点击"运行代码"按钮
6. 查看控制台是否输出：
   - `本次做题耗时: X秒`
   - `传递给后端的做题耗时: X秒`

### 2. 检查网络请求
1. 在开发者工具中切换到"网络"标签
2. 提交代码时查看 `POST /api/submissions/submit` 请求
3. 检查请求体是否包含 `solving_time_sec` 字段
4. 确认该字段的值是否正确

### 3. 检查后端日志
查看后端控制台输出：
```
DEBUG: 接收到做题时间: X秒, 用户ID: X, 课程ID: X
DEBUG: update_avg_time调用 - 输入时间: X, 当前平均: null, 尝试次数: 1
DEBUG: 计算后平均时间: X
```

### 4. 检查数据库
连接数据库，查询 `user_progress` 表：
```sql
SELECT lesson_id, attempts, avg_time_sec, updated_at 
FROM user_progress 
WHERE user_id = [你的用户ID] 
ORDER BY updated_at DESC;
```

### 5. 检查前端显示
1. 页面加载后查看控制台输出：`DEBUG: 课程数据加载完成: [数据]`
2. 检查页面是否显示：`[DEBUG: avg_time_sec = X]`

## 🔧 可能的问题和解决方案

### 问题1: 前端不计时
**症状**: 控制台没有"开始做题时间"输出
**解决**: 确保输入的代码与默认占位符不同

### 问题2: 时间没有传递给后端
**症状**: 网络请求中没有`solving_time_sec`字段
**解决**: 检查前端逻辑，确保`hasStartedCoding`为true

### 问题3: 后端没有接收到数据
**症状**: 后端控制台没有DEBUG输出
**解决**: 检查后端路由是否正确

### 问题4: 数据库没有更新
**症状**: 数据库中`avg_time_sec`仍为NULL
**解决**: 检查`db.session.commit()`是否执行

### 问题5: 前端没有显示
**症状**: 页面显示`[DEBUG: avg_time_sec = null]`
**解决**: 检查课程数据加载逻辑

## 🚀 快速测试

1. 进入任意课程
2. 修改代码（比如添加一行`print("test")`）
3. 等待5秒
4. 点击"运行代码"
5. 检查各个步骤的输出

## 💡 预期结果

如果一切正常，你应该看到：
1. ✅ 前端计时器开始计时
2. ✅ 网络请求包含`solving_time_sec`
3. ✅ 后端DEBUG输出正确
4. ✅ 数据库`avg_time_sec`字段更新
5. ✅ 页面显示"📊 平均做题时间: 0:05" 