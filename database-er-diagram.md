# Python学习平台 - 数据库实体关系图 (ER Diagram)

## 概述

本文档包含Python学习平台的完整数据库实体关系图，基于智能辅导系统(ITS)三模型架构设计。

## ER图 (Mermaid代码)

```mermaid
erDiagram
    %% ITS三模型架构的数据库设计
    
    %% ========== Content Model (内容模型) ==========
    modules {
        int module_id PK "模块ID"
        string title "模块标题"
        text description "模块描述"
        int order_index "排序索引"
    }
    
    lessons {
        int lesson_id PK "课程ID"
        int module_id FK "所属模块ID"
        string title "课程标题"
        text content_md "Markdown内容"
        int prerequisite_lesson_id FK "前置课程ID"
        int order_index "排序索引"
    }
    
    exercises {
        int exercise_id PK "练习ID"
        int lesson_id FK "所属课程ID"
        text problem_statement "题目描述"
        text hints "分级提示(JSON)"
    }
    
    test_cases {
        int test_case_id PK "测试用例ID"
        int exercise_id FK "所属练习ID"
        text input_data "输入数据"
        text expected_output "期望输出"
        boolean is_hidden "是否隐藏"
    }
    
    %% ========== Learner Model (学习者模型) ==========
    users {
        int user_id PK "用户ID"
        string username UK "用户名"
        string email UK "邮箱"
        string password_hash "密码哈希"
        enum role "角色(STUDENT/ADMIN)"
        datetime created_at "创建时间"
    }
    
    user_progress {
        int progress_id PK "进度ID"
        int user_id FK "用户ID"
        int lesson_id FK "课程ID"
        enum status "状态(NOT_STARTED/IN_PROGRESS/COMPLETED)"
        int attempts "尝试次数"
        float avg_time_sec "平均用时"
        datetime first_attempt_at "首次尝试时间"
        datetime completed_at "完成时间"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    submissions {
        int submission_id PK "提交ID"
        int user_id FK "用户ID"
        int exercise_id FK "练习ID"
        text code "提交代码"
        boolean is_correct "是否正确"
        enum error_type "错误类型"
        text error_message "错误信息"
        text output "输出结果"
        float exec_time_sec "执行时间"
        datetime timestamp "提交时间"
        int code_lines "代码行数"
        float code_complexity "代码复杂度"
        float syntax_score "语法分数"
        text api_analysis_result "API分析结果(JSON)"
    }
    
    %% ========== Interaction Model (交互模型) ==========
    hint_usage {
        int hint_usage_id PK "提示使用ID"
        int user_id FK "用户ID"
        int exercise_id FK "练习ID"
        int hint_level "提示级别"
        datetime used_at "使用时间"
    }
    
    feedback_instances {
        int feedback_id PK "反馈ID"
        int submission_id FK "提交ID"
        enum feedback_type "反馈类型(JUDGE0/OPENAI/DEEPSEEK/LOCAL/HYBRID)"
        text feedback_content "反馈内容"
        float generation_time_ms "生成时间(毫秒)"
        float api_cost "API成本"
        datetime created_at "创建时间"
    }
    
    feedback_effectiveness {
        int effectiveness_id PK "效果评估ID"
        int user_id FK "用户ID"
        int exercise_id FK "练习ID"
        int feedback_id FK "反馈ID"
        int subsequent_attempts "后续尝试次数"
        float time_to_success_sec "成功用时"
        boolean did_succeed "是否成功"
        float helpfulness_rating "有用性评分(1-5)"
        float clarity_rating "清晰度评分(1-5)"
        enum triggering_error_type "触发错误类型"
        datetime created_at "创建时间"
        datetime success_achieved_at "成功时间"
    }
    
    %% ========== 关系定义 ==========
    
    %% Content Model 内部关系
    modules ||--o{ lessons : "包含"
    lessons ||--o{ exercises : "包含"
    lessons ||--o| lessons : "前置依赖"
    exercises ||--o{ test_cases : "包含"
    
    %% Learner Model 关系
    users ||--o{ user_progress : "学习进度"
    users ||--o{ submissions : "代码提交"
    lessons ||--o{ user_progress : "课程进度"
    exercises ||--o{ submissions : "练习提交"
    
    %% Interaction Model 关系
    users ||--o{ hint_usage : "提示使用"
    exercises ||--o{ hint_usage : "练习提示"
    submissions ||--o{ feedback_instances : "反馈实例"
    users ||--o{ feedback_effectiveness : "反馈效果"
    exercises ||--o{ feedback_effectiveness : "练习反馈"
    feedback_instances ||--o{ feedback_effectiveness : "效果评估"
```

## 架构说明

### 🎯 ITS三模型架构

#### 1. Content Model (内容模型)
- **modules**: 课程模块，提供层次化的内容组织
- **lessons**: 具体课程，包含Markdown格式的教学内容
- **exercises**: 编程练习题，包含题目描述和分级提示
- **test_cases**: 测试用例，支持公开和隐藏测试

#### 2. Learner Model (学习者模型)
- **users**: 用户基础信息，支持学生和管理员角色
- **user_progress**: 学习进度跟踪，实现Overlay Model
- **submissions**: 代码提交记录，包含详细的代码分析数据

#### 3. Interaction Model (交互模型)
- **hint_usage**: 提示系统使用记录
- **feedback_instances**: 多种反馈方式的实例存储
- **feedback_effectiveness**: 反馈效果评估，支持教育研究

### 🔗 关键特性

1. **个性化学习路径**: 基于前置依赖的课程序列
2. **智能反馈系统**: 支持多种AI模型的反馈对比
3. **学习分析**: 详细的学习行为和效果数据收集
4. **代码质量评估**: 多维度的代码分析和评分
5. **适应性提示**: 基于学习表现的智能提示系统

### 📊 数据流向

1. **学习流程**: User → UserProgress → Lesson → Exercise → Submission
2. **反馈循环**: Submission → FeedbackInstance → FeedbackEffectiveness
3. **提示系统**: User ↔ HintUsage ↔ Exercise
4. **内容结构**: Module → Lesson → Exercise → TestCase

## 使用说明

1. 将上述Mermaid代码复制到支持Mermaid的编辑器中查看图形化ER图
2. 推荐使用工具：
   - GitHub/GitLab (原生支持)
   - Typora
   - VS Code + Mermaid插件
   - 在线Mermaid编辑器

## 更新日志

- 2025-01-01: 初始版本，包含完整的ITS三模型架构设计
