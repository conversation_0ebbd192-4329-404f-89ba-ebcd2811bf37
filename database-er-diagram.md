# Python学习平台 - 数据库实体关系图 (ER Diagram)

## 概述

本文档包含Python学习平台的完整数据库实体关系图，基于智能辅导系统(ITS)三模型架构设计。

## ER图 (Mermaid代码)

### Database Entity-Relationship Diagram

```mermaid
erDiagram
    %% ITS三模型架构的数据库设计 - 精简版

    %% ========== Content Model (内容模型) ==========
    modules {
        int module_id PK
        string title
        text description
    }

    lessons {
        int lesson_id PK
        int module_id FK
        string title
        int prerequisite_lesson_id FK
    }

    exercises {
        int exercise_id PK
        int lesson_id FK
        text problem_statement
    }

    test_cases {
        int test_case_id PK
        int exercise_id FK
        text expected_output
        boolean is_hidden
    }

    %% ========== Learner Model (学习者模型) ==========
    users {
        int user_id PK
        string username
        string email
        enum role
    }

    user_progress {
        int progress_id PK
        int user_id FK
        int lesson_id FK
        enum status
        int attempts
        float avg_time_sec
    }

    submissions {
        int submission_id PK
        int user_id FK
        int exercise_id FK
        text code
        boolean is_correct
        enum error_type
        datetime timestamp
    }

    %% ========== Interaction Model (交互模型) ==========
    feedback_instances {
        int feedback_id PK
        int submission_id FK
        enum feedback_type
        text feedback_content
    }

    feedback_effectiveness {
        int effectiveness_id PK
        int user_id FK
        int exercise_id FK
        int feedback_id FK
        int subsequent_attempts
        boolean did_succeed
    }

    %% ========== 关系定义 ==========

    %% Content Model 内部关系
    modules ||--o{ lessons : "包含"
    lessons ||--o{ exercises : "包含"
    lessons ||--o| lessons : "前置依赖"
    exercises ||--o{ test_cases : "包含"

    %% Learner Model 关系
    users ||--o{ user_progress : "学习进度"
    users ||--o{ submissions : "代码提交"
    lessons ||--o{ user_progress : "课程进度"
    exercises ||--o{ submissions : "练习提交"

    %% Interaction Model 关系
    submissions ||--o{ feedback_instances : "反馈实例"
    users ||--o{ feedback_effectiveness : "反馈效果"
    exercises ||--o{ feedback_effectiveness : "练习反馈"
    feedback_instances ||--o{ feedback_effectiveness : "效果评估"
```

## 架构说明

### 🎯 ITS三模型架构

#### 1. Content Model (内容模型)
- **modules**: 课程模块，提供层次化的内容组织
- **lessons**: 具体课程，包含Markdown格式的教学内容
- **exercises**: 编程练习题，包含题目描述和分级提示
- **test_cases**: 测试用例，支持公开和隐藏测试

#### 2. Learner Model (学习者模型)
- **users**: 用户基础信息，支持学生和管理员角色
- **user_progress**: 学习进度跟踪，实现Overlay Model
- **submissions**: 代码提交记录，包含详细的代码分析数据

#### 3. Interaction Model (交互模型)
- **hint_usage**: 提示系统使用记录
- **feedback_instances**: 多种反馈方式的实例存储
- **feedback_effectiveness**: 反馈效果评估，支持教育研究

### 🔗 关键特性

1. **个性化学习路径**: 基于前置依赖的课程序列
2. **智能反馈系统**: 支持多种AI模型的反馈对比
3. **学习分析**: 详细的学习行为和效果数据收集
4. **代码质量评估**: 多维度的代码分析和评分
5. **适应性提示**: 基于学习表现的智能提示系统

### 📊 数据流向

1. **学习流程**: User → UserProgress → Lesson → Exercise → Submission
2. **反馈循环**: Submission → FeedbackInstance → FeedbackEffectiveness
3. **提示系统**: User ↔ HintUsage ↔ Exercise
4. **内容结构**: Module → Lesson → Exercise → TestCase

---

## 论文专用精简版 ER图

以下是专门为论文Figure 5.2设计的精简版本，突出核心实体和关键关系：

```mermaid
erDiagram
    %% Figure 5.2: Entity-Relationship Diagram for the Learning Platform
    %% ITS Three-Model Architecture

    %% Content Model - Blue Theme
    Modules {
        module_id PK
        title string
        description text
        order_index int
    }

    Lessons {
        lesson_id PK
        module_id FK
        title string
        content_md text
        prerequisite_lesson_id FK
        order_index int
    }

    Exercises {
        exercise_id PK
        lesson_id FK
        problem_statement text
        hints text
    }

    Test_Cases {
        test_case_id PK
        exercise_id FK
        input_data text
        expected_output text
        is_hidden boolean
    }

    %% Learner Model - Green Theme
    Users {
        user_id PK
        username string
        email string
        role enum
        created_at datetime
    }

    User_Progress {
        progress_id PK
        user_id FK
        lesson_id FK
        status enum
        attempts int
        avg_time_sec float
        first_attempt_at datetime
        completed_at datetime
    }

    %% Interaction Model - Orange Theme
    Submissions {
        submission_id PK
        user_id FK
        exercise_id FK
        code text
        is_correct boolean
        error_type enum
        exec_time_sec float
        timestamp datetime
        code_complexity float
    }

    Feedback_Instances {
        feedback_id PK
        submission_id FK
        feedback_type enum
        feedback_content text
        generation_time_ms float
        api_cost float
        created_at datetime
    }

    Feedback_Effectiveness {
        effectiveness_id PK
        user_id FK
        exercise_id FK
        feedback_id FK
        subsequent_attempts int
        time_to_success_sec float
        did_succeed boolean
        helpfulness_rating float
        clarity_rating float
        triggering_error_type enum
        created_at datetime
        success_achieved_at datetime
    }

    %% Relationships
    Modules ||--o{ Lessons : contains
    Lessons ||--o{ Exercises : contains
    Lessons ||--o| Lessons : prerequisite
    Exercises ||--o{ Test_Cases : contains

    Users ||--o{ User_Progress : tracks
    Users ||--o{ Submissions : submits
    Lessons ||--o{ User_Progress : progress_on
    Exercises ||--o{ Submissions : attempts

    Submissions ||--o{ Feedback_Instances : generates
    Users ||--o{ Feedback_Effectiveness : evaluates
    Exercises ||--o{ Feedback_Effectiveness : measures
    Feedback_Instances ||--o{ Feedback_Effectiveness : assesses

    %% Color Themes for ITS Models
    classDef contentModel fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef learnerModel fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef interactionModel fill:#fff3e0,stroke:#ef6c00,stroke-width:2px

    class Modules,Lessons,Exercises,Test_Cases contentModel
    class Users,User_Progress learnerModel
    class Submissions,Feedback_Instances,Feedback_Effectiveness interactionModel
```

## Key Design Features for Academic Research

### 📊 Enhanced Feedback_Effectiveness Entity

The `Feedback_Effectiveness` entity has been carefully designed to support comprehensive educational research with the following key attributes:

**Core Effectiveness Metrics:**
- `subsequent_attempts`: Number of attempts after receiving feedback
- `time_to_success_sec`: Active learning time from feedback to success
- `did_succeed`: Boolean indicating final success outcome

**User Experience Metrics:**
- `helpfulness_rating`: 1-5 scale rating for feedback usefulness
- `clarity_rating`: 1-5 scale rating for feedback clarity

**Research Analytics:**
- `triggering_error_type`: The error type that triggered the feedback
- `created_at`: Timestamp for temporal analysis
- `success_achieved_at`: Success timestamp for learning velocity analysis

These attributes enable:
1. **Quantitative Analysis**: Measuring feedback effectiveness through success rates and time metrics
2. **Qualitative Assessment**: User satisfaction ratings for different feedback types
3. **Error Pattern Analysis**: Understanding which error types benefit most from specific feedback approaches
4. **Temporal Studies**: Analyzing learning patterns over time
5. **Comparative Research**: A/B testing different feedback strategies

### 🎨 Visual Design for Academic Papers

**Color-Coded ITS Models:**
- **Content Model** (Blue): Represents the domain knowledge structure
- **Learner Model** (Green): Tracks individual student progress and characteristics
- **Interaction Model** (Orange): Captures teaching strategies and feedback mechanisms

**English Academic Format:**
- Standard entity naming conventions for international publications
- Clear relationship labels suitable for academic discourse
- Balanced detail level for readability in paper format

## Usage Guidelines

### 📄 For Academic Papers
- Use the "Enhanced Academic Version" as Figure 5.2
- The color coding helps readers distinguish the three ITS models
- Entity relationships clearly demonstrate the adaptive learning cycle

### 🔧 For Development
- Reference the detailed first version for implementation
- The academic version provides the conceptual framework

### 📱 Viewing Tools
1. [Mermaid Live Editor](https://mermaid.live/) - Copy and paste the code
2. GitHub/GitLab - Native Mermaid support
3. VS Code + Mermaid Preview extension
4. Typora editor
5. Academic writing tools with Mermaid support

---

## Figure 5.3: UML Sequence Diagram for Code Submission Process

### Mermaid Version

```mermaid
sequenceDiagram
    participant L as Learner
    participant F as Frontend App
    participant B as Backend API
    participant E as Code Execution Service
    participant A as AI Feedback Service
    participant D as Database

    Note over L,D: Code Submission and Feedback Process

    L->>F: 1. Submit code solution
    Note right of L: User completes coding<br/>and initiates evaluation

    F->>B: 2. POST /api/submissions<br/>{exercise_id, code}
    Note right of F: Send exercise ID<br/>and user code

    Note over B,A: Parallel Processing for Efficiency

    par Code Execution
        B->>E: 3a. Execute code with test cases
        E-->>B: 4a. Return execution results<br/>{is_correct, output, exec_time}
    and AI Analysis
        B->>A: 3b. Analyze code quality
        A-->>B: 4b. Return AI feedback<br/>{analysis, suggestions, score}
    end

    Note over B,D: Data Integration and Persistence

    B->>D: 5a. INSERT INTO submissions<br/>(user_id, exercise_id, code, results)
    B->>D: 5b. INSERT INTO feedback_instances<br/>(submission_id, feedback_content)
    B->>D: 5c. UPDATE user_progress<br/>(attempts, status, avg_time)

    D-->>B: 6. Confirm data persistence

    B-->>F: 7. Return complete response<br/>{execution_results, feedback, progress}
    Note left of B: Integrated JSON response<br/>with all evaluation data

    F-->>L: 8. Update UI with results
    Note left of F: Display execution results,<br/>feedback, and progress

    Note over L,D: End of Submission Cycle
```

### PlantUML Version

```plantuml
@startuml
!theme plain
title Figure 5.3: UML Sequence Diagram for Code Submission Process

actor Learner as L
participant "Frontend App" as F
participant "Backend API" as B
participant "Code Execution\nService" as E
participant "AI Feedback\nService" as A
database Database as D

note over L,D: Code Submission and Feedback Process

L -> F: 1. Submit code solution
note right of L: User completes coding\nand initiates evaluation

F -> B: 2. POST /api/submissions\n{exercise_id, code}
note right of F: Send exercise ID\nand user code

note over B,A: Parallel Processing for Efficiency

par
    B -> E: 3a. Execute code with test cases
    E --> B: 4a. Return execution results\n{is_correct, output, exec_time}
else
    B -> A: 3b. Analyze code quality
    A --> B: 4b. Return AI feedback\n{analysis, suggestions, score}
end

note over B,D: Data Integration and Persistence

B -> D: 5a. INSERT INTO submissions\n(user_id, exercise_id, code, results)
B -> D: 5b. INSERT INTO feedback_instances\n(submission_id, feedback_content)
B -> D: 5c. UPDATE user_progress\n(attempts, status, avg_time)

D --> B: 6. Confirm data persistence

B --> F: 7. Return complete response\n{execution_results, feedback, progress}
note left of B: Integrated JSON response\nwith all evaluation data

F --> L: 8. Update UI with results
note left of F: Display execution results,\nfeedback, and progress

note over L,D: End of Submission Cycle

@enduml
```

## Sequence Diagram Analysis

### 🔄 Core Interaction Flow

The sequence diagram illustrates the complete code submission and feedback process, highlighting:

1. **User Initiation**: Learner submits code through the frontend interface
2. **API Communication**: Frontend sends structured request to backend
3. **Parallel Processing**: Simultaneous code execution and AI analysis for efficiency
4. **Data Persistence**: Comprehensive database updates across multiple tables
5. **Response Integration**: Backend consolidates all results into unified response
6. **UI Updates**: Frontend displays complete evaluation results to learner

### ⚡ Key Design Features

**Parallel Processing Architecture:**
- Code execution and AI feedback generation occur simultaneously
- Minimizes user waiting time through concurrent operations
- Demonstrates system efficiency and scalability

**Comprehensive Data Tracking:**
- Updates three key database tables: `submissions`, `feedback_instances`, `user_progress`
- Ensures complete audit trail for learning analytics
- Supports the ITS three-model architecture data requirements

**Unified Response Model:**
- Backend integrates all evaluation components into single JSON response
- Reduces frontend complexity and network overhead
- Provides consistent user experience

### 📊 Comparison: Mermaid vs PlantUML

**Mermaid Advantages:**
- Native GitHub/GitLab support
- Simpler syntax for basic diagrams
- Better integration with modern documentation workflows

**PlantUML Advantages:**
- More mature and feature-rich
- Better control over diagram styling and layout
- Wider tool ecosystem and export options
- More precise positioning and formatting capabilities

Both versions accurately represent the same interaction flow, allowing you to choose based on your publication requirements and tool preferences.

## Changelog

- 2025-01-01: Initial version with complete ITS three-model architecture
- 2025-01-01: Added simplified academic version
- 2025-01-01: Enhanced with English labels, color themes, and research-focused attributes
- 2025-01-01: Added UML sequence diagram for code submission process (Mermaid & PlantUML)
