Research Question Workshop Gui（2025-06-26 11_07_09）_原文
2025年07月31日 22:35
40. 

And to look how often it is. So if you want, say, maybe in this one of that, and then maybe I can see you'll see a green box that sounds like 15 An in, please join in. 

These are designed to keep these sessions active. And so it's not just me talking for an hour, but when we're not doing an activity, we need remain quiet while I talk so I can hear people talking. At the moment, you are distracting people around you. You're discussing me. It's not appropriate because I know I'm talking and I will give you plenty of time to challenge yourselves during the activities. And importantly, ask any questions you might have. So I'll be here after the session. You've also got my email on the Canvas page. I'm happy to answer any questions you might have. 

The one thing I'm not saying is that these sessions are general guidance either. The science background, this is my experience, the recent projects, but you should follow any specific guidelines that your school has told you, okay? So always follow your school information first. So that wherever you in your research project, you probably have your research questions and a.s. ready. And maybe you've already made a plan, and maybe you've even started to do your research and get results. You've got some arrows going around the thermal sphere, and you're probably at the stage where you realize that the more research you do and the more questions you end up with. And then finally, at the very end of the summer, you're going to need to produce a report. I've got a link here at the bottom that you'll be able to access afterwards, and I'll put that on campus as well. 

There is some guidance for the use of generative AI ethically in your studies and your research. I'm not going to be talking about that today, but that is very unique. 

So I had a question on board when you came in, and it was, what makes a strong research question? So all of you are trying to discover something or make something or find something else. So you're all asking questions. I'm going to tell you what I think makes a strong research question. So I think it needs to focus to focus on a specific topic or issue, and you need to be able to measure it. So you need to be able to know when you've answered your question. And then kind of secondly, I suppose it needs to be appropriate. So at this level and masters level, it needs to be the right complexity, right level room originality, but it also needs to be feasible. 

There are lots of really interesting questions in the world, but you can't answer all of them in a framework of project. So my first back in for you today, I thought a research question on board it is how can we, how can we, how can electric cars be improved? And I want you to think about this and think about whether this is a good, strong research question or a weak research question. And I would like you. This is all anonymous. I would like you to submit your answer using this QR code. So I will give you a few minutes, think about it, talk to you, even think about whether this is a good question or not. How easy is this transfer? 

Thank. 

Okay, lots of times I answer this question. 

And I'm about to close it. 

So I don't know if you can see that on your screen, but the average. The average group, 1 83 of you voting is about halfway, which is interesting. So it's not a terrible question, and it's not a really, really good question. 

Is anyone feeling brave? Does anyone else want to tell me why they think it's a strong or weak question? Yeah, just a general, absolutely. Let me get back to the PowerPoint slide. Yeah, it's entirely too general. Does anyone else have any other comments? I'd like to. Yeah, so that really is the biggest problem, but it's not focused at all. So electric vehicles, that covers a lot of things, different manufacturers, many different variables. And what this improves me, we're talking about driving further, we're talking about it looking nicer, we're talking about it being more comfortable. 

And so how would you know when this question about this? So really, you got a phone call. Really, you want something where you should be able to picture what experiment you would do to answer the question. And it should not be possible to answer just as an opinion. So, so. 

Next question for you. I've rewriting the research question. I've not got four options down here, so it's not necessarily the right answer to this. But again, I would like you to vote on which one you think is the strong. I'll give you a little bit longer to read these and think about it. It's the same QR code the last time you when you have it open. 

Here at the back, there's some seat plug in this middle section. Yeah, if you come down a little. Bit. 

Okay, last 10 seconds to think about this question, then we'll move on. 

Thank, thank you. All right? 

Okay, I say now I think is. On this one, does anyone want to explain why they fixed the option they picked? Was it all just random looks that you kept 8%? Anyone feeling great? 

I'll tell you why I picked anything. Let's get out here. 

So the two things we got here. Sorry, I can hear people talking. I'm not going to continue while people talking. So, you know, people on their back road talking. I think I can hear people down here. 

In my, I have the come back to the idea of being so if we look at A from our range of the clicker, we're very close. So the experiment is a picture we're looking at. If we vary the background in density, we can measure the driving range. It's a very specific cific question, same idea, we think we can think about what that experiment would be. We can change the Co of alloy or how much sugar there is. I measure the energy consumption being gain still a little bit better, a little bit too unfocused. So now all I'm going to say, research questions, you will have a couple of reasons questions that you're pursuing, so I encourage you at the end of the session to think about whether your questions. Fit criteria for now. 

Project 5. Right, I find is important. You are somewhere around here and you have got about 10 weeks to finish your project and write a dissertation and submit them. So you need a plan of how to get from today. Next activity. Next question point. Next question towards your neighbor. Think about what makes an effective the plan. It can be science, it can be non sciences, and then submit your answer. So you'll need to type the word using this QR code. I'm going to give you a couple minutes and I'll come around the room as we do this. There's no wrong answer in this room. 

What makes? 

Okay, 180. 

Okay, last and. 

229 suggestions. 

You can see a big one is I we've on screen for you. 

We're. We've got clear jack fi tail, reasonable clear goal, specific goals, clearance structures. Yeah, these are all really, really good and really important part that makes you fun. 

These are my suggestions and you've covered all of them. Okay, so we've gone trying to install some more drugs. You need to know what you're going to be doing each week. Dead fire. And then the last one is quite important. What happens if things go wrong? Because very hard. I'm sure. I'm sure people who have done a lot of research in your room will agree with me when I say things don't always go, but I should plan. If things don't work, what are you going to do about it? So having a contingency fund is important for hands account focus, a simplified young chart, we are now somewhere. 

What can some times, some fun times, some research times, some writing report times, how do you think we can improve this class or are you happy with that? 

You can happen following this class. It good because I can tell you and looking at it, it makes me nervous and it makes me nervous for a couple reasons. Firstly, that is not enough time to write a structured and well-written you are planning to finish right on the deadline, so if something goes wrong, there is no wiggle. The other problem is you've made a big chunk of research that lasts for about three months. How do you know if you're on track? You don't, so here is an alternative that I would propose is a better option. So aim to finish before the deadline, give yourself some bottom time, maybe FG it, writing it out a little bit further, Break your research into smaller chunks and build in these little buffer times in 2 takes a little bit longer. Don't not stop being free. 

So if you've made a plan already, you've probably thought about these 5 points. But if you're at the stage where you don't have a plan, these are what I would point you towards to help hold them. And once you've, you need to be looking at the regulator and checking your progress against it. I would recommend you meet your sympathize as as possible and check in about the role. And then finally, things don't go well. Tell you things, time to tell someone, you know, I just come behind and people like Edward support, right? 

Ism going to give you a 1 minute brain to put in the attendance you can the top line if you don't the election that you can get the functional 1. If you have any major issues, just raise your runs and I will deal with it. You do have a can of. Smoke? 

You know, send it to anybody. I do not want to have just. 

Hi? 

Survey. 

Okay? Go? 

She? 

For. 

So this might not be what you want to hear ye. 

You can't do research projects without knowing how. Because you don't know how to analyze your results. You don't know how to critically evaluate the stress of your results. 

And there's no point that research project that you can't communicate the results. So how do you read efficiently? The good news is you don't have to read every single paper you come across, and that would be a massive waste of time. We're going to talk about how to find relevant literature in a future session, What we're going to focus on the day is deciding whether the paper is even worth reading and how to read it practically. 

So this is a very generic structure of a paper. It will vary in discipline VA, depending on the journal, that kind of thing. 

And as you are finding a paper, you want to look at the cost scheme values. So if you are trying to work out what the aim of the paper is, you want to be looking in the abstract. If you want to know what the results are, the methods they use, you want to look at the abstract, introduction and conclusion, the method because in discussion contains lots of informations that is used the live session. There's no point reading that whole paper if you can decide from just the abstract of instruction, it's probably that relevant. I'm not asking you to read this. Worry past the link on Canvas if you want to look at it, but this is an example abstract. And if I analyze this, they start by telling you the important results and the impact of the result. Then they talk about the methodology they use, then they tell you the second impulse result and the impact of that result. 

So if you're 1 paragraph, you know the overview paper. If we were to look at the introduction of that paper, again, not expect these reasons, I can colorcode it. So for an introduction, you generally start with background and context. So this is literature review, what's been done previously. 

At the end, you usually start talking about what this paper, what you knew, what they. And so you'll start seeing we or in the paper and so on. And then in between those concepts is usually where the knowledge gaps. So it's usually the part that tells you what they're trying to achieve. So if we zoom in on this, I have to start in here, See the screen? It says their proposals have emerged to explain the improvement in plq 1, including sync perturbation etching in the face of zinc doping, the face championing. However, a detailed understanding of this phenomenon is lacking and more data needed to gain insight into the mechanism. 

So if we translate that into just general English, the knowledge gap is there are some, but we don't have a good understanding and we need to have dangerous to understand how this happens. And so then you can think about the questions they're asking. And we probably asking questions along the lines of what is the mechanism to enhance this or how does certain conservation enhance this? So when you look in the paper, especially the introduction, you can pinpoint where knowledge gap is and work out what questions they might be asking. 

Okay, I to this sound if the pancreas Britain, well, and that is a big air, then you should be able to identify the aims of the paper, even if you're not an expert in that subject. So you can look at the abstraction in of a paper and try and work out what the knowledge gap is based on the mechanisms I just showed you. So for a couple of options X Pro, if you want to pick paper, sorry, this is a QR code that takes you to the canvas page and on the Canva I've got links to each of these three papers and the PDF. So if you want to access them that way I've got a green box around the top 1 because that's the example that I'm going to go through in about 5 minutes. So I'm going to give you some time to have a look at the instructions of on a paper and try and identify what they, and I'll be coming around the room if you've got any questions. And whilst we've got Russ and Jen, that can come answer questions. So I'm going to meet myself now and give you at least five minutes to have to think about this trick. 

This is, this is not an easy Aom tip. It's okay if you find a problem. Anybody recognize any of those underline games? 

You can also have people away or just Google all these pieces as well. They should all be accessible to those things. 

I at this university. 

Some groups may deputy director A for our college. 

So for the introduction, the thing I talk about on the screen and some little hints to think about. 

Yeah? 

Thank. You. 

Probably. For the people who find it so much easier, hard I inferring from that whole. I think it's. 

All right, second question. In doing electrical or electronic engineering, anyone here in the electronic room? So you might, he's a PGT leader within engineering. So this is one of his speakers, and I am not an electrical or an electronic or any other kind of engineers. So by science, we can find the same my daily profile. So you always can some kind review. 

And if we look carefully, we see here, we end with this is what we did in our So. In between the two, we have our A, which I've written about clear, so the sentence and it invalu the quantitative of estimate, in fact, shock the barrier on the on the CS. So I would say that at the end of the paper, and if we wanted, we could think about how we can actually put zoo. Again, this is my interpretation of good work. You can write that as what is the one secret facts of shocking barrier? And if we're looking at BMC or how can the effect of shock, the barriers on the C, basically. So it's good when you're reading paper. It's just, again, the habit of thinking, what are they trying to achieve this? 

Because often when you read a paper, they're telling you what they've done, but you've got to a sex, whether they have ahe, what they've said they've done, which leads me quite nicely in that section. 

So what you next, the research questions that you've looked at, the outcomes, decide whether it's relevant to you. If it is, read paper. If it's not, don't read paper. Your time is limited and it might feel like you're doing a better job with a research project if you read every single piece of literature. But there is a practicality benefit and you should try and be efficient in good time. So you found a paper, you've decided it's relevant. Now you've got to read something. 

Critical reading. So when you're breathing practically, you're analyzing the straightening evidence for and against the claim. So you're not just reading it and saying, oh, yeah. But sometimes you're reading it and you're thinking about it, and you're working out, when do you? Agree with it. 

And it's a skill you practice the So one level 1, what we want is what names of the paper, how the way things you want to start NJ than that you want to get down to 2 firstly, which is one strength weakness. You know, do they do they back up that kind of evidence or not? And then you want to go even deeper than that. You want to start thinking about how that work is relevant to your own work and thinking about how essential your project, the direction you're taking your work in, and the library, academic, academic skills gate. So I have load of friendship resources, and again, all of these slides will be shared so you can access these links. And then finally, step 4, the one that will save you most time is if you found the paper and you read it, stay there and make some notes so you don't have to reread it again. 

When you write things. The way to do this, some breakfast. I got breakfast monitor controls also file in another session, but just find a map of the words for you. So it depends what you are trying to assess. 

When I did my Phd, I was for a while interested in working out how to make jigsaw files naap pels to do that, so I chose to have a PowerPoint slide per paper. And the reason I did that because I can pull on the slide, the reference, the paper, and I make some, but it really helps me pull on the figures that were relevant so I can flick back through and see which figures I wanted to talk about. But it would be different for everyone. And my goal set reading efficiently. And that kind of ends what we were talking about today. 

So we talked about research questions, planning, and physical reading. I got some suggestions for things to think about going forward for you, so reflects on your research functions, reflects on your plans and practice critical reading, practice those three levels on the patient that's relevant to you. So that is the today's session, and thank you for your attention for being here. 

The next one is on 8 July. Frequently? Read up first question, please, and I will see you all next time, goodbye. 

Yes? 

Thank. 

And because the amazing? 

Thank. 
Programming Teaching System Di（2025-07-01 10_53_03）_原文
2025年07月06日 06:23
A . 

seat. So what's the situation . 

I'm? Ready, I want to show my 3 more motor. 

Okay, so now before we forget, before designing principles, okay, before you do the tables themselves, yes, you do. You know how to do an entity relationship model. 

So tell me, what . 

are you teaching classic Python? Python programming. Yeah. 

and you can do all this program. How is your programming? 

Good, okay. So that. 

Means you can, okay? Going basic programming, you're teaching basic programming. 

Okay, so. 

so what you say, basically. 

you've done a lot of work here. 

My system is based on two main ideas from education theory. The first one is scaffolding. Scaffolding, it means the system gives help. I like it only when the student need it the girls to provide just enough support. 

So, all right, okay, so you've got to this, how are you going to evaluate this? So you're going to write your system. I would like you see next I'd like to see the anti relationship because you're building the database. Yes, there is a database because you have database schema, the module, the lesson, the exercise test case is very good, okay, the ear, an theor relationship model, you know what it is. Yes, we've done that in the lecture. 

Nervous? 

Tell me. Yeah, you should inform people before you use a translator. Yeah, okay, if you are actually recording the conversation. So you must say to people, I am going to use the system. All right, so we need to look at the entity relationship model, that's one thing. And the second aspect is this, how you're going to evaluate this? 

I want to. So? 

Thank. 

I plan. 

To invite for, for now, compress student to test my system and correct their feedback so through inters. 

System . 

test. 

You're going to ask them to use your system, see whether they learn. Or yes? 

Yeah, I want to. 

If they are your friends, the day you have to program. 

you . 

see from here. 

No, they are. They are no, no comparison students. Oh. 

okay, they are not science students, okay? And see if you get more . 

and . 

then you can evaluate it that one way. Have you looked at the papers existing systems for teaching programming? 

We some. Some paper? 

Yeah, you need to read papers because you have remember the . 

structure I gave you? Yeah. 

I give you a structure of . 

paper. Yeah, you've got it. This section here. 

you need to work on this one. Yeah, because this is how you read the you read papers and then you create a summary and you look at existing systems and how they do things. Yes, that's very important. So you have to do it now because this will take you some time and you're going to, I would like to see the entity relationship Diam because all actually, it's not that difficult here for the modules. The lesson they exercise in the test. Cases so the exercises for training people for is the application of the lesson 1. 

Then the test is it help people to wear this method? And test case. I don't know what to do . 

now. Yeah, but probably for test. Yes, fine. So the algorithm quite a bit here. You need to, that's the theory. You have the structure, the user, the user programs, the machine first visit one answer update. I think you probably need an entity, a relationship diagram. Look at there are too many tables here. Yes, so we need to have an overall picture of what's happening. What's the relationship between the different entities? Yes, so you need to look at, you need to look at that. You need to look at the other papers, other systems. The reason is this. Why are you doing the system? Why are you developing the system? 

Yeah, okay, that's what If you read the papers, you will find maybe no. Yes, which means that you may be doing it because the systems are not very detailed, maybe they are not helpful, maybe there is something missing there. 

yes, yeah. 

and when you read the papers, you can see the criticism there. 

Yeah. 

because he says what's good, what's bad? And you can say, okay I'm going to add this date in my system. 

Yeah. 

because they will ask you this question, what, why are you doing this? They will say there are so many systems. Very important. 

Okay. 

so this is extremely important. You've got to answer this question here. And they will ask you one simple question. Remember, in one sentence, they will say, okay. 

what is your? 

What your project? 

About, about? 

To help people learn programming. 

But it's got to be clear in your header because when you come to demonstration, you've got to show them, you know what you're doing. Yeah, you know why you're doing it, and you can demonstrate the system. And that means you should show them how to do it. 

Yeah okay? 

Yeah, you carry on with this then. And but you have to make emphasize the difference of your system with the other systems. 

Yeah. 

because I would say, all right, it's the same thing over an overwa Emphasize something. 

yes. 

Okay, so next week, er, diagram, yes. And the difference, you have to look at the difference. That means you have to read the papers, very important, reading the paper, do it parallel, don't boring too much work, do it now, when have time. 

Yeah. 

and hopefully with the other diagram, and yeah, there is some program clear here. It's just I'm not sure what the tables are coming from. You've shown the dynamics here, you've shown everything kind of Uml. Yes, except except the E that's missing. 

you have got. 

The dynamic how we do things by you've talked about the tables and therefore the classes so correspond, you're going to use Python to develop . 

the system programming. 

Python. 

Jazz. 

yeah. What you have the okay, very. Flask Why you have graphic have here? You have it says here database Q and process here. 

Yeah? Reality to store. 

So no, I know, I know that, but why the two? They are different databases. 

Things. 

We. 

Are mixing two things here? Never mind if it's working, that would be okay. Right, okay, all right. 

So you've got work to do here. Yeah. 

you've got some work, so you've got to work harder. So we agree on what kind of this one here, if it's an ipad. 

ipad all. 

So, oh. So we talk about, I talking about the Er diagram . 

and the whole picture. 

If people want to know what's happening, that's one thing. You need to read the papers to justify of the work, the rationale why you're doing it. Yeah I'm sure you will find something. Yes, you can say people don't know, So you maybe you can give them more feedback, not find something slightly different, justify the work, and then so, er, reading. And of course, start playing with the programming so you're confident. But if your programming is good, it. Any questions about any questions . 

you want . 

to get on with. 

have a. Day? 
麦克风录音（2025-07-08 11_27_28）_原文
2025年07月12日 21:39
We've. Got one? O, do you mind? I think there was some confusion with somebody. So you can wait. 

Yeah okay? I'll be quick. It's my fault, okay? Have a so, so, so you are. 

Tasks and some advice. Today I will, report my work on these tasks. Your first task for me was to, make, yeah, diagram it is. 

Picture, big picture of my system. It has all the tables and shows how they connect. The green color is for the. 

Modules, okay, so the lessons are made up of modules here, tracks. Right, mix submission. What do you mean by submission? What's the meaning of submission here? Send if user to exercises they have? Okay, so basically this, the user is taking the exercise, yes, right, where are the results? Results it is. Got to be linked to the submission in some mission? 

Is correct? Sorry, no. Yeah, okay, but but can you see that the if it is correct or, oh, so this is the result you say basically, so this is the question. 

Is I submiss I so for this particular exercise? So one specific question, you say correct or not? Okay, this is, this is where the so what user progress? Progress ID is a lesson ID, but the lesson so includes exercise, okay? The lesson includes exercise, the user take this exercise so basically. The user progress. So through this. Yeah, okay, so, okay, this, this is. Just a module. Module is made up of this, okay? So that basically what we have is that we have the lessons themselves are made up of exercises. 

The exercise is taken by the user and the user track progress on the lesson, but they have to, I think they have to, the user tracks this and the link through this one submission. In case the submission, this must be linked to the submission. No, what do you think? Because in order to check progress, you have to check this the submission. 

What do you think, no? If you think about it, how can you check progress if you don't check the submission? Yeah? Thank. 

We have to think about this one because the progress itself, because you have to keep a record, okay, for this exercise like this. So how can you check record whether they are doing well or not? The only way is to check how they do well on the exercise or they do badly how they perform on the exercise. 

You agree? Yeah, makes sense. Does it make sense to you? So you need to look exactly. You need to look at that because you can't have them separate here for this lesson. You can do that indirectly through the exercise, but I think it's a submission that's linking the exercise, and the exercise is linked to the lesson, it's probably. So, so unless you're doing it indirectly, you say the progress itself, but the user can't see the user tracks its user progress, okay? 

Yeah, but it means you have to go through this, through this, through this. Okay, that's up to you. I think you're trying to capture a lot of things here. Yeah, but you can see that you have to check this in terms of processing and link it to the lesson. So the user is submitting this, this. So this exercise for this lesson, agree, And therefore to check the progress, I need to look at the performance of the user. 

This progress here, it has to be which exercise, which lesson, and of course, which module? So yes, I think that's fine. It looks, it makes sense to me, but you need to navigate it so that you can actually check the submission. Otherwise, if you don't check the submission, you can't tell whether he's doing well or badly. Yeah, so I think you've got the essence here. That's good progress. So what you should do is now check what you are going to do and see whether the Er diagram can support this. 

You have to test your operations, your functionality against this. 

Can I do express the progress? Can I, how, where are the and of course storage of the exercise? Is it text? Yes, it is text, of course, again, less than whatever. So, so everything is really around this, isn't it? With this one cares? Can use third party-type piece? So basically, this is how you represent the exercise. Right? Stages look at exercise, what you're going to teach. Yes, so the lessons have you represent the lesson, But the most important aspect is this one is the exercise in the submission because he's all around that, isn't it? Yeah, so how you represent, okay, that's good progress. So you've got to check that the operations you want to perform can be. 

What? You do. Yeah, that's good. The so we're talking about language program, the theory here, foundation feedback I'm. 

Of. 

That's good, so you have, that's good, good approach. You present the theory and you present specific ways of teaching. Yes, but you have to produce the table. Remember the table I talked about you? Yes, remember we say system one like this and so on, and we said there are limitations to these systems because they ask the question, why do you want to do this? 

To teach this programming? What, because another another website is, have some limit? You have to produce that table. Let me just show you. I think I showed you this table didn't. Last week? Here is, is it? So you have the table here, which is your system, which one is system? Free code is here, then the other one is here, and then you have the characteristic, yes, remember, and you think they have their this and you say, okay, these systems, they haven't got this, this, this, that's why I want to add, not too complicated, but show that you studied existing systems and you've seen the limitations and you want to improve it. 

In there, I use that 3, 3 model, to analysis other website, 3 or 4 maybe there are, there are lot of systems, so maybe 4, 5 will be good for table like this. And you just show the end. It's not that complicated. At least you have shown that you have studied the systems. Yes, another website have a strong, connect model, but their interaction model and learning model are not very smart. Student, okay, that's good. By the way, we talked about the learner model and the content learning content and the introduction model. Yes, you have got to talk about that. 

Very, very. 

Okay, that's good. That shows you've got to have the draw a diagram. It's good with the diagram that you have a box for each one. Learn more box for the introduction of box for this. And. And then when comes to you, and at the end, the twos, you look at my, the hand that I gave you the evaluation at the end, you talk about you add your system and then hopefully your system will be here. You will have 6 Stick, stick. Your system is doing better, hopefully. Now, my survey task, I do a small website the prototype. 

You can now. You can, you can. This is markdown. Markdown, this. Yep, praying earned it is. 

It is a mistake code. 

Three, three times. Three times. Mistake and need a hit you? 

Rather, write code. You can cook, cook, cook the eraser. Okay? So this is basic for them to try things, yeah? Yeah, that's good, that's good, but you've got to do the one where you teach, that was the one that corresponds to your data. So that is good. You have here. They try this. And then the other one will be to have the lesson, the module, the lessons, the exercise, and the user takes the exercise. You can see the instruction with the model. Yes? So the exercise is learning content model, the us, the user model, and the submission corresponds to the infection model. Yes, so you do that. We have all task. 

Yeah, hopefully you will get. Yeah, you've got to check them carefully that how to represent this and also how to check, progress, all right, Okay, so I'll see you next week. Let me see, I got confused with my because of this lecture, so see you next week. Let me have a good day. 
麦克风录音（2025-07-15 11_16_42）_原文
2025年07月15日 19:13
Seat. So what's the situation's? 

Sure ta. 

Thank you, I have, made good progress, this week on, class last May meeting, last week you asked me to, two main things create the comparison table in the lecture review. Draw a diagram of a 3 model, this, finish my review. Oh, this is the background is the table, which is here. Very good, okay. No, the proposed system, remember, comes at the end. You have the form I gave you. 

Remember that, yeah, you got it. Okay, so you have yours. All right, so this, this comparative evaluation is. When you do the background comparative evaluation, that's the table. But it's all this, not your system. Because the system, you will do it here. 

Comparison of others with others. Oh, so you take this table, we put it here, but at the you haven't done anything. And you say you look at the system and you say, okay. You look at these characters and you say, okay, there are things they do not do. So my system is going to cover that makes sense. 

Yeah, so this is the rational, the reason, motivation, it's clear. Yeah, this is, this is your, this is in the background reading very good when it comes to the evaluation here, you include everything and you talk about it in the advantages. So that makes sense, it's better. Okay, that's very good. So you've got this background here, right? And then what else? So this? Is. Have a look here. So we have interact model, model here, very good. So you have that tradition here, really the right database. Yes, the interaction model. 

Right when you have, you have the content model, the learner model here in the database. How about the interaction model? Do you keep track of the of this? Do you keep information on the interaction model? 

I, if you think about it, when you have the introduction model here, it's going to be set. Say you have exercises. Exercises, and then you're going to give exercises and to record the answer. So you can take the questions from the content here database and through the interaction model. Yes, you will have the exercise. Exercises here. To you? Going to say that teaching, how do you know? How can you check whether they've learned? Something they need to do, access this. Okay, that's going to be part of the introduction model. Agree? So that means you have to keep track of this in the database as well. 

Yes, so we have there will be question options, say answer ABC, and so on. Yes, question one, and then you have cancer ABC or D 4. Yes, in AIC it's basically. Question something like this. It's easier to up like this and you have an answer. But that means you have to store this in database as well. Yes, story. And you extract the question and you see how. So there is the teaching and there is the interaction through the learner is going to interact with the system, see whether they have learned something or not. So make it complete. Otherwise, what's the point? Because if you're going to teach only, you might as well have static files. 

HT, you agree? HTML files published one, this is a function published two, this is the, what is it the? Talk about the variables. Can you see now? 

Accesses how? This is your Is. 

So basically the learner has got exercise submis that means you've got to keep, you've got to keep these exercises. They have to come from the database. Yeah, it is in database. Okay, which is good because you have the long learning content. Let's get back. Can we get back to that, the other diagrams? The main one? 

This one, that's good. Yeah, exercise the lean content, but let us get back to this one, the interaction. This, this is here, can we get back to the? 

This one here has to be linked to the interaction model, yes, and stored in data because. This is, this is a record of your introduction, isn't it? The. 

Record of action. And you need to keep track of this because this. This, this will be used. 

You can study this because you have to. You could do, you could put a time stamp here. There it is. Yes, very good. You got time star, so you can follow through the progress through the time. Yeah, you can analyze this. So this one is needs to be connect it to the database because you've got to score this somewhere. So this actually, the exercises will be exercise ID and there will be hand hi. But exercise one, you have to put this is the link. You haven't put the answer, is it? So what's this one? So these are both difference between the exercise and test case. So they basically make up is made up of many of test cases. 

Test cases. To check the check the accesses right or error. So this exercise. Basically has got? This I'm not about the link. And you could have the expected output here, what do you think? So you are adding this one here because the difference, the difference seems to me is expected output. Was this input here? Was this input? What, me? 

And. 

It it is. 

Can you see that? This needs to be clear, yeah. I'm not sure what why you have exercise and we have test case. The sub is the sub the for this so is in. But the test input is here as well. No submission. I corrector type. This test input, is it from the user? 

The user, the test input, the exercise, The answer to the external given by the user is here. It is here. But I don't understand why you have the test case when in fact the exercise itself. One exercise, yes, it refers to the lesson, the problem statement, okay? And then you have unless, unless it is question one and then answers ABC, something like this, this test case, you have to think how you, how are you going to set the questions they exercise check users, a user, input, write or error, okay? But you could put it here and exercise. 

This exercise has birth, maybe? Maybe you're saying, give me an idea of an exercise, what does it look like? When you do an exercise for you set an exercise display, what does it look like? 

If. 

Find. 

Write your code here. Oh I see, so there of this exercise, write a single Python company, and then you analyze this code. How do you analyze the code? 

No, I use third-party API, okay? 

Siri, I see OK. 

So the old the exercises is about writing a small program sandbo technology. So basically, all the questions are about writing small code. Yes, yes, yes, okay. Now, so forget about the options then, not a DC. So let me let me look again at your diagram. So now I understand what you have done the. 

Okay, so what we have here, you have this exercise, right? This and so on, okay, and? This is a submission. What is a text that is submitted by the by the user here, the condition head code, where is it? Where is it store? What is stored the text or what, what is it? 

In database, yeah. But where is it in the idaga here? So what the code, you give me an exercise, I type the code, where do you store the code in the database here? 

Because we need to. Yeah, this is what you analyze. So what is this? This input text test? Input the? 

The that the user has answered or just standard code, you tell them this is how the program what the correct solution? Or is this the correct solution? When you say expected output, that's the result of the running the program. Yes, and this one test input, is it the code or is it exercise? So is this is basically the solution? An example of a solution? Yeah, it is the solution. Okay? So you have the text and you have the expected output when you run it, but where where you store the text of the us, the user when he types the code? 

It should be in the submission, shouldn't it? Must be because this is the interaction between the user, the learner, and the exercise in a.s. this learner, This user has typed this code for this exercise. Yeah, it. It is a so. 

When the three party API back, back, back. 

Yeah, but API, it's going to extract or it goes everything there. Can you see the point? Because you need the code. Either you extract it and you give it to the API, pass it to the IPA, is that what happens? Or the whole thing, the whole interface is the API. 

Show me the exercise, the example you gave me. Show me that, you know the exercise, the API, when it that it? Show me this one here, this is yours, this is the exercise. You type this, yes, and you write your code here, you type this. So when they type the code, how does it go? You extract it? Or is this API the whole thing is the API? I. Didn't conduct ATI? Now you have to think therefore, how this code here that you type has got to be stored somewhere, yes? 

And even if you can, okay, you print this, yes? 

So this is not connected. 

Right, okay, so this is an A is a test case. Oh, test case, test pass. So this is the output really what it should be when you write the code. But this is you type this, yes? Print hello. You type this. And the test case here is the output from the API. Test is the test is in database I. Have. Transfer tests and users code to API. Okay, so the idea. So, but this interface here is used. You've written it yourself. 

In Python, a Python API. I don't conduct conduct the third party API, okay? All right, I think, yeah, I think you need to sort this out, you see the point iss's? Can we get back to your database? This needs to be clear. Yes, because there is no record of this. Get back to the exercise. Now let's look at the API. There is no record of this is that you don't keep record of this, this is the code, where is it stored? Yeah, I don't, I will record this. Yeah, how do you know how they doing? Can you see the point? Because then then basically it doesn't. Of. Work hard on it to. 

So what is this submission? What is submission is the code. What is the submission? Where user make make a submission for? Let's get back to the API to. This is the submission. But you don't have the code. I mean, there could be any submission. You don't know which one is which. Can you see the point? I will use user make submission for access we can. So you know exactly what was submitted, what? Yeah, then, then this makes sense. 

A story? Story in this, but I don't see it. Where is it in the data? Let's go back. Where is it exactly what you store it? He said you will store it, I think you should be here if you're going to keep track of the code and you have this, we must know what is the code because this is the exercise, this is the code submission, and you give them the feedback. 

It sorry, correct error, yeah, okay. Five minutes. I'll call you one second. It is it 1? A few minutes, please. Okay, so yeah, this is it, so you need to this out, so you know, what is the answer to this exercise? And you can otherwise, yes, it could be anything, yes, can you see the point therefore, right? So you need to work on this one here so you can analyze it. It's coming along. But what is the teaching? You have the teaching material, this is the code, you type the code, what is the teaching material? This is the lesson, yes, the these are the, so you have to the lessons, yeah, what is this information? Is it it, where is it in? 

Your probably so is text. To. Yeah, so you can at least learn something, all right, okay, okay. So anyway, it's good. At least you've got something that's working here, but it's got to be consistent because then you can analyze what's happening and so on I. Want? Yeah, the other thing, maybe you could use it ChatGPT as well to see how to analyze the code. So you have three methods of doing the instead of a single API, you use few methods and you can compare and see which one is better so. 

Next, hopefully will have this clear and we have a little bit more comprehensive and sophisticated questions. All right, any questions? All right, we'll talk to you then next week, yeah? So it's coming, but we have, okay. All right, right? 
麦克风录音（2025-07-22 11_19_17）_原文
2025年07月22日 20:38
Good morning? 

Our discussion? Folks has been my Er? So we have? 

Here, the exercises. So this is basically the submission. So what you changed, what's this? Oh, the feedback. Feedback what? This here? Feedback, feedback. This is the core system job is. To measure the feedback was actually useful. The student gets a piece of feedback. I records how many times, what times they tried, okay? So basically this is monitoring the performance of the student. Yes, okay, you consider that, you consider that, right? So only doing this. You mean for the next exercises? Yes, for the next exercises, okay. That. 

This model user use program. Emission, what's this case? Test cases. The table hold the secret test. The institutions answer to key to automatically grade students code. Write it again. Old test. What do you mean by secret tests you? Some test case is is heading? Yeah, oh heathen. Oh heathen. You mean never heat instead of secret food? Heathen, in other words, because they decided to hide. Yes, yeah, okay, heathen Texas on. So basically it is the answer, but but the code can, the code can be anything, you know, the code, the way you write the code, it depends on the style as well. Does it know? 

There is a standard. Prototype? This is your protype. How it works? 

You is your. AC? Any. The. 

This is first pass program. You can, you can. It waves around. 

That it is a complexity as well? So can something? Something more complex. Yeah I. Have, you can you can you, can you have OpenAI control and this and this sector is. Okay, what does it teach? How do you specify what you teach? So if tutor, you tell it what to you want to learn or how does it work? Is. You use the accesses if you run. The open total and this total and can can teach, teach, teach you, Yes, but how does it know what to teach? 

Okay, how does it know what it is, what does it get? Because it can be anything. Learning Python is huge. How does it learn what to teach? You say it's a tutor or is it separate? Is it linked to the program that you are trying to write or is it separate? Yeah, this will explain your called Z so that that that OpenAI is going to look at the code. Yeah. Okay, that you print, can you write something else and see how can you write another program? Okay, right program. Say that to add a function that adds two numbers. A method for two numbers. 

For what? What loop? This access is print whole world. Can you change it? Does it mean it works only for Hello world? So how can do exercise whatever were 2? So create, how can you create an exercise? Forget about create an exercise called exercise to occur nice. 

So that's what it does world. 

You can, can you create what is, can you create another exercise? Come create another exercise. How you create another exercise? 

Because you have exercise 1 here. How did you generate this? How did you get this from exercise? How did you create this? 

If this is. 

This is my idea, I know that, but you've got one. I don't think it's going to be acceptable. One single program you want to teach test, that's fine, but it's working only for one program. What are the exercises you say, the feedback and so on? What are the exercises that if I want to create another exercise, can you show me how to do that? 

And, You don't know. So how did you get exercise 1? Show me where, where, where is exercise one coming from? 

One, 1 reason have one accesses. Yeah, but. You're to, how did you create this one? This one here, remover, How did you create this one? What is the code for it? Is it Python code? Have you done, did you do the work yourself? Yeah, so where is this coming from? Is there a code for this? Did you write it in Python? Show me the code for this. 

And this is called? Code? What is her? What is exercise 1? 

With the? 

Exercise There is exercise too. How do I get? Okay? You've got moved down a little bit. So you have exercise 1 and exercise 2. Yes, 1, is it 1, 2, 2? How can have access? So you have to write them in, they are coded, that's how you create exercises. Yeah? This is a fixed base setup. Which database? Set up database. So set up database. So how do we access to, how do we get access to, how can I see it in your system? 

You need? A admin? Okay, let's have a look at the FB code. 

In the model. Model magical. Listen, you can. You can create the new, new vision, a new lesson, and you can add new exercises. Okay, so can you create new lesson? It now? I don't complete this now. Okay, so, so you create, but the exercise to see, so this one, this code, can I move up? Okay, this is another Python learning platform. So you're going to create, this one is coming from the code only exercise 1. Yeah, why not exercise too? It's there, it's in the code. 

Can you see you've got to demonstrate clearly? Yes, you have to show that you can create how you create exercises or lessons. So you have to create a lesson, and with a lesson, there are exercises. Oh, but I just, I just write it in database, I don't complete. My prototype? And you need to. Demonstrate you see, when it comes to the, you have to show them how you create the lessons, Yes, and then what is the link between this, this lesson and OpenAI in the code? How do you, how do you jump from one to the other? Open, open, I need to. 

Evolution your evaluate your your code, evaluate your code, okay, but how does it do it? Is it in the, is it already in your system? Have you written this because you showed me OpenAI? Open, show me. Can you show me the code, And I use. 

I use the open open root API and short 0 API. Make sure it's in English because they can look at the code. You got to look at the code, and then they can check. So it's in English. It should be common, should be in English. So what is this? 

I open root it. It has the user's code, to open, OpenAI, See, okay, so this is the, what's the code is represented as, what is a text? It's a group of text code here, What is it stand for? What what is he stored in code? This is just text. 

What is it exactly? Is it text? If it uses code, is it text? How is it? What's in store? Can you show me the type? Is it an object? Code is an object. 

Show me the code. Where is defined? What is code defined? Because you pass it as a parameter here. So what is it used? 

Code here, code here. So what's it means, what? 

It's a context. Okay, you know you need to, You know, you have, when they ask you questions, you have to explain clearly what you're doing, yeah? Yeah, you have to. Yeah, so what I suggest is that so next week you continue with this and next you explain to me, show me how it works. Yeah, clearly, so I still don't see what the code is. We have messages, yes, but I don't see what the code is, what it stands for, okay? Yeah, do you see it yourself? You should know about this one because you wrote it. 

AI feedback in feedback it, yeah, but it's not feedback. You have to put the code. So the code, let's get back to your program. Now, the interface, this is the code you write here. Is this what's stored in code or what is stored there, Yeah? Right? Users called. Where is it in the database? Do you store user code? You don't store it. Submission. Submission code, Okay, yeah, and so it's text. So it's text, yes, you, yeah. And one and one code have 1 and one feedback it, okay? Yeah, so I suggest you continue working on this 1, yes, but next week we'll have a demonstration, you show me how you link the database and how we store the information, me. 

Okay, yeah, okay. Yeah, that's important because this is some, that's what you're going to do when it comes to the inspection. They're going to. Alright, okay, okay, fine. Anyway, you continue with this, make sure it's clear. And when you demonstrate, the code has to be in English so that they can read the comments if they want. 

Okay? All right, okay, okay. So next week you give me a demo and you explain what's happening. Yes, oh, okay, okay, okay, okay, okay, okay. We'll talk to you next week, okay? 

Bye bye. Okay, bye bye. 

Oh, we only. 
